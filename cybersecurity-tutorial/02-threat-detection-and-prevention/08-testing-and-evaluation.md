# Testing and Evaluation of Threat Detection Systems

Testing and evaluation are crucial aspects of developing effective threat detection systems. Without rigorous testing, we can't be confident that our systems will detect real threats or avoid false positives in production environments. In this section, we'll explore methodologies and tools for testing and evaluating our Rust-based threat detection components.

## Overview

We'll cover:

1. Building a test framework for threat detection
2. Creating and using synthetic threat samples
3. Measuring detection effectiveness
4. Performance benchmarking
5. Continuous evaluation and improvement
6. Red teaming and adversarial testing

## Building a Test Framework

Let's start by creating a test framework that allows us to evaluate all aspects of our threat detection system:

```rust
use std::collections::HashMap;
use std::path::Path;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use serde::{Serialize, Deserialize};

// Import components from previous modules
use crate::signature_detection::SignatureDetector;
use crate::heuristic_analysis::HeuristicEngine;
use crate::behavioral_analysis::BehavioralMonitor;
use crate::machine_learning::AnomalyDetector;
use crate::alert_management::Alert<PERSON>anager;
use crate::automated_response::ResponseEngine;

#[derive(Debug, <PERSON><PERSON>, Ser<PERSON>ize, Deserialize)]
pub struct TestResult {
    pub test_id: String,
    pub test_name: String,
    pub threat_type: String,
    pub detected: bool,
    pub detection_method: Option<String>,
    pub detection_time_ms: u64,
    pub false_positive: bool,
    pub notes: String,
}

#[derive(Debug)]
pub struct DetectionTestFramework {
    signature_detector: Arc<SignatureDetector>,
    heuristic_engine: Arc<HeuristicEngine>,
    behavioral_monitor: Arc<BehavioralMonitor>,
    anomaly_detector: Arc<AnomalyDetector>,
    alert_manager: Arc<Mutex<AlertManager>>,
    response_engine: Arc<Mutex<ResponseEngine>>,
    results: Arc<Mutex<Vec<TestResult>>>,
}

impl DetectionTestFramework {
    pub fn new(
        signature_detector: Arc<SignatureDetector>,
        heuristic_engine: Arc<HeuristicEngine>,
        behavioral_monitor: Arc<BehavioralMonitor>,
        anomaly_detector: Arc<AnomalyDetector>,
        alert_manager: Arc<Mutex<AlertManager>>,
        response_engine: Arc<Mutex<ResponseEngine>>,
    ) -> Self {
        DetectionTestFramework {
            signature_detector,
            heuristic_engine,
            behavioral_monitor,
            anomaly_detector,
            alert_manager,
            response_engine,
            results: Arc::new(Mutex::new(Vec::new())),
        }
    }
    
    pub fn run_test(&self, test_case: &TestCase) -> TestResult {
        println!("Running test: {}", test_case.name);
        let start_time = Instant::now();
        let mut detection_method = None;
        let mut detected = false;
        
        // Test signature detection
        if let Some(ref sample_data) = test_case.sample_data {
            if self.signature_detector.check_for_threats(sample_data) {
                detected = true;
                detection_method = Some("Signature Detection".to_string());
            }
        }
        
        // Test heuristic analysis if not detected yet
        if !detected && test_case.heuristic_indicators.len() > 0 {
            if self.heuristic_engine.analyze(&test_case.heuristic_indicators) {
                detected = true;
                detection_method = Some("Heuristic Analysis".to_string());
            }
        }
        
        // Test behavioral detection if not detected yet
        if !detected && test_case.behavioral_data.len() > 0 {
            if self.behavioral_monitor.detect_anomalies(&test_case.behavioral_data) {
                detected = true;
                detection_method = Some("Behavioral Analysis".to_string());
            }
        }
        
        // Test ML-based detection if not detected yet
        if !detected && test_case.feature_vector.len() > 0 {
            if self.anomaly_detector.predict(&test_case.feature_vector) > 0.8 {
                detected = true;
                detection_method = Some("Machine Learning".to_string());
            }
        }
        
        let elapsed = start_time.elapsed().as_millis() as u64;
        let should_detect = test_case.is_malicious;
        let false_positive = detected && !should_detect;
        let false_negative = !detected && should_detect;
        
        let result = TestResult {
            test_id: test_case.id.clone(),
            test_name: test_case.name.clone(),
            threat_type: test_case.threat_type.clone(),
            detected,
            detection_method,
            detection_time_ms: elapsed,
            false_positive,
            notes: if false_positive {
                "False positive detected".to_string()
            } else if false_negative {
                "Failed to detect threat".to_string()
            } else {
                "Test passed successfully".to_string()
            },
        };
        
        // Store the result
        self.results.lock().unwrap().push(result.clone());
        result
    }
    
    pub fn get_results(&self) -> Vec<TestResult> {
        self.results.lock().unwrap().clone()
    }
    
    pub fn calculate_metrics(&self) -> TestMetrics {
        let results = self.results.lock().unwrap();
        let total = results.len();
        
        if total == 0 {
            return TestMetrics::default();
        }
        
        let malicious_tests = results.iter()
            .filter(|r| r.threat_type != "benign")
            .count();
            
        let benign_tests = total - malicious_tests;
        
        let true_positives = results.iter()
            .filter(|r| r.threat_type != "benign" && r.detected)
            .count();
            
        let false_positives = results.iter()
            .filter(|r| r.threat_type == "benign" && r.detected)
            .count();
            
        let true_negatives = results.iter()
            .filter(|r| r.threat_type == "benign" && !r.detected)
            .count();
            
        let false_negatives = results.iter()
            .filter(|r| r.threat_type != "benign" && !r.detected)
            .count();
        
        let detection_rate = if malicious_tests > 0 {
            true_positives as f64 / malicious_tests as f64
        } else {
            0.0
        };
        
        let false_positive_rate = if benign_tests > 0 {
            false_positives as f64 / benign_tests as f64
        } else {
            0.0
        };
        
        let precision = if true_positives + false_positives > 0 {
            true_positives as f64 / (true_positives + false_positives) as f64
        } else {
            0.0
        };
        
        let average_detection_time = results.iter()
            .map(|r| r.detection_time_ms)
            .sum::<u64>() as f64 / total as f64;
        
        TestMetrics {
            total_tests: total,
            true_positives,
            false_positives,
            true_negatives,
            false_negatives,
            detection_rate,
            false_positive_rate,
            precision,
            average_detection_time_ms: average_detection_time,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestCase {
    pub id: String,
    pub name: String,
    pub description: String,
    pub threat_type: String,
    pub is_malicious: bool,
    pub sample_data: Option<Vec<u8>>,
    pub heuristic_indicators: HashMap<String, f64>,
    pub behavioral_data: Vec<BehavioralEvent>,
    pub feature_vector: Vec<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct TestMetrics {
    pub total_tests: usize,
    pub true_positives: usize,
    pub false_positives: usize,
    pub true_negatives: usize,
    pub false_negatives: usize,
    pub detection_rate: f64,
    pub false_positive_rate: f64,
    pub precision: f64,
    pub average_detection_time_ms: f64,
}

// Simplified behavioral event for testing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BehavioralEvent {
    pub event_type: String,
    pub process_name: String,
    pub timestamp: u64,
    pub details: HashMap<String, String>,
}
```

## Creating and Using Synthetic Threat Samples

For effective testing, we need both benign samples and threat samples. Let's create a test dataset manager:

```rust
use std::fs;
use std::path::PathBuf;
use rand::{Rng, thread_rng};

#[derive(Debug)]
pub struct TestDatasetManager {
    dataset_path: PathBuf,
    benign_samples: Vec<PathBuf>,
    malicious_samples: HashMap<String, Vec<PathBuf>>, // Category -> samples
}

impl TestDatasetManager {
    pub fn new<P: AsRef<Path>>(dataset_path: P) -> Result<Self, std::io::Error> {
        let path = dataset_path.as_ref().to_path_buf();
        
        if !path.exists() {
            fs::create_dir_all(&path)?;
        }
        
        let mut manager = TestDatasetManager {
            dataset_path: path,
            benign_samples: Vec::new(),
            malicious_samples: HashMap::new(),
        };
        
        manager.scan_datasets()?;
        Ok(manager)
    }
    
    fn scan_datasets(&mut self) -> Result<(), std::io::Error> {
        // Scan benign directory
        let benign_path = self.dataset_path.join("benign");
        if benign_path.exists() {
            for entry in fs::read_dir(benign_path)? {
                let entry = entry?;
                let path = entry.path();
                if path.is_file() {
                    self.benign_samples.push(path);
                }
            }
        }
        
        // Scan malicious categories
        let malicious_path = self.dataset_path.join("malicious");
        if malicious_path.exists() {
            for category_entry in fs::read_dir(malicious_path)? {
                let category_entry = category_entry?;
                let category_path = category_entry.path();
                
                if category_path.is_dir() {
                    let category_name = category_path.file_name()
                        .unwrap_or_default()
                        .to_string_lossy()
                        .to_string();
                        
                    let mut samples = Vec::new();
                    
                    for sample_entry in fs::read_dir(&category_path)? {
                        let sample_entry = sample_entry?;
                        let sample_path = sample_entry.path();
                        
                        if sample_path.is_file() {
                            samples.push(sample_path);
                        }
                    }
                    
                    self.malicious_samples.insert(category_name, samples);
                }
            }
        }
        
        Ok(())
    }
    
    pub fn create_synthetic_test_case(&self, malicious: bool) -> TestCase {
        let mut rng = thread_rng();
        
        if malicious {
            // Select a random malware category
            if self.malicious_samples.is_empty() {
                return self.generate_synthetic_malicious_test_case();
            }
            
            let categories: Vec<_> = self.malicious_samples.keys().collect();
            let category = categories[rng.gen_range(0..categories.len())];
            
            let samples = &self.malicious_samples[category];
            if samples.is_empty() {
                return self.generate_synthetic_malicious_test_case();
            }
            
            let sample_path = &samples[rng.gen_range(0..samples.len())];
            
            // Try to read the sample
            match fs::read(sample_path) {
                Ok(data) => {
                    return TestCase {
                        id: uuid::Uuid::new_v4().to_string(),
                        name: format!("Malicious test - {}", category),
                        description: format!("Test with real {} sample", category),
                        threat_type: category.clone(),
                        is_malicious: true,
                        sample_data: Some(data),
                        heuristic_indicators: self.generate_malicious_heuristics(category),
                        behavioral_data: self.generate_malicious_behavior(category),
                        feature_vector: self.generate_malicious_features(category),
                    }
                },
                Err(_) => {
                    return self.generate_synthetic_malicious_test_case();
                }
            }
        } else {
            // Generate benign test case
            if self.benign_samples.is_empty() {
                return self.generate_synthetic_benign_test_case();
            }
            
            let sample_path = &self.benign_samples[rng.gen_range(0..self.benign_samples.len())];
            
            // Try to read the sample
            match fs::read(sample_path) {
                Ok(data) => {
                    return TestCase {
                        id: uuid::Uuid::new_v4().to_string(),
                        name: "Benign test case".to_string(),
                        description: "Test with real benign sample".to_string(),
                        threat_type: "benign".to_string(),
                        is_malicious: false,
                        sample_data: Some(data),
                        heuristic_indicators: self.generate_benign_heuristics(),
                        behavioral_data: self.generate_benign_behavior(),
                        feature_vector: self.generate_benign_features(),
                    }
                },
                Err(_) => {
                    return self.generate_synthetic_benign_test_case();
                }
            }
        }
    }
    
    // Helper methods to generate synthetic test data
    fn generate_synthetic_malicious_test_case(&self) -> TestCase {
        let mut rng = thread_rng();
        let threat_types = ["ransomware", "trojan", "worm", "rootkit", "spyware"];
        let threat_type = threat_types[rng.gen_range(0..threat_types.len())].to_string();
        
        TestCase {
            id: uuid::Uuid::new_v4().to_string(),
            name: format!("Synthetic {} test", threat_type),
            description: format!("Synthetically generated {} test case", threat_type),
            threat_type: threat_type.clone(),
            is_malicious: true,
            sample_data: Some(self.generate_synthetic_malware_bytes(&threat_type)),
            heuristic_indicators: self.generate_malicious_heuristics(&threat_type),
            behavioral_data: self.generate_malicious_behavior(&threat_type),
            feature_vector: self.generate_malicious_features(&threat_type),
        }
    }
    
    fn generate_synthetic_benign_test_case(&self) -> TestCase {
        TestCase {
            id: uuid::Uuid::new_v4().to_string(),
            name: "Synthetic benign test".to_string(),
            description: "Synthetically generated benign test case".to_string(),
            threat_type: "benign".to_string(),
            is_malicious: false,
            sample_data: Some(self.generate_synthetic_benign_bytes()),
            heuristic_indicators: self.generate_benign_heuristics(),
            behavioral_data: self.generate_benign_behavior(),
            feature_vector: self.generate_benign_features(),
        }
    }
    
    // Implementation details for generating synthetic test data...
    // (these methods would generate appropriate synthetic data for each test case type)
}
```

## Measuring Detection Effectiveness

To evaluate our threat detection system properly, we need comprehensive metrics:

```rust
use std::time::Instant;
use plotters::prelude::*;

#[derive(Debug, Clone)]
pub struct EvaluationSuite {
    test_framework: Arc<DetectionTestFramework>,
    dataset_manager: Arc<TestDatasetManager>,
    results_path: PathBuf,
}

impl EvaluationSuite {
    pub fn new(
        test_framework: Arc<DetectionTestFramework>,
        dataset_manager: Arc<TestDatasetManager>,
        results_path: &Path,
    ) -> Result<Self, std::io::Error> {
        if !results_path.exists() {
            fs::create_dir_all(results_path)?;
        }
        
        Ok(EvaluationSuite {
            test_framework,
            dataset_manager,
            results_path: results_path.to_path_buf(),
        })
    }
    
    pub fn run_evaluation(&self, num_benign: usize, num_malicious: usize) -> TestMetrics {
        println!("Starting evaluation with {} benign and {} malicious samples", 
            num_benign, num_malicious);
            
        let start_time = Instant::now();
        
        // Run benign tests
        for i in 0..num_benign {
            let test_case = self.dataset_manager.create_synthetic_test_case(false);
            self.test_framework.run_test(&test_case);
            
            if (i + 1) % 10 == 0 {
                println!("Completed {}/{} benign tests", i + 1, num_benign);
            }
        }
        
        // Run malicious tests
        for i in 0..num_malicious {
            let test_case = self.dataset_manager.create_synthetic_test_case(true);
            self.test_framework.run_test(&test_case);
            
            if (i + 1) % 10 == 0 {
                println!("Completed {}/{} malicious tests", i + 1, num_malicious);
            }
        }
        
        let total_time = start_time.elapsed();
        println!("Evaluation completed in {:.2} seconds", total_time.as_secs_f64());
        
        // Calculate metrics
        let metrics = self.test_framework.calculate_metrics();
        self.save_metrics_report(&metrics)?;
        self.generate_metric_visualizations(&metrics)?;
        
        metrics
    }
    
    fn save_metrics_report(&self, metrics: &TestMetrics) -> Result<(), std::io::Error> {
        let report_path = self.results_path.join("evaluation_report.json");
        let report_json = serde_json::to_string_pretty(metrics)?;
        fs::write(report_path, report_json)?;
        
        // Also save a human-readable version
        let report_txt_path = self.results_path.join("evaluation_report.txt");
        let report_txt = format!(
            "Threat Detection System Evaluation Report\n\
             -------------------------------------\n\
             Total tests:           {}\n\
             True positives:        {}\n\
             False positives:       {}\n\
             True negatives:        {}\n\
             False negatives:       {}\n\
             \n\
             Detection rate:        {:.2}%\n\
             False positive rate:   {:.2}%\n\
             Precision:             {:.2}%\n\
             Average detection time: {:.2} ms\n\
             \n\
             F1 Score:              {:.3}\n\
             Accuracy:              {:.3}",
            metrics.total_tests,
            metrics.true_positives,
            metrics.false_positives,
            metrics.true_negatives,
            metrics.false_negatives,
            metrics.detection_rate * 100.0,
            metrics.false_positive_rate * 100.0,
            metrics.precision * 100.0,
            metrics.average_detection_time_ms,
            self.calculate_f1_score(metrics),
            self.calculate_accuracy(metrics)
        );
        
        fs::write(report_txt_path, report_txt)?;
        Ok(())
    }
    
    fn calculate_f1_score(&self, metrics: &TestMetrics) -> f64 {
        let precision = metrics.precision;
        let recall = if metrics.true_positives + metrics.false_negatives > 0 {
            metrics.true_positives as f64 / 
            (metrics.true_positives + metrics.false_negatives) as f64
        } else {
            0.0
        };
        
        if precision + recall > 0.0 {
            2.0 * (precision * recall) / (precision + recall)
        } else {
            0.0
        }
    }
    
    fn calculate_accuracy(&self, metrics: &TestMetrics) -> f64 {
        let total = metrics.true_positives + metrics.true_negatives + 
                   metrics.false_positives + metrics.false_negatives;
        
        if total > 0 {
            (metrics.true_positives + metrics.true_negatives) as f64 / total as f64
        } else {
            0.0
        }
    }
    
    // Generate visualizations of metrics using plotters crate
    fn generate_metric_visualizations(&self, metrics: &TestMetrics) -> Result<(), Box<dyn std::error::Error>> {
        // Implementation for creating charts and graphs of the results
        // This would use the plotters crate to create confusion matrix,
        // ROC curves, and other visualization
        
        Ok(())
    }
}
```

## Performance Benchmarking

For threat detection solutions, performance is crucial - systems must detect threats quickly without consuming excessive resources:

```rust
use std::time::Instant;
use std::thread;
use std::sync::atomic::{AtomicUsize, Ordering};

#[derive(Debug)]
pub struct PerformanceBenchmark {
    test_framework: Arc<DetectionTestFramework>,
    dataset_manager: Arc<TestDatasetManager>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BenchmarkResult {
    pub throughput_samples_per_second: f64,
    pub average_detection_time_ms: f64,
    pub memory_usage_mb: f64,
    pub cpu_usage_percent: f64,
    pub detection_rate: f64,
    pub false_positive_rate: f64,
}

impl PerformanceBenchmark {
    pub fn new(
        test_framework: Arc<DetectionTestFramework>,
        dataset_manager: Arc<TestDatasetManager>,
    ) -> Self {
        PerformanceBenchmark {
            test_framework,
            dataset_manager,
        }
    }
    
    pub fn run_benchmark(&self, num_samples: usize, parallel_threads: usize) -> BenchmarkResult {
        println!("Running performance benchmark with {} samples on {} threads", 
            num_samples, parallel_threads);
        
        let start_time = Instant::now();
        let processed = Arc::new(AtomicUsize::new(0));
        let detection_time_sum = Arc::new(AtomicUsize::new(0));
        
        // Create test cases up front
        let mut test_cases = Vec::with_capacity(num_samples);
        for i in 0..num_samples {
            // Mix of benign and malicious samples (70% benign, 30% malicious)
            let is_malicious = i % 10 < 3;
            test_cases.push(self.dataset_manager.create_synthetic_test_case(is_malicious));
        }
        
        // Processing function
        let process_batch = |batch: Vec<TestCase>, 
                             processed: Arc<AtomicUsize>,
                             time_sum: Arc<AtomicUsize>| {
            for test_case in batch {
                let result = self.test_framework.run_test(&test_case);
                time_sum.fetch_add(result.detection_time_ms as usize, Ordering::Relaxed);
                processed.fetch_add(1, Ordering::SeqCst);
            }
        };
        
        // Split test cases into batches for threads
        let mut handles = vec![];
        let batch_size = (num_samples + parallel_threads - 1) / parallel_threads;
        
        for i in 0..parallel_threads {
            let start_idx = i * batch_size;
            let end_idx = std::cmp::min(start_idx + batch_size, num_samples);
            
            if start_idx >= end_idx {
                continue;
            }
            
            let batch = test_cases[start_idx..end_idx].to_vec();
            let processed_clone = processed.clone();
            let time_sum_clone = detection_time_sum.clone();
            
            let handle = thread::spawn(move || {
                process_batch(batch, processed_clone, time_sum_clone);
            });
            
            handles.push(handle);
        }
        
        // Monitor progress
        let processed_clone = processed.clone();
        let monitoring_handle = thread::spawn(move || {
            loop {
                thread::sleep(std::time::Duration::from_secs(2));
                let current = processed_clone.load(Ordering::SeqCst);
                println!("Processed {}/{} samples", current, num_samples);
                
                if current >= num_samples {
                    break;
                }
            }
        });
        
        // Wait for all processing to complete
        for handle in handles {
            handle.join().unwrap();
        }
        
        let elapsed_time = start_time.elapsed().as_secs_f64();
        let throughput = num_samples as f64 / elapsed_time;
        
        let avg_detection_time = if num_samples > 0 {
            detection_time_sum.load(Ordering::Relaxed) as f64 / num_samples as f64
        } else {
            0.0
        };
        
        // Get the rest of the metrics from the test framework
        let metrics = self.test_framework.calculate_metrics();
        
        // Allow the monitoring thread to exit
        monitoring_handle.join().unwrap();
        
        // Memory and CPU measurements would require OS-specific solutions
        // Here we're using placeholder values
        BenchmarkResult {
            throughput_samples_per_second: throughput,
            average_detection_time_ms: avg_detection_time,
            memory_usage_mb: 0.0, // Would require actual measurement
            cpu_usage_percent: 0.0, // Would require actual measurement
            detection_rate: metrics.detection_rate,
            false_positive_rate: metrics.false_positive_rate,
        }
    }
}
```

## Continuous Evaluation and Improvement

Threat detection systems must continuously evolve to remain effective against new threats:

```rust
use chrono::{DateTime, Utc};
use std::sync::mpsc::{channel, Receiver, Sender};

#[derive(Debug)]
pub struct ContinuousEvaluation {
    evaluation_suite: Arc<EvaluationSuite>,
    performance_benchmark: Arc<PerformanceBenchmark>,
    history: Arc<Mutex<Vec<EvaluationRecord>>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EvaluationRecord {
    pub timestamp: DateTime<Utc>,
    pub version: String,
    pub metrics: TestMetrics,
    pub benchmark: Option<BenchmarkResult>,
    pub improvement_notes: String,
}

impl ContinuousEvaluation {
    pub fn new(
        evaluation_suite: Arc<EvaluationSuite>,
        performance_benchmark: Arc<PerformanceBenchmark>,
    ) -> Self {
        ContinuousEvaluation {
            evaluation_suite,
            performance_benchmark,
            history: Arc::new(Mutex::new(Vec::new())),
        }
    }
    
    pub fn load_history<P: AsRef<Path>>(&self, path: P) -> Result<(), std::io::Error> {
        if !path.as_ref().exists() {
            return Ok(());
        }
        
        let data = fs::read_to_string(path)?;
        let records: Vec<EvaluationRecord> = serde_json::from_str(&data)?;
        
        let mut history = self.history.lock().unwrap();
        *history = records;
        
        Ok(())
    }
    
    pub fn save_history<P: AsRef<Path>>(&self, path: P) -> Result<(), std::io::Error> {
        let history = self.history.lock().unwrap();
        let data = serde_json::to_string_pretty(&*history)?;
        fs::write(path, data)?;
        Ok(())
    }
    
    pub fn run_scheduled_evaluation(&self, version: &str) -> EvaluationRecord {
        // Run full evaluation
        println!("Running scheduled evaluation for version {}", version);
        let metrics = self.evaluation_suite.run_evaluation(100, 100);
        
        // Run performance benchmark
        println!("Running performance benchmark");
        let benchmark = self.performance_benchmark.run_benchmark(50, 4);
        
        let record = EvaluationRecord {
            timestamp: Utc::now(),
            version: version.to_string(),
            metrics,
            benchmark: Some(benchmark),
            improvement_notes: String::new(),
        };
        
        // Add to history
        self.history.lock().unwrap().push(record.clone());
        
        // Compare with previous results
        self.analyze_trends();
        
        record
    }
    
    pub fn analyze_trends(&self) {
        let history = self.history.lock().unwrap();
        
        if history.len() < 2 {
            println!("Not enough historical data for trend analysis");
            return;
        }
        
        let latest = &history[history.len() - 1];
        let previous = &history[history.len() - 2];
        
        println!("Trend Analysis:");
        println!("  Detection rate: {:.2}% -> {:.2}% ({:+.2}%)", 
            previous.metrics.detection_rate * 100.0,
            latest.metrics.detection_rate * 100.0,
            (latest.metrics.detection_rate - previous.metrics.detection_rate) * 100.0);
            
        println!("  False positive rate: {:.2}% -> {:.2}% ({:+.2}%)",
            previous.metrics.false_positive_rate * 100.0,
            latest.metrics.false_positive_rate * 100.0,
            (latest.metrics.false_positive_rate - previous.metrics.false_positive_rate) * 100.0);
            
        if let (Some(prev_bench), Some(latest_bench)) = (&previous.benchmark, &latest.benchmark) {
            println!("  Performance: {:.2} -> {:.2} samples/sec ({:+.2}%)",
                prev_bench.throughput_samples_per_second,
                latest_bench.throughput_samples_per_second,
                (latest_bench.throughput_samples_per_second - prev_bench.throughput_samples_per_second) / 
                    prev_bench.throughput_samples_per_second * 100.0);
        }
    }
}
```

## Red Teaming and Adversarial Testing

Red teaming involves simulating realistic attacks to test detection systems:

```rust
#[derive(Debug)]
pub struct RedTeam {
    test_framework: Arc<DetectionTestFramework>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RedTeamScenario {
    pub name: String,
    pub description: String,
    pub attack_stages: Vec<AttackStage>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttackStage {
    pub name: String,
    pub description: String,
    pub tactics: Vec<String>,
    pub techniques: Vec<String>,
    pub test_cases: Vec<TestCase>,
}

impl RedTeam {
    pub fn new(test_framework: Arc<DetectionTestFramework>) -> Self {
        RedTeam {
            test_framework,
        }
    }
    
    pub fn run_red_team_scenario(&self, scenario: &RedTeamScenario) -> Vec<(AttackStage, Vec<TestResult>)> {
        println!("Running red team scenario: {}", scenario.name);
        println!("Description: {}", scenario.description);
        
        let mut results = Vec::new();
        
        for (i, stage) in scenario.attack_stages.iter().enumerate() {
            println!("\nStage {}: {}", i + 1, stage.name);
            println!("Description: {}", stage.description);
            println!("Tactics: {}", stage.tactics.join(", "));
            println!("Techniques: {}", stage.techniques.join(", "));
            
            let mut stage_results = Vec::new();
            
            for test_case in &stage.test_cases {
                println!("\nExecuting test: {}", test_case.name);
                let result = self.test_framework.run_test(test_case);
                
                if result.detected {
                    println!("Attack detected via: {}", 
                        result.detection_method.as_deref().unwrap_or("unknown"));
                    println!("Detection time: {} ms", result.detection_time_ms);
                } else {
                    println!("Attack NOT detected!");
                }
                
                stage_results.push(result);
            }
            
            // Calculate evasion rate for this stage
            let evasion_rate = stage_results.iter()
                .filter(|r| !r.detected)
                .count() as f64 / stage_results.len() as f64;
                
            println!("Stage {} evasion rate: {:.2}%", i + 1, evasion_rate * 100.0);
            
            results.push((stage.clone(), stage_results));
        }
        
        // Generate overall report
        let total_tests = results.iter()
            .map(|(_, results)| results.len())
            .sum::<usize>();
            
        let total_evaded = results.iter()
            .map(|(_, results)| results.iter().filter(|r| !r.detected).count())
            .sum::<usize>();
            
        let overall_evasion_rate = if total_tests > 0 {
            total_evaded as f64 / total_tests as f64
        } else {
            0.0
        };
        
        println!("\nRed Team Scenario Complete");
        println!("Overall evasion rate: {:.2}%", overall_evasion_rate * 100.0);
        
        results
    }
    
    pub fn create_adversarial_test_cases(&self, base_case: &TestCase, 
                                         num_variations: usize) -> Vec<TestCase> {
        let mut variations = Vec::with_capacity(num_variations);
        
        for i in 0..num_variations {
            // Create variations that attempt to evade detection while preserving malicious behavior
            let mut variant = base_case.clone();
            variant.id = uuid::Uuid::new_v4().to_string();
            variant.name = format!("{} - Adversarial Variant {}", base_case.name, i + 1);
            
            // Apply evasion techniques
            match i % 4 {
                0 => {
                    // Data obfuscation
                    if let Some(data) = &variant.sample_data {
                        let mut obfuscated = data.clone();
                        // Simple obfuscation: XOR with a key
                        for byte in obfuscated.iter_mut() {
                            *byte ^= 0x42;
                        }
                        variant.sample_data = Some(obfuscated);
                    }
                },
                1 => {
                    // Timing variation
                    if !variant.behavioral_data.is_empty() {
                        for event in &mut variant.behavioral_data {
                            // Add random delays between events
                            event.timestamp += thread_rng().gen_range(100..5000);
                        }
                    }
                },
                2 => {
                    // Split behavior into multiple phases
                    if !variant.behavioral_data.is_empty() {
                        // Add benign-looking behaviors between malicious actions
                        let benign_events = self.generate_benign_behavioral_events(3);
                        let mut new_sequence = Vec::new();
                        
                        for (i, event) in variant.behavioral_data.iter().enumerate() {
                            new_sequence.push(event.clone());
                            
                            if i < variant.behavioral_data.len() - 1 {
                                new_sequence.push(benign_events[i % benign_events.len()].clone());
                            }
                        }
                        
                        variant.behavioral_data = new_sequence;
                    }
                },
                3 => {
                    // Modify heuristic indicators to appear more benign
                    for (key, value) in variant.heuristic_indicators.iter_mut() {
                        if *value > 0.5 {
                            *value = 0.5 - (*value - 0.5) * 0.5; // Reduce the indicator
                        }
                    }
                    
                    // Add some benign indicators
                    let benign_indicators = self.generate_benign_heuristic_indicators();
                    for (key, value) in benign_indicators {
                        if !variant.heuristic_indicators.contains_key(&key) {
                            variant.heuristic_indicators.insert(key, value);
                        }
                    }
                },
                _ => {}
            }
            
            variations.push(variant);
        }
        
        variations
    }
    
    // Helper methods to generate benign behavior patterns
    fn generate_benign_behavioral_events(&self, count: usize) -> Vec<BehavioralEvent> {
        // Code to generate benign-looking events
        let mut events = Vec::with_capacity(count);
        let benign_process_names = ["explorer.exe", "chrome.exe", "firefox.exe", "code.exe", "outlook.exe"];
        let benign_event_types = ["FileRead", "NetworkConnection", "ProcessStart", "RegistryRead"];
        
        let mut rng = thread_rng();
        
        for _ in 0..count {
            let process_name = benign_process_names[rng.gen_range(0..benign_process_names.len())];
            let event_type = benign_event_types[rng.gen_range(0..benign_event_types.len())];
            
            let mut details = HashMap::new();
            match event_type {
                "FileRead" => {
                    details.insert("path".to_string(), 
                                   format!("C:\\Users\\<USER>\\Documents\\doc{}.txt", rng.gen_range(1..100)));
                    details.insert("size".to_string(), format!("{}", rng.gen_range(1024..10240)));
                },
                "NetworkConnection" => {
                    details.insert("destination".to_string(), 
                                   format!("192.168.1.{}", rng.gen_range(1..254)));
                    details.insert("port".to_string(), format!("{}", rng.gen_range(1024..65535)));
                    details.insert("protocol".to_string(), "TCP".to_string());
                },
                "ProcessStart" => {
                    details.insert("command_line".to_string(), 
                                   format!("\"C:\\Program Files\\App\\{}.exe\" --config default", 
                                          process_name.replace(".exe", "")));
                },
                "RegistryRead" => {
                    details.insert("key".to_string(), 
                                   format!("HKEY_CURRENT_USER\\Software\\App{}\\Settings", 
                                          rng.gen_range(1..10)));
                },
                _ => {}
            }
            
            events.push(BehavioralEvent {
                event_type: event_type.to_string(),
                process_name: process_name.to_string(),
                timestamp: rng.gen_range(1000000..2000000),
                details,
            });
        }
        
        events
    }
    
    fn generate_benign_heuristic_indicators(&self) -> HashMap<String, f64> {
        let mut indicators = HashMap::new();
        let benign_indicators = [
            "file_access_pattern",
            "memory_usage",
            "cpu_usage",
            "network_connection_rate",
            "registry_access_pattern",
        ];
        
        let mut rng = thread_rng();
        
        for indicator in benign_indicators {
            indicators.insert(indicator.to_string(), rng.gen_range(0.0..0.3));
        }
        
        indicators
    }
}
```

## Example Usage

Here's how you might use all these testing and evaluation components together:

```rust
fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize all detection components from previous modules
    let signature_detector = Arc::new(SignatureDetector::new());
    let heuristic_engine = Arc::new(HeuristicEngine::new());
    let behavioral_monitor = Arc::new(BehavioralMonitor::new());
    let anomaly_detector = Arc::new(AnomalyDetector::new());
    let alert_manager = Arc::new(Mutex::new(AlertManager::new()));
    let response_engine = Arc::new(Mutex::new(ResponseEngine::new()));
    
    // Create test framework
    let test_framework = Arc::new(DetectionTestFramework::new(
        signature_detector,
        heuristic_engine, 
        behavioral_monitor,
        anomaly_detector,
        alert_manager,
        response_engine,
    ));
    
    // Create dataset manager
    let dataset_path = Path::new("./test_datasets");
    let dataset_manager = Arc::new(TestDatasetManager::new(dataset_path)?);
    
    // Create evaluation suite
    let results_path = Path::new("./evaluation_results");
    let evaluation_suite = Arc::new(EvaluationSuite::new(
        test_framework.clone(),
        dataset_manager.clone(),
        results_path,
    )?);
    
    // Create performance benchmark
    let benchmark = Arc::new(PerformanceBenchmark::new(
        test_framework.clone(),
        dataset_manager.clone(),
    ));
    
    // Create continuous evaluation manager
    let continuous_eval = ContinuousEvaluation::new(
        evaluation_suite.clone(),
        benchmark.clone(),
    );
    
    // Create red team
    let red_team = RedTeam::new(test_framework.clone());
    
    println!("=== Basic Evaluation ===");
    let metrics = evaluation_suite.run_evaluation(20, 20);
    println!("Detection Rate: {:.2}%", metrics.detection_rate * 100.0);
    println!("False Positive Rate: {:.2}%", metrics.false_positive_rate * 100.0);
    
    println!("\n=== Performance Benchmark ===");
    let benchmark_result = benchmark.run_benchmark(50, 4);
    println!("Throughput: {:.2} samples/sec", benchmark_result.throughput_samples_per_second);
    println!("Average Detection Time: {:.2} ms", benchmark_result.average_detection_time_ms);
    
    println!("\n=== Red Team Testing ===");
    // Create a simple red team scenario
    let scenario = RedTeamScenario {
        name: "Data Exfiltration Attack".to_string(),
        description: "Simulates an attacker attempting to steal sensitive data".to_string(),
        attack_stages: vec![
            AttackStage {
                name: "Initial Access".to_string(),
                description: "Gaining initial access through phishing".to_string(),
                tactics: vec!["Initial Access".to_string()],
                techniques: vec!["Spearphishing Attachment".to_string()],
                test_cases: vec![dataset_manager.create_synthetic_test_case(true)],
            },
            AttackStage {
                name: "Data Collection".to_string(),
                description: "Locate and collect sensitive data".to_string(),
                tactics: vec!["Collection".to_string()],
                techniques: vec!["Data from Local System".to_string()],
                test_cases: vec![dataset_manager.create_synthetic_test_case(true)],
            },
            AttackStage {
                name: "Exfiltration".to_string(),
                description: "Send data to external server".to_string(),
                tactics: vec!["Exfiltration".to_string()],
                techniques: vec!["Exfiltration Over Alternative Protocol".to_string()],
                test_cases: vec![dataset_manager.create_synthetic_test_case(true)],
            },
        ],
    };
    
    red_team.run_red_team_scenario(&scenario);
    
    println!("\n=== All tests completed successfully ===");
    Ok(())
}
```

## Integration with CI/CD

For continuous integration, we can use a configuration file to define our test strategy:

```yaml
# test_config.yaml
version: "1.0"
test_suites:
  standard_evaluation:
    benign_samples: 100
    malicious_samples: 100
    categories:
      - ransomware
      - trojan
      - spyware
      - worm
      - rootkit
  
  performance_benchmark:
    num_samples: 200
    parallel_threads: 4
    iterations: 3

  red_team:
    scenarios:
      - name: "Data Exfiltration"
        stages: 3
      - name: "Ransomware Attack"
        stages: 4

schedule:
  nightly_run:
    test_suites:
      - standard_evaluation
      - performance_benchmark
  
  weekly_run:
    test_suites:
      - standard_evaluation
      - performance_benchmark
      - red_team
```

This YAML configuration can be used with a CI/CD pipeline to automate testing and evaluation of the threat detection system.

## Conclusion

Testing and evaluation are essential parts of building effective threat detection systems. With the framework we've developed in this module, you can:

1. Create comprehensive test cases for all detection components
2. Measure the effectiveness of your detection methods
3. Benchmark performance under various conditions
4. Continuously evaluate and improve your system
5. Simulate realistic attack scenarios through red team exercises

By incorporating these practices into your development workflow, you'll build more reliable and effective threat detection systems that can adapt to the evolving threat landscape.

## Next Steps

- Explore different evaluation methodologies for specific threat types
- Implement A/B testing for detection algorithm improvements
- Develop custom test cases for your specific security needs
- Integrate with threat intelligence feeds for realistic testing
- Consider adversarial machine learning techniques for more robust detection

---

🔗 **Previous**: [07-automated-responses.md](./07-automated-responses.md)

🔗 **Next**: [../03-network-security-tools/00-overview.md](../03-network-security-tools/00-overview.md)

## Quiz: Threat Detection
1. What is the difference between signature-based and behavioral detection?
2. Give an example of a heuristic rule.
3. Why is machine learning useful in threat detection?
