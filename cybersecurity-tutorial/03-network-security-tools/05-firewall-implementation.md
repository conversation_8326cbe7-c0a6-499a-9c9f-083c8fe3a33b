# Basic Firewall Functionality

A firewall is a security system that monitors and controls incoming and outgoing network traffic based on predetermined security rules. In this section, we'll implement a basic firewall in Rust, focusing on packet filtering and rule management.

## Overview

We'll cover:

1. Firewall concepts and types
2. Designing a rule-based packet filter
3. Implementing firewall rules in Rust
4. Stateful vs. stateless filtering
5. Logging and alerting for blocked packets
6. Integrating the firewall with other network tools

## Firewall Concepts and Types

Firewalls can be:
- **Stateless**: Each packet is evaluated independently
- **Stateful**: Tracks the state of network connections (e.g., TCP handshake)

Common firewall actions:
- **Allow**: Permit the packet
- **Block**: Drop the packet
- **Log**: Record the event for auditing

## Designing a Rule-Based Packet Filter

Let's define a structure for firewall rules:

```rust
use std::net::IpAddr;
use crate::network::models::{NetworkPacket, TransportProtocol};

#[derive(Debug, <PERSON>lone, PartialEq, Eq)]
pub enum FirewallAction {
    Allow,
    Block,
    Log,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct FirewallRule {
    pub name: String,
    pub src_ip: Option<IpAddr>,
    pub dst_ip: Option<IpAddr>,
    pub src_port: Option<u16>,
    pub dst_port: Option<u16>,
    pub protocol: Option<TransportProtocol>,
    pub action: FirewallAction,
    pub log: bool,
}

impl FirewallRule {
    pub fn matches(&self, packet: &NetworkPacket) -> bool {
        if let Some(ip) = self.src_ip {
            if packet.src_ip != Some(ip) { return false; }
        }
        if let Some(ip) = self.dst_ip {
            if packet.dst_ip != Some(ip) { return false; }
        }
        if let Some(port) = self.src_port {
            if packet.src_port != Some(port) { return false; }
        }
        if let Some(port) = self.dst_port {
            if packet.dst_port != Some(port) { return false; }
        }
        if let Some(proto) = self.protocol {
            if packet.protocol != proto { return false; }
        }
        true
    }
}
```

## Implementing the Firewall Engine

```rust
pub struct Firewall {
    pub rules: Vec<FirewallRule>,
}

impl Firewall {
    pub fn new() -> Self { Firewall { rules: Vec::new() } }
    pub fn add_rule(&mut self, rule: FirewallRule) { self.rules.push(rule); }
    pub fn check_packet(&self, packet: &NetworkPacket) -> FirewallAction {
        for rule in &self.rules {
            if rule.matches(packet) {
                if rule.log {
                    println!("[FIREWALL LOG] Matched rule '{}': {}", rule.name, packet.summary());
                }
                return rule.action.clone();
            }
        }
        FirewallAction::Allow // Default allow if no rule matches
    }
}
```

## Stateful vs. Stateless Filtering

A stateful firewall tracks connection state (e.g., TCP handshake):

```rust
use std::collections::HashMap;
use std::time::SystemTime;

#[derive(Debug, Clone)]
pub struct ConnectionState {
    pub src_ip: IpAddr,
    pub dst_ip: IpAddr,
    pub src_port: u16,
    pub dst_port: u16,
    pub protocol: TransportProtocol,
    pub established: bool,
    pub last_seen: SystemTime,
}

pub struct StatefulFirewall {
    pub firewall: Firewall,
    pub connections: HashMap<(IpAddr, u16, IpAddr, u16, TransportProtocol), ConnectionState>,
}

impl StatefulFirewall {
    pub fn new(firewall: Firewall) -> Self {
        StatefulFirewall {
            firewall,
            connections: HashMap::new(),
        }
    }
    pub fn process_packet(&mut self, packet: &NetworkPacket) -> FirewallAction {
        // Track connection state for TCP
        if packet.protocol == TransportProtocol::TCP {
            let key = (
                packet.src_ip.unwrap_or_default(),
                packet.src_port.unwrap_or_default(),
                packet.dst_ip.unwrap_or_default(),
                packet.dst_port.unwrap_or_default(),
                packet.protocol,
            );
            let state = self.connections.entry(key).or_insert(ConnectionState {
                src_ip: packet.src_ip.unwrap_or_default(),
                dst_ip: packet.dst_ip.unwrap_or_default(),
                src_port: packet.src_port.unwrap_or_default(),
                dst_port: packet.dst_port.unwrap_or_default(),
                protocol: packet.protocol,
                established: false,
                last_seen: SystemTime::now(),
            });
            state.last_seen = SystemTime::now();
            // Example: set established if SYN+ACK seen
            if let Some(flags) = packet.tcp_flags {
                if flags & 0x12 == 0x12 { // SYN+ACK
                    state.established = true;
                }
            }
        }
        self.firewall.check_packet(packet)
    }
}
```

## Logging and Alerting

Blocked packets should be logged for auditing:

```rust
pub fn log_blocked_packet(packet: &NetworkPacket, rule: &FirewallRule) {
    println!("[FIREWALL BLOCKED] Rule '{}': {}", rule.name, packet.summary());
}
```

## Example Usage

```rust
fn main() {
    let mut firewall = Firewall::new();
    firewall.add_rule(FirewallRule {
        name: "Block Telnet".to_string(),
        src_ip: None,
        dst_ip: None,
        src_port: None,
        dst_port: Some(23),
        protocol: Some(TransportProtocol::TCP),
        action: FirewallAction::Block,
        log: true,
    });
    // ... set up packet capture ...
    capture.add_handler(move |packet| {
        let action = firewall.check_packet(packet);
        if action == FirewallAction::Block {
            println!("Blocked packet: {}", packet.summary());
        }
    });
}
```

## Best Practices

- Place most specific rules first
- Regularly review and update rules
- Log all blocked packets for auditing
- Use stateful filtering for protocols like TCP
- Test rules in a safe environment before deployment

## Conclusion

In this section, we've implemented a basic firewall in Rust, including:

1. Rule-based packet filtering
2. Stateless and stateful filtering
3. Logging and alerting for blocked packets
4. Integration with packet capture and other tools

In the next section, we'll explore protocol analyzers for deeper inspection of network traffic.

---

🔗 **Previous**: [04-intrusion-detection.md](./04-intrusion-detection.md)

🔗 **Next**: [06-protocol-analyzers.md](./06-protocol-analyzers.md)
