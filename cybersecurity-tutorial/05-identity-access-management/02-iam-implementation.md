# IAM Implementation in Rust

In this section, we'll implement basic Identity and Access Management (IAM) features in Rust, including user authentication, role-based access control, and account lifecycle management.

## Overview

We'll cover:

1. User and role data structures
2. Password-based authentication
3. Role-based access control (RBAC)
4. Account provisioning and de-provisioning
5. Auditing and logging

## User and Role Data Structures

```rust
use std::collections::HashSet;

#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct User {
    pub username: String,
    pub password_hash: String,
    pub roles: HashSet<String>,
    pub active: bool,
}

#[derive(Debug, <PERSON>lone)]
pub struct Role {
    pub name: String,
    pub permissions: HashSet<String>,
}
```

## Password-Based Authentication

```rust
use sha2::{Sha256, Digest};

pub fn hash_password(password: &str) -> String {
    let mut hasher = Sha256::new();
    hasher.update(password.as_bytes());
    format!("{:x}", hasher.finalize())
}

pub fn authenticate(user: &User, password: &str) -> bool {
    user.active && user.password_hash == hash_password(password)
}
```

## Role-Based Access Control (RBAC)

```rust
pub fn is_authorized(user: &User, required_permission: &str, roles: &[Role]) -> bool {
    for role_name in &user.roles {
        if let Some(role) = roles.iter().find(|r| &r.name == role_name) {
            if role.permissions.contains(required_permission) {
                return true;
            }
        }
    }
    false
}
```

## Account Provisioning and De-Provisioning

```rust
pub fn provision_user(username: &str, password: &str, roles: &[&str]) -> User {
    User {
        username: username.to_string(),
        password_hash: hash_password(password),
        roles: roles.iter().map(|r| r.to_string()).collect(),
        active: true,
    }
}

pub fn deprovision_user(user: &mut User) {
    user.active = false;
}
```

## Auditing and Logging

```rust
use chrono::Utc;

pub fn log_event(event: &str, username: &str) {
    println!("[{}] {}: {}", Utc::now(), username, event);
}
```

## Example Usage

```rust
fn main() {
    let admin_role = Role {
        name: "admin".to_string(),
        permissions: ["read", "write", "delete"].iter().cloned().map(String::from).collect(),
    };
    let mut user = provision_user("alice", "password123", &["admin"]);
    let roles = vec![admin_role];
    if authenticate(&user, "password123") {
        log_event("Login successful", &user.username);
        if is_authorized(&user, "delete", &roles) {
            log_event("Authorized for delete", &user.username);
        }
    }
    deprovision_user(&mut user);
    log_event("User deprovisioned", &user.username);
}
```

## Conclusion

We've implemented basic IAM features in Rust, including authentication, RBAC, and account management. In the next section, we'll explore advanced IAM topics such as SSO and federation.

---

🔗 **Previous**: [01-identity-access-concepts.md](./01-identity-access-concepts.md)

🔗 **Next**: [03-advanced-iam.md](./03-advanced-iam.md)
