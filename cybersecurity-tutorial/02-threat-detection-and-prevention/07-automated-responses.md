# Automated Responses

Automated responses are a critical component of modern security systems, enabling immediate action in response to detected threats without requiring human intervention. In this section, we'll implement a flexible and extensible automated response framework in Rust that can integrate with our threat detection and alert management systems.

## Overview

We'll cover:

1. Response action framework
2. Configurable response policies
3. Implementing common response actions
4. Risk assessment and proportional response
5. Logging and audit trails for automated actions

## Response Action Framework

Let's start by creating a framework for defining and executing response actions:

```rust
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{SystemTime, UNIX_EPOCH};
use uuid::Uuid;
use serde::{Serialize, Deserialize};

// Import types from previous modules
use crate::alert_management::{SecurityAlert, AlertSeverity};

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ResponseStatus {
    Pending,
    InProgress,
    Completed,
    Failed,
    Canceled,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ResponseAction {
    id: String,
    name: String,
    description: String,
    alert_id: String,
    target_asset: String,
    action_type: String,
    parameters: HashMap<String, String>,
    status: ResponseStatus,
    created_at: u64,
    updated_at: u64,
    completed_at: Option<u64>,
    result: Option<String>,
    error: Option<String>,
    executed_by: String,
    approved_by: Option<String>,
    requires_approval: bool,
}

impl ResponseAction {
    pub fn new(
        name: &str,
        description: &str,
        alert_id: &str,
        target_asset: &str,
        action_type: &str,
        parameters: HashMap<String, String>,
        requires_approval: bool,
    ) -> Self {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_else(|_| std::time::Duration::from_secs(0))
            .as_secs();
            
        ResponseAction {
            id: Uuid::new_v4().to_string(),
            name: name.to_string(),
            description: description.to_string(),
            alert_id: alert_id.to_string(),
            target_asset: target_asset.to_string(),
            action_type: action_type.to_string(),
            parameters,
            status: ResponseStatus::Pending,
            created_at: current_time,
            updated_at: current_time,
            completed_at: None,
            result: None,
            error: None,
            executed_by: "system".to_string(),
            approved_by: None,
            requires_approval,
        }
    }
    
    pub fn start(&mut self) {
        self.status = ResponseStatus::InProgress;
        self.update_timestamp();
    }
    
    pub fn complete(&mut self, result: &str) {
        self.status = ResponseStatus::Completed;
        self.result = Some(result.to_string());
        self.completed_at = Some(SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_else(|_| std::time::Duration::from_secs(0))
            .as_secs());
        self.update_timestamp();
    }
    
    pub fn fail(&mut self, error: &str) {
        self.status = ResponseStatus::Failed;
        self.error = Some(error.to_string());
        self.completed_at = Some(SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_else(|_| std::time::Duration::from_secs(0))
            .as_secs());
        self.update_timestamp();
    }
    
    pub fn cancel(&mut self) {
        self.status = ResponseStatus::Canceled;
        self.update_timestamp();
    }
    
    pub fn approve(&mut self, approver: &str) {
        self.approved_by = Some(approver.to_string());
        self.update_timestamp();
    }
    
    pub fn set_executor(&mut self, executor: &str) {
        self.executed_by = executor.to_string();
        self.update_timestamp();
    }
    
    fn update_timestamp(&mut self) {
        self.updated_at = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_else(|_| std::time::Duration::from_secs(0))
            .as_secs();
    }
    
    pub fn is_approved(&self) -> bool {
        if !self.requires_approval {
            return true;
        }
        self.approved_by.is_some()
    }
    
    pub fn duration_secs(&self) -> Option<u64> {
        self.completed_at.map(|completed| completed - self.created_at)
    }
}

// Define a trait for executing response actions
pub trait ActionExecutor: Send + Sync {
    fn execute(&self, action: &mut ResponseAction) -> Result<(), String>;
    fn supports_action_type(&self, action_type: &str) -> bool;
    fn validate_parameters(&self, parameters: &HashMap<String, String>) -> Result<(), String>;
}
```

## Response Policy Framework

Now, let's create a system for defining policies that determine when and how to respond to alerts:

```rust
pub struct ResponsePolicy {
    name: String,
    description: String,
    conditions: Box<dyn Fn(&SecurityAlert) -> bool + Send + Sync>,
    action_generator: Box<dyn Fn(&SecurityAlert) -> Vec<ResponseAction> + Send + Sync>,
}

impl ResponsePolicy {
    pub fn new(
        name: &str,
        description: &str,
        conditions: impl Fn(&SecurityAlert) -> bool + Send + Sync + 'static,
        action_generator: impl Fn(&SecurityAlert) -> Vec<ResponseAction> + Send + Sync + 'static,
    ) -> Self {
        ResponsePolicy {
            name: name.to_string(),
            description: description.to_string(),
            conditions: Box::new(conditions),
            action_generator: Box::new(action_generator),
        }
    }
    
    pub fn applies_to(&self, alert: &SecurityAlert) -> bool {
        (self.conditions)(alert)
    }
    
    pub fn generate_actions(&self, alert: &SecurityAlert) -> Vec<ResponseAction> {
        (self.action_generator)(alert)
    }
}

// Automated Response Manager
pub struct AutoResponseManager {
    policies: Arc<Mutex<Vec<ResponsePolicy>>>,
    executors: Arc<Mutex<Vec<Box<dyn ActionExecutor>>>>,
    actions: Arc<Mutex<HashMap<String, ResponseAction>>>,
}

impl AutoResponseManager {
    pub fn new() -> Self {
        AutoResponseManager {
            policies: Arc::new(Mutex::new(Vec::new())),
            executors: Arc::new(Mutex::new(Vec::new())),
            actions: Arc::new(Mutex::new(HashMap::new())),
        }
    }
    
    pub fn add_policy(&self, policy: ResponsePolicy) {
        let mut policies = self.policies.lock().unwrap();
        policies.push(policy);
    }
    
    pub fn register_executor(&self, executor: Box<dyn ActionExecutor>) {
        let mut executors = self.executors.lock().unwrap();
        executors.push(executor);
    }
    
    pub fn process_alert(&self, alert: &SecurityAlert) -> Vec<String> {
        let mut actions_ids = Vec::new();
        
        // Find matching policies
        let matching_policies = {
            let policies = self.policies.lock().unwrap();
            policies.iter()
                .filter(|p| p.applies_to(alert))
                .collect::<Vec<_>>()
        };
        
        // Generate actions from matching policies
        let mut generated_actions = Vec::new();
        for policy in matching_policies {
            generated_actions.extend(policy.generate_actions(alert));
        }
        
        // Store and potentially execute actions
        for mut action in generated_actions {
            let action_id = action.id.clone();
            
            // Execute immediately if no approval required
            if !action.requires_approval {
                self.execute_action(&mut action);
            }
            
            // Store the action
            let mut actions = self.actions.lock().unwrap();
            actions.insert(action_id.clone(), action);
            actions_ids.push(action_id);
        }
        
        actions_ids
    }
    
    pub fn approve_action(&self, action_id: &str, approver: &str) -> Result<(), String> {
        let mut actions = self.actions.lock().unwrap();
        
        let action = actions.get_mut(action_id)
            .ok_or_else(|| format!("Action with ID {} not found", action_id))?;
            
        if action.status != ResponseStatus::Pending {
            return Err(format!("Action is not pending, current status: {:?}", action.status));
        }
        
        action.approve(approver);
        
        // Now execute the approved action
        self.execute_action(action);
        
        Ok(())
    }
    
    pub fn cancel_action(&self, action_id: &str) -> Result<(), String> {
        let mut actions = self.actions.lock().unwrap();
        
        let action = actions.get_mut(action_id)
            .ok_or_else(|| format!("Action with ID {} not found", action_id))?;
            
        if action.status != ResponseStatus::Pending {
            return Err(format!("Action cannot be cancelled, current status: {:?}", action.status));
        }
        
        action.cancel();
        Ok(())
    }
    
    pub fn get_action(&self, action_id: &str) -> Option<ResponseAction> {
        let actions = self.actions.lock().unwrap();
        actions.get(action_id).cloned()
    }
    
    pub fn get_actions_by_alert(&self, alert_id: &str) -> Vec<ResponseAction> {
        let actions = self.actions.lock().unwrap();
        actions.values()
            .filter(|a| a.alert_id == alert_id)
            .cloned()
            .collect()
    }
    
    pub fn get_pending_actions(&self) -> Vec<ResponseAction> {
        let actions = self.actions.lock().unwrap();
        actions.values()
            .filter(|a| a.status == ResponseStatus::Pending && a.requires_approval)
            .cloned()
            .collect()
    }
    
    fn execute_action(&self, action: &mut ResponseAction) {
        if !action.is_approved() {
            return;
        }
        
        let executor_option = {
            let executors = self.executors.lock().unwrap();
            executors.iter()
                .find(|e| e.supports_action_type(&action.action_type))
                .cloned()
        };
        
        if let Some(executor) = executor_option {
            action.start();
            
            match executor.execute(action) {
                Ok(()) => {
                    action.complete("Action executed successfully");
                },
                Err(error) => {
                    action.fail(&error);
                }
            }
        } else {
            action.fail(&format!("No executor found for action type: {}", action.action_type));
        }
    }
}
```

## Common Response Actions

Let's implement some typical response actions for security incidents:

```rust
// Network isolation executor
pub struct NetworkIsolationExecutor {
    firewall_api: String,
    api_token: String,
}

impl NetworkIsolationExecutor {
    pub fn new(firewall_api: &str, api_token: &str) -> Self {
        NetworkIsolationExecutor {
            firewall_api: firewall_api.to_string(),
            api_token: api_token.to_string(),
        }
    }
}

impl ActionExecutor for NetworkIsolationExecutor {
    fn execute(&self, action: &mut ResponseAction) -> Result<(), String> {
        // In a real implementation, this would call the firewall API
        // For the tutorial, we'll simulate the API call
        
        println!("Executing network isolation on {}", action.target_asset);
        println!("Firewall API: {}", self.firewall_api);
        println!("API Token: {}", self.api_token);
        
        let isolation_type = action.parameters.get("isolation_type")
            .ok_or_else(|| "Missing isolation_type parameter".to_string())?;
            
        match isolation_type.as_str() {
            "full" => {
                println!("Implementing full network isolation for {}", action.target_asset);
                // Simulate blocking all traffic
            },
            "partial" => {
                let allowed_ips = action.parameters.get("allowed_ips")
                    .ok_or_else(|| "Missing allowed_ips for partial isolation".to_string())?;
                    
                println!("Implementing partial network isolation for {}", action.target_asset);
                println!("Allowing traffic only to/from: {}", allowed_ips);
                // Simulate allowing only specific traffic
            },
            _ => return Err(format!("Unsupported isolation type: {}", isolation_type)),
        }
        
        // Simulate successful isolation
        let result = format!(
            "Successfully isolated {} with {} isolation", 
            action.target_asset, 
            isolation_type
        );
        
        action.complete(&result);
        Ok(())
    }
    
    fn supports_action_type(&self, action_type: &str) -> bool {
        action_type == "network_isolation"
    }
    
    fn validate_parameters(&self, parameters: &HashMap<String, String>) -> Result<(), String> {
        if !parameters.contains_key("isolation_type") {
            return Err("Missing required parameter: isolation_type".to_string());
        }
        
        let isolation_type = parameters.get("isolation_type").unwrap();
        if isolation_type == "partial" && !parameters.contains_key("allowed_ips") {
            return Err("Partial isolation requires allowed_ips parameter".to_string());
        }
        
        Ok(())
    }
}

// Process termination executor
pub struct ProcessTerminationExecutor {
    endpoint_api: String,
    api_token: String,
}

impl ProcessTerminationExecutor {
    pub fn new(endpoint_api: &str, api_token: &str) -> Self {
        ProcessTerminationExecutor {
            endpoint_api: endpoint_api.to_string(),
            api_token: api_token.to_string(),
        }
    }
}

impl ActionExecutor for ProcessTerminationExecutor {
    fn execute(&self, action: &mut ResponseAction) -> Result<(), String> {
        println!("Executing process termination on {}", action.target_asset);
        println!("Endpoint API: {}", self.endpoint_api);
        
        let process_id = action.parameters.get("process_id")
            .ok_or_else(|| "Missing process_id parameter".to_string())?;
            
        let host = action.target_asset.strip_prefix("host:")
            .ok_or_else(|| format!("Invalid target asset format: {}", action.target_asset))?;
            
        // In a real implementation, this would call the endpoint API
        println!("Terminating process {} on host {}", process_id, host);
        
        action.complete(&format!("Successfully terminated process {} on host {}", process_id, host));
        Ok(())
    }
    
    fn supports_action_type(&self, action_type: &str) -> bool {
        action_type == "process_termination"
    }
    
    fn validate_parameters(&self, parameters: &HashMap<String, String>) -> Result<(), String> {
        if !parameters.contains_key("process_id") {
            return Err("Missing required parameter: process_id".to_string());
        }
        
        Ok(())
    }
}

// File quarantine executor
pub struct FileQuarantineExecutor {
    antivirus_api: String,
    api_token: String,
}

impl FileQuarantineExecutor {
    pub fn new(antivirus_api: &str, api_token: &str) -> Self {
        FileQuarantineExecutor {
            antivirus_api: antivirus_api.to_string(),
            api_token: api_token.to_string(),
        }
    }
}

impl ActionExecutor for FileQuarantineExecutor {
    fn execute(&self, action: &mut ResponseAction) -> Result<(), String> {
        println!("Executing file quarantine via {}", self.antivirus_api);
        
        let file_path = action.parameters.get("file_path")
            .ok_or_else(|| "Missing file_path parameter".to_string())?;
            
        let host = action.target_asset.strip_prefix("host:")
            .ok_or_else(|| format!("Invalid target asset format: {}", action.target_asset))?;
            
        // In a real implementation, this would call the antivirus API
        println!("Quarantining file {} on host {}", file_path, host);
        
        action.complete(&format!("Successfully quarantined file {} on host {}", file_path, host));
        Ok(())
    }
    
    fn supports_action_type(&self, action_type: &str) -> bool {
        action_type == "file_quarantine"
    }
    
    fn validate_parameters(&self, parameters: &HashMap<String, String>) -> Result<(), String> {
        if !parameters.contains_key("file_path") {
            return Err("Missing required parameter: file_path".to_string());
        }
        
        Ok(())
    }
}

// User account management executor
pub struct UserAccountExecutor {
    identity_api: String,
    api_token: String,
}

impl UserAccountExecutor {
    pub fn new(identity_api: &str, api_token: &str) -> Self {
        UserAccountExecutor {
            identity_api: identity_api.to_string(),
            api_token: api_token.to_string(),
        }
    }
}

impl ActionExecutor for UserAccountExecutor {
    fn execute(&self, action: &mut ResponseAction) -> Result<(), String> {
        println!("Executing user account action via {}", self.identity_api);
        
        let username = action.parameters.get("username")
            .ok_or_else(|| "Missing username parameter".to_string())?;
            
        let operation = action.parameters.get("operation")
            .ok_or_else(|| "Missing operation parameter".to_string())?;
            
        // In a real implementation, this would call the identity management API
        match operation.as_str() {
            "disable" => {
                println!("Disabling user account: {}", username);
                action.complete(&format!("Successfully disabled user account {}", username));
            },
            "reset_password" => {
                println!("Forcing password reset for user: {}", username);
                action.complete(&format!("Successfully forced password reset for user {}", username));
            },
            "revoke_sessions" => {
                println!("Revoking active sessions for user: {}", username);
                action.complete(&format!("Successfully revoked active sessions for user {}", username));
            },
            _ => return Err(format!("Unsupported user account operation: {}", operation)),
        }
        
        Ok(())
    }
    
    fn supports_action_type(&self, action_type: &str) -> bool {
        action_type == "user_account_management"
    }
    
    fn validate_parameters(&self, parameters: &HashMap<String, String>) -> Result<(), String> {
        if !parameters.contains_key("username") {
            return Err("Missing required parameter: username".to_string());
        }
        
        if !parameters.contains_key("operation") {
            return Err("Missing required parameter: operation".to_string());
        }
        
        let operation = parameters.get("operation").unwrap();
        if operation != "disable" && operation != "reset_password" && operation != "revoke_sessions" {
            return Err(format!("Invalid operation: {}", operation));
        }
        
        Ok(())
    }
}
```

## Risk Assessment and Proportional Response

Now, let's create a system that evaluates the risk and determines the appropriate level of response:

```rust
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum ResponseLevel {
    Informational,
    Low,
    Medium,
    High,
    Emergency,
}

pub struct RiskAssessor {
    asset_criticality: HashMap<String, u8>, // 1-10 scale of asset importance
}

impl RiskAssessor {
    pub fn new() -> Self {
        RiskAssessor {
            asset_criticality: HashMap::new(),
        }
    }
    
    pub fn add_asset_criticality(&mut self, asset_id: &str, criticality: u8) {
        if criticality > 0 && criticality <= 10 {
            self.asset_criticality.insert(asset_id.to_string(), criticality);
        }
    }
    
    pub fn assess_risk(&self, alert: &SecurityAlert) -> ResponseLevel {
        // Calculate risk based on alert severity and asset criticality
        let severity_score = match alert.severity {
            AlertSeverity::Critical => 4,
            AlertSeverity::High => 3,
            AlertSeverity::Medium => 2,
            AlertSeverity::Low => 1,
        };
        
        // Get highest criticality from affected assets
        let max_criticality = alert.affected_assets.iter()
            .filter_map(|asset| self.asset_criticality.get(asset))
            .max()
            .copied()
            .unwrap_or(5); // Default to medium criticality if unknown
            
        // Calculate combined risk score (1-40)
        let risk_score = severity_score * max_criticality;
        
        // Map to response level
        match risk_score {
            1..=8 => ResponseLevel::Informational,
            9..=16 => ResponseLevel::Low,
            17..=24 => ResponseLevel::Medium,
            25..=32 => ResponseLevel::High,
            _ => ResponseLevel::Emergency,
        }
    }
    
    pub fn determine_response(&self, alert: &SecurityAlert) -> Vec<ResponseAction> {
        let response_level = self.assess_risk(alert);
        let mut actions = Vec::new();
        
        // Generate appropriate actions based on response level and alert tags
        match response_level {
            ResponseLevel::Informational => {
                // Just log, no actions
            },
            ResponseLevel::Low => {
                // For malware, quarantine the file
                if alert.tags.contains("malware") {
                    if let Some(file_path) = alert.metadata.get("malware_file") {
                        for asset in &alert.affected_assets {
                            if asset.starts_with("host:") {
                                let mut params = HashMap::new();
                                params.insert("file_path".to_string(), file_path.clone());
                                
                                actions.push(ResponseAction::new(
                                    "Quarantine Malware",
                                    &format!("Quarantine malicious file on {}", asset),
                                    &alert.id,
                                    asset,
                                    "file_quarantine",
                                    params,
                                    false, // No approval needed for low risk
                                ));
                            }
                        }
                    }
                }
            },
            ResponseLevel::Medium => {
                // For malware, quarantine and kill process
                if alert.tags.contains("malware") {
                    if let Some(file_path) = alert.metadata.get("malware_file") {
                        for asset in &alert.affected_assets {
                            if asset.starts_with("host:") {
                                // Quarantine file
                                let mut file_params = HashMap::new();
                                file_params.insert("file_path".to_string(), file_path.clone());
                                
                                actions.push(ResponseAction::new(
                                    "Quarantine Malware",
                                    &format!("Quarantine malicious file on {}", asset),
                                    &alert.id,
                                    asset,
                                    "file_quarantine",
                                    file_params,
                                    false,
                                ));
                                
                                // Terminate process if available
                                if let Some(process_id) = alert.metadata.get("process_id") {
                                    let mut proc_params = HashMap::new();
                                    proc_params.insert("process_id".to_string(), process_id.clone());
                                    
                                    actions.push(ResponseAction::new(
                                        "Terminate Malicious Process",
                                        &format!("Terminate malicious process on {}", asset),
                                        &alert.id,
                                        asset,
                                        "process_termination",
                                        proc_params,
                                        false,
                                    ));
                                }
                            }
                        }
                    }
                }
                
                // For brute force attempts, require password reset
                if alert.tags.contains("brute_force") {
                    if let Some(username) = alert.metadata.get("target_user") {
                        let mut params = HashMap::new();
                        params.insert("username".to_string(), username.clone());
                        params.insert("operation".to_string(), "reset_password".to_string());
                        
                        actions.push(ResponseAction::new(
                            "Force Password Reset",
                            &format!("Force password reset for user {}", username),
                            &alert.id,
                            &format!("user:{}", username),
                            "user_account_management",
                            params,
                            true, // Require approval for user account actions
                        ));
                    }
                }
            },
            ResponseLevel::High => {
                // For malware, quarantine, kill process, and partial isolation
                if alert.tags.contains("malware") {
                    for asset in &alert.affected_assets {
                        if asset.starts_with("host:") {
                            // Quarantine file if available
                            if let Some(file_path) = alert.metadata.get("malware_file") {
                                let mut file_params = HashMap::new();
                                file_params.insert("file_path".to_string(), file_path.clone());
                                
                                actions.push(ResponseAction::new(
                                    "Quarantine Malware",
                                    &format!("Quarantine malicious file on {}", asset),
                                    &alert.id,
                                    asset,
                                    "file_quarantine",
                                    file_params,
                                    false,
                                ));
                            }
                            
                            // Terminate process if available
                            if let Some(process_id) = alert.metadata.get("process_id") {
                                let mut proc_params = HashMap::new();
                                proc_params.insert("process_id".to_string(), process_id.clone());
                                
                                actions.push(ResponseAction::new(
                                    "Terminate Malicious Process",
                                    &format!("Terminate malicious process on {}", asset),
                                    &alert.id,
                                    asset,
                                    "process_termination",
                                    proc_params,
                                    false,
                                ));
                            }
                            
                            // Partial network isolation
                            let mut isolation_params = HashMap::new();
                            isolation_params.insert("isolation_type".to_string(), "partial".to_string());
                            isolation_params.insert("allowed_ips".to_string(), "********,********".to_string());
                            
                            actions.push(ResponseAction::new(
                                "Partial Network Isolation",
                                &format!("Implement partial network isolation for {}", asset),
                                &alert.id,
                                asset,
                                "network_isolation",
                                isolation_params,
                                true, // Require approval for network isolation
                            ));
                        }
                    }
                }
                
                // For unauthorized access, disable account and revoke sessions
                if alert.tags.contains("unauthorized_access") {
                    if let Some(username) = alert.metadata.get("target_user") {
                        // Revoke active sessions
                        let mut revoke_params = HashMap::new();
                        revoke_params.insert("username".to_string(), username.clone());
                        revoke_params.insert("operation".to_string(), "revoke_sessions".to_string());
                        
                        actions.push(ResponseAction::new(
                            "Revoke User Sessions",
                            &format!("Revoke active sessions for user {}", username),
                            &alert.id,
                            &format!("user:{}", username),
                            "user_account_management",
                            revoke_params,
                            true,
                        ));
                        
                        // Disable account
                        let mut disable_params = HashMap::new();
                        disable_params.insert("username".to_string(), username.clone());
                        disable_params.insert("operation".to_string(), "disable".to_string());
                        
                        actions.push(ResponseAction::new(
                            "Disable User Account",
                            &format!("Disable compromised user account {}", username),
                            &alert.id,
                            &format!("user:{}", username),
                            "user_account_management",
                            disable_params,
                            true,
                        ));
                    }
                }
            },
            ResponseLevel::Emergency => {
                // For critical threats, full isolation of affected hosts
                for asset in &alert.affected_assets {
                    if asset.starts_with("host:") {
                        let mut isolation_params = HashMap::new();
                        isolation_params.insert("isolation_type".to_string(), "full".to_string());
                        
                        actions.push(ResponseAction::new(
                            "Emergency Network Isolation",
                            &format!("Implement full network isolation for {}", asset),
                            &alert.id,
                            asset,
                            "network_isolation",
                            isolation_params,
                            false, // No approval for emergency response
                        ));
                    }
                }
                
                // Disable any compromised accounts immediately
                if let Some(username) = alert.metadata.get("target_user") {
                    let mut params = HashMap::new();
                    params.insert("username".to_string(), username.clone());
                    params.insert("operation".to_string(), "disable".to_string());
                    
                    actions.push(ResponseAction::new(
                        "Emergency Account Disable",
                        &format!("Disable compromised user account {}", username),
                        &alert.id,
                        &format!("user:{}", username),
                        "user_account_management",
                        params,
                        false, // No approval for emergency response
                    ));
                }
            }
        }
        
        actions
    }
}
```

## Response Audit Logging

Let's create a comprehensive audit trail system for automated actions:

```rust
use std::fs::{File, OpenOptions};
use std::io::Write;
use chrono::{Utc, DateTime};

pub struct ResponseAuditor {
    log_file: String,
}

impl ResponseAuditor {
    pub fn new(log_file: &str) -> Self {
        ResponseAuditor {
            log_file: log_file.to_string(),
        }
    }
    
    pub fn log_action(&self, action: &ResponseAction) -> Result<(), String> {
        let now: DateTime<Utc> = Utc::now();
        let timestamp = now.format("%Y-%m-%d %H:%M:%S UTC").to_string();
        
        let log_entry = format!(
            "[{}] [{}] [{}] Action ID: {}, Type: {}, Target: {}, Status: {:?}, Executor: {}\n",
            timestamp,
            action.alert_id,
            action.name,
            action.id,
            action.action_type,
            action.target_asset,
            action.status,
            action.executed_by
        );
        
        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&self.log_file)
            .map_err(|e| format!("Failed to open log file: {}", e))?;
            
        file.write_all(log_entry.as_bytes())
            .map_err(|e| format!("Failed to write to log file: {}", e))?;
            
        Ok(())
    }
    
    pub fn log_policy_match(&self, alert_id: &str, policy_name: &str) -> Result<(), String> {
        let now: DateTime<Utc> = Utc::now();
        let timestamp = now.format("%Y-%m-%d %H:%M:%S UTC").to_string();
        
        let log_entry = format!(
            "[{}] [{}] Alert matched policy: {}\n",
            timestamp,
            alert_id,
            policy_name
        );
        
        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&self.log_file)
            .map_err(|e| format!("Failed to open log file: {}", e))?;
            
        file.write_all(log_entry.as_bytes())
            .map_err(|e| format!("Failed to write to log file: {}", e))?;
            
        Ok(())
    }
}
```

## Integration with Alert Manager

Let's integrate our automated response system with the alert management system:

```rust
use std::sync::mpsc::{self, Receiver, Sender};
use std::thread;

pub struct AutoResponseSystem {
    auto_manager: Arc<AutoResponseManager>,
    risk_assessor: Arc<RiskAssessor>,
    response_auditor: Arc<ResponseAuditor>,
}

impl AutoResponseSystem {
    pub fn new(
        auto_manager: Arc<AutoResponseManager>,
        risk_assessor: Arc<RiskAssessor>,
        response_auditor: Arc<ResponseAuditor>,
    ) -> Self {
        AutoResponseSystem {
            auto_manager,
            risk_assessor,
            response_auditor,
        }
    }
    
    pub fn setup_alert_handler(&self, rx: Receiver<SecurityAlert>) -> thread::JoinHandle<()> {
        let auto_manager = self.auto_manager.clone();
        let risk_assessor = self.risk_assessor.clone();
        let response_auditor = self.response_auditor.clone();
        
        thread::spawn(move || {
            for alert in rx {
                println!("AutoResponseSystem: Processing alert {}: {}", alert.id, alert.title);
                
                // Determine risk level and appropriate responses
                let actions = risk_assessor.determine_response(&alert);
                
                // Log the risk assessment
                let response_level = risk_assessor.assess_risk(&alert);
                println!("Risk Assessment: {:?} for alert {}", response_level, alert.id);
                
                // Process each action
                for action in actions {
                    println!("Initiating action: {} on {}", action.name, action.target_asset);
                    
                    // Process the action
                    auto_manager.process_alert(&alert);
                    
                    // Log the action for audit
                    if let Err(e) = response_auditor.log_action(&action) {
                        eprintln!("Failed to log action: {}", e);
                    }
                }
            }
        })
    }
    
    pub fn create_alert_channel() -> (Sender<SecurityAlert>, Receiver<SecurityAlert>) {
        mpsc::channel()
    }
}

```

## Putting It All Together

Let's create a comprehensive example showing how the automated response system works:

```rust
fn main() {
    println!("Initializing Automated Response System");
    
    // Set up components
    let auto_manager = Arc::new(AutoResponseManager::new());
    
    // Register action executors
    {
        let mut manager = AutoResponseManager::new();
        
        // Network isolation executor
        manager.register_executor(Box::new(NetworkIsolationExecutor::new(
            "https://firewall-api.example.com", 
            "api-token-123"
        )));
        
        // Process termination executor
        manager.register_executor(Box::new(ProcessTerminationExecutor::new(
            "https://endpoint-api.example.com",
            "api-token-456"
        )));
        
        // File quarantine executor
        manager.register_executor(Box::new(FileQuarantineExecutor::new(
            "https://antivirus-api.example.com",
            "api-token-789"
        )));
        
        // User account management executor
        manager.register_executor(Box::new(UserAccountExecutor::new(
            "https://identity-api.example.com",
            "api-token-abc"
        )));
        
        *auto_manager = Arc::new(manager);
    }
    
    // Set up risk assessor with asset criticality
    let mut risk_assessor = RiskAssessor::new();
    
    // Configure asset criticality (1-10 scale)
    risk_assessor.add_asset_criticality("host:dc1.example.com", 10);  // Domain controller
    risk_assessor.add_asset_criticality("host:db1.example.com", 9);   // Database server
    risk_assessor.add_asset_criticality("host:web1.example.com", 7);  // Web server
    risk_assessor.add_asset_criticality("host:workstation1", 4);      // Employee workstation
    risk_assessor.add_asset_criticality("user:admin", 10);            // Admin account
    risk_assessor.add_asset_criticality("user:bob", 3);               // Regular user
    
    let risk_assessor = Arc::new(risk_assessor);
    
    // Set up response auditor
    let response_auditor = Arc::new(ResponseAuditor::new("response_audit.log"));
    
    // Create auto-response system
    let auto_response_system = AutoResponseSystem::new(
        auto_manager.clone(),
        risk_assessor.clone(),
        response_auditor.clone()
    );
    
    // Create alert channel
    let (alert_tx, alert_rx) = AutoResponseSystem::create_alert_channel();
    
    // Start alert handler thread
    let _handler = auto_response_system.setup_alert_handler(alert_rx);
    
    // Simulate some security alerts
    println!("\nSimulating security alerts...\n");
    
    // Example 1: Low-risk malware alert
    {
        let mut alert = SecurityAlert::new(
            "Low-risk PUP detected".to_string(),
            "Potentially unwanted program detected on workstation".to_string(),
            AlertSeverity::Low,
            AlertSeverity::Low,
            vec!["host:workstation1".to_string(), "file:/tmp/adware.exe".to_string()]
        );
        
        alert.add_tag("malware");
        alert.add_metadata("malware_file", "/tmp/adware.exe");
        alert.add_metadata("malware_type", "adware");
        
        println!("Sending low-risk malware alert");
        alert_tx.send(alert).unwrap();
    }
    
    thread::sleep(Duration::from_millis(500));
    
    // Example 2: High-risk ransomware alert on critical server
    {
        let mut alert = SecurityAlert::new(
            "Ransomware activity detected".to_string(),
            "File encryption behavior detected on database server".to_string(),
            AlertSeverity::Critical,
            AlertSeverity::Critical,
            vec!["host:db1.example.com".to_string()]
        );
        
        alert.add_tag("malware");
        alert.add_tag("ransomware");
        alert.add_metadata("malware_file", "/var/tmp/cryptor.bin");
        alert.add_metadata("process_id", "4256");
        
        println!("Sending critical ransomware alert");
        alert_tx.send(alert).unwrap();
    }
    
    thread::sleep(Duration::from_millis(500));
    
    // Example 3: Brute force attack
    {
        let mut alert = SecurityAlert::new(
            "Multiple authentication failures".to_string(),
            "Multiple failed login attempts detected for admin user".to_string(),
            AlertSeverity::Medium,
            AlertSeverity::Medium,
            vec!["user:admin".to_string()]
        );
        
        alert.add_tag("brute_force");
        alert.add_tag("authentication_failure");
        alert.add_metadata("target_user", "admin");
        alert.add_metadata("source_ip", "*************");
        alert.add_metadata("failure_count", "25");
        
        println!("Sending brute force attack alert");
        alert_tx.send(alert).unwrap();
    }
    
    thread::sleep(Duration::from_millis(500));
    
    // Example 4: Unauthorized access
    {
        let mut alert = SecurityAlert::new(
            "Unauthorized privilege escalation".to_string(),
            "User bob performed unauthorized privilege escalation to admin".to_string(),
            AlertSeverity::High,
            AlertSeverity::High,
            vec!["user:bob".to_string(), "host:workstation1".to_string()]
        );
        
        alert.add_tag("unauthorized_access");
        alert.add_tag("privilege_escalation");
        alert.add_metadata("target_user", "bob");
        alert.add_metadata("escalation_method", "sudo bypass");
        
        println!("Sending unauthorized access alert");
        alert_tx.send(alert).unwrap();
    }
    
    // Wait for responses to be processed
    thread::sleep(Duration::from_secs(2));
    
    println!("\nAutomated response simulation complete!");
    println!("Check response_audit.log for audit trail of automated actions.");
}
```

## Key Concepts and Best Practices

1. **Proportional Response**
   - Match the severity of the response to the risk level
   - Consider asset criticality when determining response actions
   - Implement tiered response levels with appropriate actions

2. **Human in the Loop**
   - Require approval for high-impact actions
   - Provide clear context for approval decisions
   - Allow manual override and cancellation of automated responses

3. **Isolation Strategies**
   - Implement granular isolation capabilities (full vs. partial)
   - Consider business impact when isolating assets
   - Maintain emergency access paths for recovery

4. **Comprehensive Logging**
   - Maintain detailed audit trails of all automated actions
   - Log both successful and failed responses
   - Include timestamps, actors, and results

5. **Fail-Safe Defaults**
   - Design responses to fail safely if they can't complete
   - Verify action completion before considering a threat contained
   - Have fallback mechanisms for critical response failures

## Conclusion

Automated responses significantly enhance security operations by reducing response time and ensuring consistent handling of threats. By implementing a framework that assesses risk, determines appropriate responses, and executes them safely, you can drastically improve your organization's security posture.

In the next section, we'll explore how to test and evaluate our threat detection and prevention system to ensure it's functioning correctly and effectively.

[Previous Section: Alert Management](06-alert-management.md) | [Next Section: Testing and Evaluation](08-testing-and-evaluation.md)
