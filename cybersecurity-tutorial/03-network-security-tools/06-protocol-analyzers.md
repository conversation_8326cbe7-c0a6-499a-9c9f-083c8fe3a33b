# Protocol Analyzers

Protocol analyzers (also known as protocol decoders or dissectors) are tools that parse and interpret network protocol data, enabling deep inspection of network traffic. In this section, we'll implement protocol analyzers in Rust for common protocols such as HTTP, DNS, and TLS.

## Overview

We'll cover:

1. Protocol analysis concepts
2. Implementing an HTTP analyzer
3. Implementing a DNS analyzer
4. Implementing a TLS analyzer
5. Integrating analyzers with the packet capture pipeline
6. Best practices for protocol parsing

## Protocol Analysis Concepts

Protocol analyzers extract structured information from raw packet data, such as:
- HTTP requests and responses
- DNS queries and answers
- TLS handshake details

This enables:
- Application-layer threat detection
- Forensics and troubleshooting
- Performance monitoring

## HTTP Analyzer Example

Let's implement a simple HTTP request parser:

```rust
use std::str;

pub struct HttpRequest {
    pub method: String,
    pub path: String,
    pub version: String,
    pub headers: Vec<(String, String)>,
}

pub fn parse_http_request(data: &[u8]) -> Option<HttpRequest> {
    let text = str::from_utf8(data).ok()?;
    let mut lines = text.lines();
    let request_line = lines.next()?;
    let mut parts = request_line.split_whitespace();
    let method = parts.next()?.to_string();
    let path = parts.next()?.to_string();
    let version = parts.next()?.to_string();
    let mut headers = Vec::new();
    for line in lines {
        if line.is_empty() { break; }
        if let Some((k, v)) = line.split_once(": ") {
            headers.push((k.to_string(), v.to_string()));
        }
    }
    Some(HttpRequest { method, path, version, headers })
}
```

## DNS Analyzer Example

Parsing DNS packets can be done using the `dns-parser` crate:

```rust
use dns_parser::Packet as DnsPacket;

pub fn parse_dns_packet(data: &[u8]) -> Option<DnsPacket> {
    DnsPacket::parse(data).ok()
}
```

## TLS Analyzer Example

For TLS, we can use the `tls-parser` crate to extract handshake information:

```rust
use tls_parser::{parse_tls_plaintext, TlsMessage, TlsExtension};

pub fn parse_tls_handshake(data: &[u8]) {
    if let Ok((_, record)) = parse_tls_plaintext(data) {
        for msg in record.msg { match msg {
            TlsMessage::Handshake(handshake) => {
                println!("TLS Handshake: {:?}", handshake);
            },
            _ => {}
        }}
    }
}
```

## Integrating with the Packet Pipeline

You can add protocol analyzers as processors in your packet pipeline:

```rust
pub struct HttpAnalyzer;
impl PacketProcessor for HttpAnalyzer {
    fn process(&mut self, packet: NetworkPacket) -> Option<NetworkPacket> {
        if packet.dst_port == Some(80) || packet.src_port == Some(80) {
            if let Some(http) = parse_http_request(&packet.data) {
                println!("HTTP Request: {} {}", http.method, http.path);
            }
        }
        Some(packet)
    }
    fn name(&self) -> &str { "HttpAnalyzer" }
}
```

## Best Practices for Protocol Parsing

- Use existing crates for complex protocols
- Validate input to avoid panics or crashes
- Handle partial/incomplete packets gracefully
- Log parsing errors for troubleshooting
- Keep protocol analyzers modular and testable

## Conclusion

In this section, we've implemented protocol analyzers for HTTP, DNS, and TLS in Rust, and shown how to integrate them into a packet processing pipeline. These analyzers enable deep inspection of network traffic for security monitoring and troubleshooting.

In the next section, we'll explore network visualization techniques.

---

🔗 **Previous**: [05-firewall-implementation.md](./05-firewall-implementation.md)

🔗 **Next**: [07-network-visualization.md](./07-network-visualization.md)
