# Alert Management

Alert management is a critical component of any threat detection system. Without proper alert handling, even the most sophisticated detection mechanisms can be rendered ineffective due to alert fatigue or missing critical notifications. In this section, we'll build a robust alert management system in Rust that can effectively prioritize, filter, and route security alerts.

## Overview

We'll cover:

1. Alert classification and prioritization
2. Alert deduplication and correlation
3. Alert routing and notification
4. Alert lifecycle management

## Alert Data Structure

Let's start by defining a comprehensive data structure for security alerts:

```rust
use std::collections::{HashMap, HashSet};
use std::time::{SystemTime, UNIX_EPOCH};
use serde::{Serialize, Deserialize};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum AlertSeverity {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum AlertStatus {
    New,
    Acknowledged,
    InProgress,
    Resolved,
    FalsePositive,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum AlertSource {
    SignatureDetection,
    HeuristicAnalysis,
    BehavioralAnalysis,
    MachineLearning,
    ExternalIntel,
    Manual,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAlert {
    id: String,
    title: String,
    description: String,
    severity: AlertSeverity,
    status: AlertStatus,
    source: AlertSource,
    creation_time: u64,
    update_time: u64,
    affected_assets: Vec<String>,
    tags: HashSet<String>,
    metadata: HashMap<String, String>,
    related_alerts: Vec<String>,
    assigned_to: Option<String>,
    resolution_notes: Option<String>,
}

impl SecurityAlert {
    pub fn new(
        title: String,
        description: String,
        severity: AlertSeverity,
        source: AlertSource,
        affected_assets: Vec<String>,
    ) -> Self {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_else(|_| std::time::Duration::from_secs(0))
            .as_secs();
        
        SecurityAlert {
            id: Uuid::new_v4().to_string(),
            title,
            description,
            severity,
            status: AlertStatus::New,
            source,
            creation_time: current_time,
            update_time: current_time,
            affected_assets,
            tags: HashSet::new(),
            metadata: HashMap::new(),
            related_alerts: Vec::new(),
            assigned_to: None,
            resolution_notes: None,
        }
    }
    
    pub fn add_tag(&mut self, tag: &str) {
        self.tags.insert(tag.to_string());
        self.update_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_else(|_| std::time::Duration::from_secs(0))
            .as_secs();
    }
    
    pub fn add_metadata(&mut self, key: &str, value: &str) {
        self.metadata.insert(key.to_string(), value.to_string());
        self.update_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_else(|_| std::time::Duration::from_secs(0))
            .as_secs();
    }
    
    pub fn update_status(&mut self, status: AlertStatus) {
        self.status = status;
        self.update_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_else(|_| std::time::Duration::from_secs(0))
            .as_secs();
    }
    
    pub fn assign_to(&mut self, user: &str) {
        self.assigned_to = Some(user.to_string());
        self.update_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_else(|_| std::time::Duration::from_secs(0))
            .as_secs();
    }
    
    pub fn add_related_alert(&mut self, alert_id: &str) {
        if !self.related_alerts.contains(&alert_id.to_string()) {
            self.related_alerts.push(alert_id.to_string());
            self.update_time = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_else(|_| std::time::Duration::from_secs(0))
                .as_secs();
        }
    }
    
    pub fn resolve(&mut self, notes: &str) {
        self.status = AlertStatus::Resolved;
        self.resolution_notes = Some(notes.to_string());
        self.update_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_else(|_| std::time::Duration::from_secs(0))
            .as_secs();
    }
    
    pub fn mark_false_positive(&mut self, notes: &str) {
        self.status = AlertStatus::FalsePositive;
        self.resolution_notes = Some(notes.to_string());
        self.update_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_else(|_| std::time::Duration::from_secs(0))
            .as_secs();
    }
    
    pub fn priority_score(&self) -> u32 {
        // Calculate a priority score based on severity and other factors
        let severity_score = match self.severity {
            AlertSeverity::Low => 1,
            AlertSeverity::Medium => 10,
            AlertSeverity::High => 50,
            AlertSeverity::Critical => 100,
        };
        
        // Higher score for alerts affecting more assets
        let asset_score = self.affected_assets.len() as u32;
        
        // Higher score for newer alerts
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_else(|_| std::time::Duration::from_secs(0))
            .as_secs();
        let age_hours = (current_time - self.creation_time) / 3600;
        let recency_score = if age_hours < 1 {
            20  // Very recent (< 1 hour)
        } else if age_hours < 24 {
            10  // Recent (< 1 day)
        } else if age_hours < 168 {
            5   // Less than a week old
        } else {
            1   // Older alerts
        };
        
        // Calculate the final score
        severity_score * 10 + asset_score * 2 + recency_score
    }
}
```

## Alert Manager Implementation

Now, let's implement the alert manager to handle various aspects of alert processing:

```rust
use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;

#[derive(Clone)]
pub struct AlertingRule {
    name: String,
    condition: Box<dyn Fn(&SecurityAlert) -> bool + Send + Sync>,
    action: Box<dyn Fn(&SecurityAlert) -> () + Send + Sync>,
}

impl AlertingRule {
    pub fn new(
        name: &str,
        condition: impl Fn(&SecurityAlert) -> bool + Send + Sync + 'static,
        action: impl Fn(&SecurityAlert) -> () + Send + Sync + 'static,
    ) -> Self {
        AlertingRule {
            name: name.to_string(),
            condition: Box::new(condition),
            action: Box::new(action),
        }
    }
    
    pub fn evaluate(&self, alert: &SecurityAlert) -> bool {
        (self.condition)(alert)
    }
    
    pub fn execute(&self, alert: &SecurityAlert) {
        (self.action)(alert);
    }
}

pub struct AlertManager {
    alerts: Arc<Mutex<HashMap<String, SecurityAlert>>>,
    alert_queue: Arc<Mutex<VecDeque<SecurityAlert>>>,
    rules: Arc<Mutex<Vec<AlertingRule>>>,
    notifiers: Arc<Mutex<Vec<Box<dyn AlertNotifier + Send + Sync>>>>,
}

impl AlertManager {
    pub fn new() -> Self {
        AlertManager {
            alerts: Arc::new(Mutex::new(HashMap::new())),
            alert_queue: Arc::new(Mutex::new(VecDeque::new())),
            rules: Arc::new(Mutex::new(Vec::new())),
            notifiers: Arc::new(Mutex::new(Vec::new())),
        }
    }
    
    pub fn add_rule(&mut self, rule: AlertingRule) {
        let mut rules = self.rules.lock().unwrap();
        rules.push(rule);
    }
    
    pub fn register_notifier(&mut self, notifier: Box<dyn AlertNotifier + Send + Sync>) {
        let mut notifiers = self.notifiers.lock().unwrap();
        notifiers.push(notifier);
    }
    
    pub fn process_alert(&self, alert: SecurityAlert) {
        // Add to queue for processing
        let mut queue = self.alert_queue.lock().unwrap();
        queue.push_back(alert);
    }
    
    pub fn get_alert(&self, id: &str) -> Option<SecurityAlert> {
        let alerts = self.alerts.lock().unwrap();
        alerts.get(id).cloned()
    }
    
    pub fn get_alerts_by_status(&self, status: AlertStatus) -> Vec<SecurityAlert> {
        let alerts = self.alerts.lock().unwrap();
        alerts.values()
            .filter(|a| a.status == status)
            .cloned()
            .collect()
    }
    
    pub fn get_alerts_by_severity(&self, severity: AlertSeverity) -> Vec<SecurityAlert> {
        let alerts = self.alerts.lock().unwrap();
        alerts.values()
            .filter(|a| a.severity == severity)
            .cloned()
            .collect()
    }
    
    pub fn update_alert(&self, alert: SecurityAlert) -> Result<(), String> {
        let mut alerts = self.alerts.lock().unwrap();
        
        if !alerts.contains_key(&alert.id) {
            return Err(format!("Alert with ID {} not found", alert.id));
        }
        
        alerts.insert(alert.id.clone(), alert);
        Ok(())
    }
    
    pub fn start_processing_thread(&self) -> thread::JoinHandle<()> {
        let alerts = self.alerts.clone();
        let queue = self.alert_queue.clone();
        let rules = self.rules.clone();
        let notifiers = self.notifiers.clone();
        
        thread::spawn(move || {
            loop {
                // Process any pending alerts
                let alert_option = {
                    let mut queue = queue.lock().unwrap();
                    queue.pop_front()
                };
                
                if let Some(alert) = alert_option {
                    // Check for duplicates or correlate with existing alerts
                    let (is_duplicate, related_alert_ids) = {
                        let alerts_map = alerts.lock().unwrap();
                        let duplicate = alerts_map.values().any(|existing| {
                            // Consider alerts duplicate if they have same title, source, and affected assets
                            // and were created within 5 minutes of each other
                            existing.title == alert.title && 
                            existing.source == alert.source && 
                            existing.affected_assets == alert.affected_assets && 
                            (existing.creation_time - alert.creation_time).abs() < 300 // 5 minutes
                        });
                        
                        // Find related alerts
                        let related = alerts_map.values()
                            .filter(|existing| {
                                // Consider alerts related if they have the same affected assets
                                // but different sources or detection methods
                                existing.affected_assets.iter()
                                    .any(|asset| alert.affected_assets.contains(asset)) &&
                                existing.source != alert.source
                            })
                            .map(|a| a.id.clone())
                            .collect::<Vec<String>>();
                            
                        (duplicate, related)
                    };
                    
                    // If it's a duplicate, skip processing
                    if is_duplicate {
                        println!("Duplicate alert detected, skipping: {}", alert.title);
                        continue;
                    }
                    
                    // Store the new alert
                    let mut new_alert = alert.clone();
                    
                    // Add related alerts
                    for related_id in related_alert_ids {
                        new_alert.add_related_alert(&related_id);
                        
                        // Update the related alert to point back to this one
                        if let Some(mut related_alert) = alerts.lock().unwrap().get(&related_id).cloned() {
                            related_alert.add_related_alert(&new_alert.id);
                            let mut alerts_map = alerts.lock().unwrap();
                            alerts_map.insert(related_id, related_alert);
                        }
                    }
                    
                    // Apply rules
                    let applicable_rules = {
                        let rules_vec = rules.lock().unwrap();
                        rules_vec.iter()
                            .filter(|r| r.evaluate(&new_alert))
                            .cloned()
                            .collect::<Vec<AlertingRule>>()
                    };
                    
                    for rule in applicable_rules {
                        rule.execute(&new_alert);
                    }
                    
                    // Store the alert
                    {
                        let mut alerts_map = alerts.lock().unwrap();
                        alerts_map.insert(new_alert.id.clone(), new_alert.clone());
                    }
                    
                    // Send notifications
                    let notif_list = notifiers.lock().unwrap();
                    for notifier in notif_list.iter() {
                        notifier.send_notification(&new_alert);
                    }
                } else {
                    // No alerts to process, sleep a bit
                    thread::sleep(Duration::from_millis(100));
                }
            }
        })
    }
    
    pub fn start_cleanup_thread(&self, max_age_days: u64) -> thread::JoinHandle<()> {
        let alerts = self.alerts.clone();
        
        thread::spawn(move || {
            loop {
                thread::sleep(Duration::from_secs(3600)); // Run cleanup every hour
                
                let current_time = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap_or_else(|_| std::time::Duration::from_secs(0))
                    .as_secs();
                    
                let max_age_secs = max_age_days * 24 * 3600; // Convert days to seconds
                let mut to_remove = Vec::new();
                
                {
                    let alerts_map = alerts.lock().unwrap();
                    // Find old resolved or false positive alerts
                    for (id, alert) in alerts_map.iter() {
                        if (alert.status == AlertStatus::Resolved || 
                            alert.status == AlertStatus::FalsePositive) && 
                            (current_time - alert.update_time) > max_age_secs {
                            to_remove.push(id.clone());
                        }
                    }
                }
                
                // Remove old alerts
                let mut alerts_map = alerts.lock().unwrap();
                for id in to_remove {
                    alerts_map.remove(&id);
                }
            }
        })
    }
}
```

## Alert Notification Interface

Let's create an interface for different notification methods:

```rust
pub trait AlertNotifier: Send + Sync {
    fn send_notification(&self, alert: &SecurityAlert);
    fn supports_severity(&self, severity: &AlertSeverity) -> bool;
}

// Email notifier implementation
pub struct EmailNotifier {
    smtp_server: String,
    smtp_port: u16,
    from_address: String,
    recipients: Vec<String>,
    min_severity: AlertSeverity,
}

impl EmailNotifier {
    pub fn new(
        smtp_server: &str,
        smtp_port: u16,
        from_address: &str,
        recipients: Vec<String>,
        min_severity: AlertSeverity,
    ) -> Self {
        EmailNotifier {
            smtp_server: smtp_server.to_string(),
            smtp_port,
            from_address: from_address.to_string(),
            recipients,
            min_severity,
        }
    }
}

impl AlertNotifier for EmailNotifier {
    fn send_notification(&self, alert: &SecurityAlert) {
        // Only send notification if severity is >= minimum threshold
        if !self.supports_severity(&alert.severity) {
            return;
        }
        
        println!("Sending email notification to {} recipients for alert: {}", 
            self.recipients.len(), alert.title);
        
        // In a real implementation, this would connect to SMTP and send the email
        // For this tutorial, we'll just print what would happen
        println!("Email from: {}", self.from_address);
        println!("Email to: {}", self.recipients.join(", "));
        println!("Subject: [{}] Security Alert: {}", 
            format!("{:?}", alert.severity), alert.title);
        println!("Body: {}\n\nAffected assets: {}\nAlert ID: {}", 
            alert.description, 
            alert.affected_assets.join(", "), 
            alert.id);
    }
    
    fn supports_severity(&self, severity: &AlertSeverity) -> bool {
        match (&self.min_severity, severity) {
            (AlertSeverity::Critical, AlertSeverity::Critical) => true,
            (AlertSeverity::Critical, _) => false,
            (AlertSeverity::High, AlertSeverity::Critical) |
            (AlertSeverity::High, AlertSeverity::High) => true,
            (AlertSeverity::High, _) => false,
            (AlertSeverity::Medium, AlertSeverity::Low) => false,
            (AlertSeverity::Medium, _) => true,
            (AlertSeverity::Low, _) => true,
        }
    }
}

// Integration with messaging platforms (e.g., Slack)
pub struct SlackNotifier {
    webhook_url: String,
    channel: String,
    min_severity: AlertSeverity,
}

impl SlackNotifier {
    pub fn new(webhook_url: &str, channel: &str, min_severity: AlertSeverity) -> Self {
        SlackNotifier {
            webhook_url: webhook_url.to_string(),
            channel: channel.to_string(),
            min_severity,
        }
    }
}

impl AlertNotifier for SlackNotifier {
    fn send_notification(&self, alert: &SecurityAlert) {
        if !self.supports_severity(&alert.severity) {
            return;
        }
        
        println!("Sending Slack notification to #{} for alert: {}", 
            self.channel, alert.title);
        
        // In a real implementation, this would post to the Slack webhook
        // For this tutorial, we'll just print what would happen
        let severity_emoji = match alert.severity {
            AlertSeverity::Low => ":blue_circle:",
            AlertSeverity::Medium => ":yellow_circle:",
            AlertSeverity::High => ":orange_circle:",
            AlertSeverity::Critical => ":red_circle:",
        };
        
        println!("Slack webhook: {}", self.webhook_url);
        println!("Channel: {}", self.channel);
        println!("Message: {} *{}* - {}\nAffected assets: {}\nAlert ID: {}", 
            severity_emoji,
            alert.title,
            alert.description,
            alert.affected_assets.join(", "),
            alert.id);
    }
    
    fn supports_severity(&self, severity: &AlertSeverity) -> bool {
        match (&self.min_severity, severity) {
            (AlertSeverity::Critical, AlertSeverity::Critical) => true,
            (AlertSeverity::Critical, _) => false,
            (AlertSeverity::High, AlertSeverity::Critical) |
            (AlertSeverity::High, AlertSeverity::High) => true,
            (AlertSeverity::High, _) => false,
            (AlertSeverity::Medium, AlertSeverity::Low) => false,
            (AlertSeverity::Medium, _) => true,
            (AlertSeverity::Low, _) => true,
        }
    }
}

// For system log integration
pub struct SyslogNotifier {
    facility: String,
}

impl SyslogNotifier {
    pub fn new(facility: &str) -> Self {
        SyslogNotifier {
            facility: facility.to_string(),
        }
    }
}

impl AlertNotifier for SyslogNotifier {
    fn send_notification(&self, alert: &SecurityAlert) {
        let priority = match alert.severity {
            AlertSeverity::Critical => "alert",
            AlertSeverity::High => "crit",
            AlertSeverity::Medium => "warning",
            AlertSeverity::Low => "notice",
        };
        
        println!("Logging to syslog with facility {} and priority {}: {}", 
            self.facility, priority, alert.title);
        
        // In a real implementation, this would log to syslog
        // For this tutorial, we'll just print what would happen
        println!("{}:{} [Security Alert] {} - {}", 
            self.facility, 
            priority,
            alert.title,
            alert.description);
    }
    
    fn supports_severity(&self, _severity: &AlertSeverity) -> bool {
        // Syslog handles all severities, we just map them to different priorities
        true
    }
}
```

## Alert Correlation Engine

Let's implement an alert correlation engine to identify potentially related security incidents:

```rust
pub struct AlertCorrelationEngine {
    alert_manager: Arc<AlertManager>,
    correlation_rules: Vec<Box<dyn Fn(&[SecurityAlert]) -> Option<SecurityAlert> + Send + Sync>>,
}

impl AlertCorrelationEngine {
    pub fn new(alert_manager: Arc<AlertManager>) -> Self {
        AlertCorrelationEngine {
            alert_manager,
            correlation_rules: Vec::new(),
        }
    }
    
    pub fn add_correlation_rule(&mut self, rule: Box<dyn Fn(&[SecurityAlert]) -> Option<SecurityAlert> + Send + Sync>) {
        self.correlation_rules.push(rule);
    }
    
    pub fn start_correlation_thread(self, interval_secs: u64) -> thread::JoinHandle<()> {
        thread::spawn(move || {
            loop {
                thread::sleep(Duration::from_secs(interval_secs));
                
                // Get all active alerts
                let active_alerts = self.get_active_alerts();
                
                if active_alerts.is_empty() {
                    continue;
                }
                
                // Apply correlation rules
                for rule in &self.correlation_rules {
                    if let Some(meta_alert) = rule(&active_alerts) {
                        // Process the new meta-alert
                        self.alert_manager.process_alert(meta_alert);
                    }
                }
            }
        })
    }
    
    fn get_active_alerts(&self) -> Vec<SecurityAlert> {
        let mut result = Vec::new();
        
        // Get new alerts
        result.extend(self.alert_manager.get_alerts_by_status(AlertStatus::New));
        
        // Get acknowledged alerts
        result.extend(self.alert_manager.get_alerts_by_status(AlertStatus::Acknowledged));
        
        // Get in-progress alerts
        result.extend(self.alert_manager.get_alerts_by_status(AlertStatus::InProgress));
        
        result
    }
}

// Helper functions to build common correlation rules
pub mod correlation_rules {
    use super::*;
    use std::collections::HashMap;
    
    // Correlate multiple failed logins from different sources to same account
    pub fn brute_force_detection(threshold: usize) 
        -> Box<dyn Fn(&[SecurityAlert]) -> Option<SecurityAlert> + Send + Sync> 
    {
        Box::new(move |alerts| {
            let mut login_attempts_by_account: HashMap<String, Vec<&SecurityAlert>> = HashMap::new();
            
            // Group alerts by target account
            for alert in alerts.iter() {
                if alert.tags.contains("authentication_failure") {
                    if let Some(username) = alert.metadata.get("target_user") {
                        login_attempts_by_account
                            .entry(username.clone())
                            .or_insert_with(Vec::new)
                            .push(alert);
                    }
                }
            }
            
            // Check if any account has exceeded the threshold
            for (username, attempts) in login_attempts_by_account {
                if attempts.len() >= threshold {
                    // Check if attempts are from different sources
                    let sources: HashSet<_> = attempts.iter()
                        .filter_map(|a| a.metadata.get("source_ip"))
                        .collect();
                        
                    if sources.len() >= 3 {  // At least 3 different sources
                        // Create a meta-alert for potential brute force attack
                        let sources_list = sources.iter().cloned().collect::<Vec<_>>();
                        
                        let description = format!(
                            "Potential brute force attack detected against user {}. {} failed login attempts from {} different sources: {}",
                            username,
                            attempts.len(),
                            sources.len(),
                            sources_list.join(", ")
                        );
                        
                        let mut meta_alert = SecurityAlert::new(
                            format!("Potential brute force attack on user {}", username),
                            description,
                            AlertSeverity::High,
                            AlertSource::BehavioralAnalysis,
                            vec![format!("user:{}", username)],
                        );
                        
                        // Add all individual alerts as related
                        for attempt in attempts {
                            meta_alert.add_related_alert(&attempt.id);
                        }
                        
                        meta_alert.add_tag("brute_force");
                        meta_alert.add_tag("correlation");
                        
                        return Some(meta_alert);
                    }
                }
            }
            
            None
        })
    }
    
    // Correlate malware detection with unusual network traffic
    pub fn malware_network_activity_correlation() 
        -> Box<dyn Fn(&[SecurityAlert]) -> Option<SecurityAlert> + Send + Sync> 
    {
        Box::new(|alerts| {
            // Find malware detection alerts
            let malware_alerts: Vec<&SecurityAlert> = alerts.iter()
                .filter(|a| a.tags.contains("malware"))
                .collect();
                
            // Find unusual network alerts
            let network_alerts: Vec<&SecurityAlert> = alerts.iter()
                .filter(|a| a.tags.contains("network") && 
                         (a.tags.contains("unusual_destination") ||
                          a.tags.contains("unusual_protocol") ||
                          a.tags.contains("data_exfiltration")))
                .collect();
                
            if malware_alerts.is_empty() || network_alerts.is_empty() {
                return None;
            }
            
            // Check if any infected host is also making unusual network connections
            for malware_alert in &malware_alerts {
                let infected_hosts: HashSet<_> = malware_alert.affected_assets.iter()
                    .filter(|a| a.starts_with("host:"))
                    .collect();
                    
                for network_alert in &network_alerts {
                    let network_hosts: HashSet<_> = network_alert.affected_assets.iter()
                        .filter(|a| a.starts_with("host:"))
                        .collect();
                        
                    // Check for intersection of hosts
                    let common_hosts: Vec<_> = infected_hosts.intersection(&network_hosts)
                        .collect();
                        
                    if !common_hosts.is_empty() {
                        // Create a meta-alert for potential data exfiltration
                        let host = common_hosts[0].strip_prefix("host:").unwrap_or("");
                        
                        let description = format!(
                            "Host {} has both a malware infection and unusual network activity. This may indicate data exfiltration or command and control traffic.",
                            host
                        );
                        
                        let mut meta_alert = SecurityAlert::new(
                            format!("Potential data exfiltration from infected host {}", host),
                            description,
                            AlertSeverity::Critical,
                            AlertSource::BehavioralAnalysis,
                            vec![format!("host:{}", host)],
                        );
                        
                        // Add individual alerts as related
                        meta_alert.add_related_alert(&malware_alert.id);
                        meta_alert.add_related_alert(&network_alert.id);
                        
                        meta_alert.add_tag("data_exfiltration");
                        meta_alert.add_tag("malware_activity");
                        meta_alert.add_tag("correlation");
                        
                        return Some(meta_alert);
                    }
                }
            }
            
            None
        })
    }
}
```

## Alert Dashboard Interface

Let's implement a simple interface for a dashboard to display alerts:

```rust
pub struct AlertDashboard {
    alert_manager: Arc<AlertManager>,
}

impl AlertDashboard {
    pub fn new(alert_manager: Arc<AlertManager>) -> Self {
        AlertDashboard { alert_manager }
    }
    
    pub fn get_current_alerts(&self) -> AlertSummary {
        let all_alerts = {
            let alerts_map = self.alert_manager.alerts.lock().unwrap();
            alerts_map.values().cloned().collect::<Vec<SecurityAlert>>()
        };
        
        let mut summary = AlertSummary::new();
        
        for alert in all_alerts {
            match alert.status {
                AlertStatus::New => {
                    summary.new_alerts += 1;
                    match alert.severity {
                        AlertSeverity::Critical => summary.critical_alerts += 1,
                        AlertSeverity::High => summary.high_alerts += 1,
                        AlertSeverity::Medium => summary.medium_alerts += 1,
                        AlertSeverity::Low => summary.low_alerts += 1,
                    }
                },
                AlertStatus::Acknowledged => summary.acknowledged_alerts += 1,
                AlertStatus::InProgress => summary.in_progress_alerts += 1,
                AlertStatus::Resolved => summary.resolved_alerts += 1,
                AlertStatus::FalsePositive => summary.false_positive_alerts += 1,
            }
        }
        
        summary
    }
    
    pub fn get_top_alerts(&self, limit: usize) -> Vec<SecurityAlert> {
        let all_alerts = {
            let alerts_map = self.alert_manager.alerts.lock().unwrap();
            alerts_map.values().cloned().collect::<Vec<SecurityAlert>>()
        };
        
        // Only include new or acknowledged alerts
        let mut active_alerts: Vec<SecurityAlert> = all_alerts.into_iter()
            .filter(|a| a.status == AlertStatus::New || a.status == AlertStatus::Acknowledged)
            .collect();
            
        // Sort by priority score (highest first)
        active_alerts.sort_by(|a, b| b.priority_score().cmp(&a.priority_score()));
        
        // Take only the requested number
        active_alerts.into_iter().take(limit).collect()
    }
    
    pub fn get_affected_assets(&self) -> HashMap<String, u32> {
        let all_alerts = {
            let alerts_map = self.alert_manager.alerts.lock().unwrap();
            alerts_map.values().cloned().collect::<Vec<SecurityAlert>>()
        };
        
        let mut asset_counts: HashMap<String, u32> = HashMap::new();
        
        for alert in all_alerts {
            // Only count active alerts
            if alert.status == AlertStatus::New || 
               alert.status == AlertStatus::Acknowledged ||
               alert.status == AlertStatus::InProgress {
                for asset in &alert.affected_assets {
                    *asset_counts.entry(asset.clone()).or_insert(0) += 1;
                }
            }
        }
        
        asset_counts
    }
    
    pub fn get_alert_sources(&self) -> HashMap<AlertSource, u32> {
        let all_alerts = {
            let alerts_map = self.alert_manager.alerts.lock().unwrap();
            alerts_map.values().cloned().collect::<Vec<SecurityAlert>>()
        };
        
        let mut source_counts: HashMap<AlertSource, u32> = HashMap::new();
        
        for alert in all_alerts {
            // Only count active alerts
            if alert.status == AlertStatus::New || 
               alert.status == AlertStatus::Acknowledged ||
               alert.status == AlertStatus::InProgress {
                *source_counts.entry(alert.source.clone()).or_insert(0) += 1;
            }
        }
        
        source_counts
    }
}

#[derive(Debug, Clone)]
pub struct AlertSummary {
    pub new_alerts: u32,
    pub acknowledged_alerts: u32,
    pub in_progress_alerts: u32,
    pub resolved_alerts: u32,
    pub false_positive_alerts: u32,
    pub critical_alerts: u32,
    pub high_alerts: u32,
    pub medium_alerts: u32,
    pub low_alerts: u32,
}

impl AlertSummary {
    pub fn new() -> Self {
        AlertSummary {
            new_alerts: 0,
            acknowledged_alerts: 0,
            in_progress_alerts: 0,
            resolved_alerts: 0,
            false_positive_alerts: 0,
            critical_alerts: 0,
            high_alerts: 0,
            medium_alerts: 0,
            low_alerts: 0,
        }
    }
    
    pub fn total_active_alerts(&self) -> u32 {
        self.new_alerts + self.acknowledged_alerts + self.in_progress_alerts
    }
}
```

## Putting It All Together

Let's create a comprehensive example showing how all parts work together:

```rust
fn main() {
    println!("Initializing Alert Management System");
    
    // Create the alert manager
    let alert_manager = Arc::new(AlertManager::new());
    
    // Register notification handlers
    {
        let mut manager = AlertManager::new();
        
        // Email notifications for high and critical alerts
        manager.register_notifier(Box::new(EmailNotifier::new(
            "smtp.example.com", 
            25,
            "<EMAIL>",
            vec!["<EMAIL>".to_string()],
            AlertSeverity::High
        )));
        
        // Slack notifications for medium and above
        manager.register_notifier(Box::new(SlackNotifier::new(
            "https://hooks.slack.com/services/*********/*********/XXXXXXXXXXXXXXXXXXXXXXXX",
            "security-alerts",
            AlertSeverity::Medium
        )));
        
        // Syslog for all alerts
        manager.register_notifier(Box::new(SyslogNotifier::new("security")));
        
        *alert_manager = Arc::new(manager);
    }
    
    // Add alerting rules
    {
        let mut manager = AlertManager::new();
        
        // Auto-assign critical alerts to the security team
        manager.add_rule(AlertingRule::new(
            "critical-auto-assign",
            |alert| alert.severity == AlertSeverity::Critical,
            |alert| println!("Auto-assigning critical alert {} to security team", alert.id)
        ));
        
        // Auto-tag alerts based on content
        manager.add_rule(AlertingRule::new(
            "ransomware-tagger",
            |alert| {
                alert.description.to_lowercase().contains("ransomware") ||
                alert.description.to_lowercase().contains("encryption") ||
                alert.description.to_lowercase().contains("ransom")
            },
            |alert| println!("Tagging alert {} with 'ransomware' tag", alert.id)
        ));
        
        *alert_manager = Arc::new(manager);
    }
    
    // Create correlation engine
    let mut correlation_engine = AlertCorrelationEngine::new(alert_manager.clone());
    
    // Add correlation rules
    correlation_engine.add_correlation_rule(correlation_rules::brute_force_detection(5));
    correlation_engine.add_correlation_rule(correlation_rules::malware_network_activity_correlation());
    
    // Create dashboard
    let dashboard = AlertDashboard::new(alert_manager.clone());
    
    // Start processing threads
    let _processing_thread = alert_manager.start_processing_thread();
    let _cleanup_thread = alert_manager.start_cleanup_thread(30); // Keep alerts for 30 days
    let _correlation_thread = correlation_engine.start_correlation_thread(60); // Check for correlations every minute
    
    // Simulate some alerts
    println!("Simulating security alerts...");
    
    // Malware detection
    let malware_alert = SecurityAlert::new(
        "Malware detected: Trojan.GenericKD".to_string(),
        "Malicious software detected by signature matching".to_string(),
        AlertSeverity::High,
        AlertSource::SignatureDetection,
        vec!["host:workstation1".to_string(), "file:/tmp/suspicious.exe".to_string()]
    );
    alert_manager.process_alert(malware_alert);
    
    // Suspicious network traffic from same host
    let network_alert = SecurityAlert::new(
        "Unusual outbound connection detected".to_string(),
        "Host established connection to known C2 server".to_string(),
        AlertSeverity::Medium,
        AlertSource::BehavioralAnalysis,
        vec!["host:workstation1".to_string(), "connection:***********:443".to_string()]
    );
    alert_manager.process_alert(network_alert);
    
    // Failed logins (to trigger brute force correlation)
    for i in 0..5 {
        let mut login_alert = SecurityAlert::new(
            "Failed authentication attempt".to_string(),
            format!("Failed login attempt for user admin from IP 192.168.1.{}", 10 + i),
            AlertSeverity::Low,
            AlertSource::Manual,
            vec!["user:admin".to_string()]
        );
        
        login_alert.add_tag("authentication_failure");
        login_alert.add_metadata("target_user", "admin");
        login_alert.add_metadata("source_ip", &format!("192.168.1.{}", 10 + i));
        
        alert_manager.process_alert(login_alert);
    }
    
    // Give time for alerts to be processed
    thread::sleep(Duration::from_secs(2));
    
    // Display summary
    let summary = dashboard.get_current_alerts();
    println!("\nAlert Summary:");
    println!("-------------");
    println!("New Alerts: {} (Critical: {}, High: {}, Medium: {}, Low: {})",
        summary.new_alerts,
        summary.critical_alerts,
        summary.high_alerts,
        summary.medium_alerts,
        summary.low_alerts
    );
    println!("Acknowledged: {}", summary.acknowledged_alerts);
    println!("In Progress: {}", summary.in_progress_alerts);
    println!("Resolved: {}", summary.resolved_alerts);
    println!("False Positives: {}", summary.false_positive_alerts);
    
    // Display top alerts
    println!("\nTop Alerts:");
    println!("-----------");
    for (i, alert) in dashboard.get_top_alerts(5).iter().enumerate() {
        println!("{}. [{}] {} - {}", 
            i + 1, 
            format!("{:?}", alert.severity), 
            alert.title,
            alert.description
        );
    }
    
    // Display affected assets
    println!("\nMost Affected Assets:");
    println!("--------------------");
    let mut assets = dashboard.get_affected_assets().into_iter().collect::<Vec<_>>();
    assets.sort_by(|a, b| b.1.cmp(&a.1));
    for (asset, count) in assets.iter().take(3) {
        println!("{}: {} alerts", asset, count);
    }
    
    println!("\nAlert management system running...");
}
```

## Key Concepts and Best Practices

1. **Alert Prioritization**
   - Assign severity based on potential impact
   - Consider asset criticality when prioritizing
   - Use dynamic scoring that accounts for multiple factors

2. **Alert Reduction Strategies**
   - Implement deduplication to avoid alert fatigue
   - Use correlation to group related alerts
   - Filter out known false positives

3. **Alert Routing**
   - Route alerts to appropriate teams based on type/severity
   - Implement escalation paths for unhandled alerts
   - Use different notification channels based on urgency

4. **Alert Lifecycle Management**
   - Track alerts through their complete lifecycle
   - Maintain audit trail of alert handling actions
   - Archive resolved alerts for historical analysis

5. **Integration with Response Workflows**
   - Link alerts to incident response procedures
   - Enable automated response actions for common alerts
   - Provide context and recommendations to analysts

## Conclusion

A robust alert management system is crucial for making sense of the data generated by your threat detection mechanisms. By properly classifying, correlating, and routing alerts, security teams can focus on the most critical issues and reduce the time to respond to genuine threats.

In the next section, we'll explore how to implement automated responses to security incidents, allowing the system to take immediate action on certain types of threats without requiring human intervention.

[Previous Section: Machine Learning Integration](05-machine-learning.md) | [Next Section: Automated Responses](07-automated-responses.md)
