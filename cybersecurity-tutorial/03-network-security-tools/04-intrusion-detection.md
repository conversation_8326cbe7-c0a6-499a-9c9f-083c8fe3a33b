# Network Intrusion Detection

Intrusion Detection Systems (IDS) are essential for identifying malicious or suspicious activity within network traffic. In this section, we'll build a basic network IDS in Rust, focusing on both signature-based and anomaly-based detection methods.

## Overview

We'll cover:

1. IDS concepts and architecture
2. Implementing signature-based detection
3. Implementing anomaly-based detection
4. Alerting and logging mechanisms
5. Integrating IDS with the traffic analysis engine
6. Best practices and limitations

## IDS Concepts and Architecture

An IDS monitors network traffic for signs of attacks or policy violations. There are two main types:

- **Signature-based IDS**: Detects known threats using predefined patterns (signatures)
- **Anomaly-based IDS**: Detects deviations from normal behavior

A typical IDS architecture includes:
- Packet capture and preprocessing
- Detection engine (signatures, heuristics, ML)
- Alerting and logging
- Management interface

## Signature-Based Detection in Rust

Let's define a simple signature structure and detection engine:

```rust
use std::collections::HashMap;
use regex::Regex;
use crate::network::models::NetworkPacket;

#[derive(Debug, Clone)]
pub struct Signature {
    pub name: String,
    pub pattern: Regex, // e.g., regex for payload or header
    pub protocol: Option<u8>, // TCP=6, UDP=17, etc.
    pub src_port: Option<u16>,
    pub dst_port: Option<u16>,
}

pub struct SignatureEngine {
    signatures: Vec<Signature>,
}

impl SignatureEngine {
    pub fn new() -> Self {
        SignatureEngine { signatures: Vec::new() }
    }
    pub fn add_signature(&mut self, sig: Signature) {
        self.signatures.push(sig);
    }
    pub fn check_packet(&self, packet: &NetworkPacket) -> Option<&Signature> {
        for sig in &self.signatures {
            if let Some(proto) = sig.protocol {
                if packet.protocol as u8 != proto { continue; }
            }
            if let Some(port) = sig.src_port {
                if packet.src_port != Some(port) { continue; }
            }
            if let Some(port) = sig.dst_port {
                if packet.dst_port != Some(port) { continue; }
            }
            if let Ok(payload) = std::str::from_utf8(&packet.data) {
                if sig.pattern.is_match(payload) {
                    return Some(sig);
                }
            }
        }
        None
    }
}
```

## Anomaly-Based Detection

Anomaly detection flags traffic that deviates from established baselines. We'll use simple statistical thresholds for demonstration:

```rust
use crate::network::tools::traffic_analysis::TrafficAnalyzer;

pub struct AnomalyEngine {
    pub byte_threshold: u64,
    pub rate_threshold: f64,
}

impl AnomalyEngine {
    pub fn detect(&self, analyzer: &TrafficAnalyzer) -> Vec<String> {
        let flows = analyzer.flows.lock().unwrap();
        flows.values()
            .filter(|flow| flow.byte_count > self.byte_threshold || flow.avg_interarrival() < self.rate_threshold)
            .map(|flow| format!("Anomaly: {:?}", flow))
            .collect()
    }
}
```

## Alerting and Logging

Let's define a simple alert structure and logger:

```rust
use chrono::{DateTime, Utc};

#[derive(Debug, Clone)]
pub struct Alert {
    pub timestamp: DateTime<Utc>,
    pub alert_type: String,
    pub description: String,
    pub packet_summary: String,
}

pub struct AlertLogger {
    pub alerts: Vec<Alert>,
}

impl AlertLogger {
    pub fn new() -> Self { AlertLogger { alerts: Vec::new() } }
    pub fn log(&mut self, alert: Alert) {
        println!("[ALERT] {}: {} - {}", alert.timestamp, alert.alert_type, alert.description);
        self.alerts.push(alert);
    }
}
```

## Integrating IDS with Traffic Analysis

Here's how you might use the IDS components in your main program:

```rust
fn main() {
    // ... set up packet capture and traffic analyzer ...
    let mut sig_engine = SignatureEngine::new();
    sig_engine.add_signature(Signature {
        name: "Example HTTP Exploit".to_string(),
        pattern: Regex::new(r"/etc/passwd").unwrap(),
        protocol: Some(6), // TCP
        src_port: None,
        dst_port: Some(80),
    });
    let anomaly_engine = AnomalyEngine { byte_threshold: 10_000_000, rate_threshold: 0.001 };
    let mut logger = AlertLogger::new();
    // ...
    capture.add_handler(move |packet| {
        if let Some(sig) = sig_engine.check_packet(packet) {
            logger.log(Alert {
                timestamp: Utc::now(),
                alert_type: "Signature Match".to_string(),
                description: format!("Matched signature: {}", sig.name),
                packet_summary: packet.summary(),
            });
        }
        // ...
    });
    // After capture, run anomaly detection:
    for desc in anomaly_engine.detect(&analyzer) {
        logger.log(Alert {
            timestamp: Utc::now(),
            alert_type: "Anomaly".to_string(),
            description: desc,
            packet_summary: String::new(),
        });
    }
}
```

## Best Practices and Limitations

- Regularly update signatures for new threats
- Tune anomaly thresholds to minimize false positives
- Combine multiple detection methods for better coverage
- Log alerts with sufficient detail for investigation
- Understand that IDS can be evaded by advanced attackers

## Conclusion

In this section, we've implemented a basic network IDS in Rust, including:

1. Signature-based detection using regex patterns
2. Anomaly-based detection using traffic statistics
3. Alerting and logging mechanisms
4. Integration with the traffic analysis engine

In the next section, we'll explore how to implement basic firewall functionality in Rust.

---

🔗 **Previous**: [03-traffic-analysis.md](./03-traffic-analysis.md)

🔗 **Next**: [05-firewall-implementation.md](./05-firewall-implementation.md)
