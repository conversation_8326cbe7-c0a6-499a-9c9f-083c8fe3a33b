# WAF Implementation in Rust

In this section, we'll implement basic Web Application Firewall (WAF) features in Rust, including HTTP request inspection, rule-based filtering, and logging.

## Overview

We'll cover:

1. HTTP request parsing
2. Rule-based filtering (signatures, regex)
3. Logging and alerting
4. Blocking and monitoring modes
5. Example WAF workflow

## HTTP Request Parsing

Example: Parse HTTP requests for inspection

```rust
pub struct HttpRequest {
    pub method: String,
    pub path: String,
    pub headers: Vec<(String, String)>,
    pub body: String,
}

pub fn parse_http_request(raw: &str) -> Option<HttpRequest> {
    let mut lines = raw.lines();
    let request_line = lines.next()?;
    let mut parts = request_line.split_whitespace();
    let method = parts.next()?.to_string();
    let path = parts.next()?.to_string();
    let mut headers = Vec::new();
    let mut body = String::new();
    let mut in_body = false;
    for line in lines {
        if line.is_empty() { in_body = true; continue; }
        if in_body {
            body.push_str(line);
            body.push('\n');
        } else if let Some((k, v)) = line.split_once(": ") {
            headers.push((k.to_string(), v.to_string()));
        }
    }
    Some(HttpRequest { method, path, headers, body })
}
```

## Rule-Based Filtering

Example: Block requests with SQL injection patterns

```rust
pub fn is_sql_injection(request: &HttpRequest) -> bool {
    let patterns = ["' OR 1=1", "--", "UNION SELECT", "xp_cmdshell"];
    let target = format!("{} {} {}", request.path, request.body, request.headers.iter().map(|(k,v)| format!("{}:{}",k,v)).collect::<String>());
    patterns.iter().any(|p| target.to_lowercase().contains(&p.to_lowercase()))
}
```

## Logging and Alerting

```rust
use chrono::Utc;

pub fn log_waf_event(event: &str, path: &str) {
    println!("[{}] {}: {}", Utc::now(), path, event);
}
```

## Blocking and Monitoring Modes

- **Blocking**: Drop malicious requests
- **Monitoring**: Log but allow requests

## Example WAF Workflow

```rust
fn main() {
    let raw_request = "POST /login HTTP/1.1\r\nHost: example.com\r\n\r\nusername=admin' OR 1=1";
    if let Some(request) = parse_http_request(raw_request) {
        if is_sql_injection(&request) {
            log_waf_event("Blocked SQL injection", &request.path);
        } else {
            log_waf_event("Allowed request", &request.path);
        }
    }
}
```

## Conclusion

We've implemented basic WAF features in Rust. In the next section, we'll explore advanced WAF techniques and integration.

---

🔗 **Previous**: [01-waf-concepts.md](./01-waf-concepts.md)

🔗 **Next**: [03-advanced-waf.md](./03-advanced-waf.md)
