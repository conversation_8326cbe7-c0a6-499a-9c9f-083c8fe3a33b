# File System Monitoring

In this section, we'll implement the file system monitoring component of our Endpoint Protection plugin. File system monitoring is crucial for detecting potential threats like ransomware, malware installation, and data exfiltration.

## Understanding File System Events

Before we implement our file monitor, let's understand the types of file system events we'll be monitoring:

1. **Create Events**: Files or directories being created
2. **Modify Events**: Content of files being changed
3. **Delete Events**: Files or directories being deleted
4. **Rename Events**: Files or directories being renamed
5. **Attribute Change Events**: Permissions or other attributes being modified

## Implementing the File Monitor

We'll use the `notify` crate to monitor the file system for events. This library provides a cross-platform solution for file system monitoring.

Let's update our `file_monitor.rs` implementation:

**src/plugins/endpoint_protection/file_monitor.rs**:

```rust
//! File system monitoring for the Endpoint Protection plugin

use anyhow::{Context, Result};
use crossbeam_channel::{bounded, Receiver, Sender};
use dashmap::DashMap;
use log::{debug, error, info, warn};
use notify::{Config, Event, EventKind, RecommendedWatcher, RecursiveMode, Watcher};
use sha2::{Digest, Sha256};
use std::collections::HashSet;
use std::fs;
use std::io::Read;
use std::path::{Path, PathBuf};
use std::sync::{Arc, RwLock};
use std::thread;
use std::time::{Duration, Instant};

use super::config::FileMonitoringConfig;

/// Represents a file system event with additional context
#[derive(Debug, Clone)]
pub struct FileEvent {
    /// Path to the file that triggered the event
    pub path: PathBuf,
    /// Type of event (create, modify, delete, etc.)
    pub event_type: FileEventType,
    /// Time when the event was detected
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// SHA-256 hash of the file, if available
    pub file_hash: Option<String>,
    /// Size of the file in bytes
    pub file_size: Option<u64>,
}

/// Types of file system events
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum FileEventType {
    /// File or directory created
    Create,
    /// File modified
    Modify,
    /// File or directory deleted
    Delete,
    /// File or directory renamed
    Rename { old_path: PathBuf },
    /// File attributes changed
    AttributeChange,
    /// Other event
    Other,
}

/// Handler for file system events
pub trait FileEventHandler: Send + Sync {
    /// Called when a file event is detected
    fn handle_event(&self, event: FileEvent);
}

/// Simple file event handler that logs events
#[derive(Debug)]
pub struct LoggingEventHandler;

impl FileEventHandler for LoggingEventHandler {
    fn handle_event(&self, event: FileEvent) {
        match event.event_type {
            FileEventType::Create => {
                info!(
                    "File created: {} ({} bytes)",
                    event.path.display(),
                    event.file_size.unwrap_or(0)
                );
                if let Some(hash) = &event.file_hash {
                    debug!("File hash: {}", hash);
                }
            }
            FileEventType::Modify => {
                info!(
                    "File modified: {} ({} bytes)",
                    event.path.display(),
                    event.file_size.unwrap_or(0)
                );
                if let Some(hash) = &event.file_hash {
                    debug!("File hash: {}", hash);
                }
            }
            FileEventType::Delete => {
                info!("File deleted: {}", event.path.display());
            }
            FileEventType::Rename { old_path } => {
                info!(
                    "File renamed: {} -> {}",
                    old_path.display(),
                    event.path.display()
                );
            }
            FileEventType::AttributeChange => {
                info!("File attributes changed: {}", event.path.display());
            }
            FileEventType::Other => {
                debug!("Other file event: {}", event.path.display());
            }
        }
    }
}

/// Monitors the file system for suspicious activity
#[derive(Debug)]
pub struct FileMonitor {
    /// Configuration for file monitoring
    config: Arc<RwLock<FileMonitoringConfig>>,
    /// Channel for sending stop signals to the monitoring thread
    stop_tx: Option<Sender<()>>,
    /// Set of active watchers
    #[allow(dead_code)]  // Will be used when we implement dynamic reconfiguration
    watchers: Arc<DashMap<PathBuf, RecommendedWatcher>>,
    /// Event handlers
    event_handlers: Arc<Vec<Box<dyn FileEventHandler>>>,
    /// Thread handle for the monitoring thread
    monitor_thread: Option<thread::JoinHandle<()>>,
    /// Recently processed files to avoid duplicate events
    recent_events: Arc<DashMap<PathBuf, Instant>>,
}

impl FileMonitor {
    /// Creates a new file monitor with the given configuration
    pub fn new(
        config: FileMonitoringConfig,
        event_handlers: Vec<Box<dyn FileEventHandler>>,
    ) -> Result<Self> {
        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            stop_tx: None,
            watchers: Arc::new(DashMap::new()),
            event_handlers: Arc::new(event_handlers),
            monitor_thread: None,
            recent_events: Arc::new(DashMap::new()),
        })
    }
    
    /// Starts the file monitor
    pub fn start(&mut self) -> Result<()> {
        if self.monitor_thread.is_some() {
            warn!("File monitor is already running");
            return Ok(());
        }
        
        let config = match self.config.read() {
            Ok(config) => config.clone(),
            Err(e) => {
                error!("Failed to read configuration: {}", e);
                return Err(anyhow::anyhow!("Failed to read configuration"));
            }
        };
        
        if !config.enabled {
            info!("File monitoring is disabled in configuration");
            return Ok(());
        }
        
        // Create a channel for receiving file system events
        let (event_tx, event_rx) = bounded(1000);
        
        // Create a channel for stopping the monitoring thread
        let (stop_tx, stop_rx) = bounded(1);
        self.stop_tx = Some(stop_tx);
        
        // Initialize watchers for all configured paths
        let watchers = self.watchers.clone();
        let extensions = config.extensions.clone();
        let max_file_size = config.max_file_size;
        
        for path in &config.paths {
            let path = PathBuf::from(path);
            
            if !path.exists() {
                warn!("Path does not exist: {}", path.display());
                continue;
            }
            
            let event_tx = event_tx.clone();
            let mut watcher = match RecommendedWatcher::new(
                move |result| {
                    match result {
                        Ok(event) => {
                            if let Err(e) = event_tx.send(event) {
                                error!("Failed to send file system event: {}", e);
                            }
                        }
                        Err(e) => error!("File system watcher error: {}", e),
                    }
                },
                Config::default(),
            ) {
                Ok(watcher) => watcher,
                Err(e) => {
                    error!("Failed to create file system watcher: {}", e);
                    continue;
                }
            };
            
            let recursive_mode = if config.recursive {
                RecursiveMode::Recursive
            } else {
                RecursiveMode::NonRecursive
            };
            
            match watcher.watch(&path, recursive_mode) {
                Ok(()) => {
                    info!("Watching path: {}", path.display());
                    watchers.insert(path.clone(), watcher);
                }
                Err(e) => {
                    error!("Failed to watch path {}: {}", path.display(), e);
                }
            }
        }
        
        // Start a thread to process file system events
        let event_handlers = self.event_handlers.clone();
        let recent_events = self.recent_events.clone();
        let monitor_creates = config.monitor_creates;
        let monitor_modifies = config.monitor_modifies;
        let monitor_deletes = config.monitor_deletes;
        
        let monitor_thread = thread::spawn(move || {
            info!("File monitor thread started");
            
            loop {
                crossbeam_channel::select! {
                    recv(event_rx) -> result => {
                        match result {
                            Ok(event) => {
                                Self::process_event(
                                    event,
                                    &event_handlers,
                                    &recent_events,
                                    &extensions,
                                    max_file_size,
                                    monitor_creates,
                                    monitor_modifies,
                                    monitor_deletes,
                                );
                            }
                            Err(e) => {
                                error!("Error receiving file system event: {}", e);
                                break;
                            }
                        }
                    }
                    recv(stop_rx) -> _ => {
                        info!("Received stop signal, shutting down file monitor");
                        break;
                    }
                }
                
                // Clean up old events from the recent_events map
                let now = Instant::now();
                recent_events.retain(|_, timestamp| {
                    now.duration_since(*timestamp) < Duration::from_secs(1)
                });
            }
            
            info!("File monitor thread stopped");
        });
        
        self.monitor_thread = Some(monitor_thread);
        info!("File monitor started");
        
        Ok(())
    }
    
    /// Processes a file system event
    fn process_event(
        event: Event,
        event_handlers: &Arc<Vec<Box<dyn FileEventHandler>>>,
        recent_events: &Arc<DashMap<PathBuf, Instant>>,
        extensions: &[String],
        max_file_size: u64,
        monitor_creates: bool,
        monitor_modifies: bool,
        monitor_deletes: bool,
    ) {
        // Extract the path and event kind
        let paths = event.paths;
        let event_kind = event.kind;
        
        // Process all paths in the event (usually just one)
        for path in paths {
            // Check if we've recently processed this path
            if recent_events.contains_key(&path) {
                continue;
            }
            
            // Record that we've processed this path
            recent_events.insert(path.clone(), Instant::now());
            
            // Check file extension if path has one and we have a filter
            if let Some(extension) = path.extension() {
                if !extensions.is_empty() && !extensions.iter().any(|ext| {
                    extension.to_string_lossy().eq_ignore_ascii_case(ext)
                }) {
                    continue;
                }
            }
            
            // Determine the event type
            let event_type = match event_kind {
                EventKind::Create(_) => {
                    if !monitor_creates {
                        continue;
                    }
                    FileEventType::Create
                }
                EventKind::Modify(_) => {
                    if !monitor_modifies {
                        continue;
                    }
                    FileEventType::Modify
                }
                EventKind::Remove(_) => {
                    if !monitor_deletes {
                        continue;
                    }
                    FileEventType::Delete
                }
                EventKind::Rename(rename_event) => {
                    FileEventType::Rename {
                        old_path: rename_event.from.clone().unwrap_or_else(|| PathBuf::from("unknown")),
                    }
                }
                EventKind::Access(_) => {
                    // File access events are very frequent and usually not useful for security monitoring
                    continue;
                }
                _ => FileEventType::Other,
            };
            
            // Get file info if the file exists
            let (file_size, file_hash) = if path.exists() && path.is_file() {
                match fs::metadata(&path) {
                    Ok(metadata) => {
                        let size = metadata.len();
                        let hash = if size <= max_file_size {
                            Self::compute_file_hash(&path).ok()
                        } else {
                            None
                        };
                        (Some(size), hash)
                    }
                    Err(_) => (None, None),
                }
            } else {
                (None, None)
            };
            
            // Create and dispatch the event
            let file_event = FileEvent {
                path: path.clone(),
                event_type,
                timestamp: chrono::Utc::now(),
                file_hash,
                file_size,
            };
            
            for handler in event_handlers.iter() {
                handler.handle_event(file_event.clone());
            }
        }
    }
    
    /// Computes the SHA-256 hash of a file
    fn compute_file_hash<P: AsRef<Path>>(path: P) -> Result<String> {
        let path = path.as_ref();
        
        let mut file = fs::File::open(path)
            .with_context(|| format!("Failed to open file for hashing: {}", path.display()))?;
            
        let mut hasher = Sha256::new();
        let mut buffer = [0; 8192];
        
        loop {
            let bytes_read = file.read(&mut buffer)
                .with_context(|| format!("Failed to read file for hashing: {}", path.display()))?;
                
            if bytes_read == 0 {
                break;
            }
            
            hasher.update(&buffer[..bytes_read]);
        }
        
        let hash = hasher.finalize();
        let hash_str = format!("{:x}", hash);
        
        Ok(hash_str)
    }
    
    /// Stops the file monitor
    pub fn stop(&mut self) -> Result<()> {
        if let Some(stop_tx) = self.stop_tx.take() {
            if let Err(e) = stop_tx.send(()) {
                error!("Failed to send stop signal: {}", e);
            }
            
            if let Some(thread) = self.monitor_thread.take() {
                match thread.join() {
                    Ok(_) => {
                        info!("File monitor thread joined successfully");
                    }
                    Err(e) => {
                        error!("Failed to join file monitor thread: {:?}", e);
                    }
                }
            }
            
            // Clear all watchers to stop watching
            self.watchers.clear();
        }
        
        Ok(())
    }
}

/// Creates a new file monitor with default configuration
pub fn create_default_file_monitor(config: FileMonitoringConfig) -> Result<FileMonitor> {
    let mut handlers: Vec<Box<dyn FileEventHandler>> = Vec::new();
    handlers.push(Box::new(LoggingEventHandler));
    
    FileMonitor::new(config, handlers)
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Mutex;
    use tempfile::tempdir;
    
    /// A mock event handler that captures events for testing
    struct MockEventHandler {
        events: Arc<Mutex<Vec<FileEvent>>>,
    }
    
    impl MockEventHandler {
        fn new() -> (Self, Arc<Mutex<Vec<FileEvent>>>) {
            let events = Arc::new(Mutex::new(Vec::new()));
            (Self { events: events.clone() }, events)
        }
    }
    
    impl FileEventHandler for MockEventHandler {
        fn handle_event(&self, event: FileEvent) {
            let mut events = self.events.lock().unwrap();
            events.push(event);
        }
    }
    
    #[test]
    fn test_file_hash() {
        let dir = tempdir().unwrap();
        let file_path = dir.path().join("test.txt");
        
        // Create a test file with known content
        fs::write(&file_path, b"test content").unwrap();
        
        // Compute hash
        let hash = FileMonitor::compute_file_hash(&file_path).unwrap();
        
        // The SHA-256 hash of "test content" is known
        let expected_hash = "6ae8a75555209fd6c44157c0aed8016e763ff435a19cf186f76863140143ff72";
        
        assert_eq!(hash, expected_hash);
    }
}
```

## Updating the Plugin Implementation

Now, let's update the `EndpointProtectionPlugin` to use our new `FileMonitor` implementation:

**src/plugins/endpoint_protection/mod.rs** (partial update):

```rust
// ... existing imports ...
use std::sync::{Arc, Mutex, RwLock};
// ... existing imports ...

use self::file_monitor::{create_default_file_monitor, FileMonitor};
// ... other imports ...

/// Endpoint Protection plugin for CyberShield
#[derive(Debug)]
pub struct EndpointProtectionPlugin {
    name: String,
    version: String,
    description: String,
    config: Arc<RwLock<EndpointProtectionConfig>>,
    file_monitor: Option<Arc<Mutex<FileMonitor>>>,
    process_monitor: Option<Arc<ProcessMonitor>>,
    threat_detector: Option<Arc<ThreatDetector>>,
    system_health_monitor: Option<Arc<SystemHealthMonitor>>,
}

impl EndpointProtectionPlugin {
    // ... existing methods ...

    /// Stops all monitoring components
    pub fn stop_all(&mut self) -> Result<()> {
        info!("Stopping all endpoint protection components");
        
        // Stop file monitoring
        if let Some(monitor) = &self.file_monitor {
            if let Ok(mut monitor) = monitor.lock() {
                monitor.stop()?;
            } else {
                warn!("Failed to acquire lock on file monitor");
            }
        }
        
        // ... other monitors ...
        
        Ok(())
    }
}

impl Plugin for EndpointProtectionPlugin {
    // ... existing methods ...
    
    fn initialize(&self) -> Result<()> {
        info!("Initializing Endpoint Protection plugin");
        
        // Access configuration in a thread-safe way
        let config = match self.config.read() {
            Ok(config) => config.clone(),
            Err(e) => {
                warn!("Failed to read configuration: {}", e);
                return Err(anyhow::anyhow!("Failed to read configuration"));
            }
        };
        
        // Initialize file monitoring if enabled
        if config.file_monitoring.enabled {
            debug!("File monitoring is enabled");
            
            // Create and start a file monitor
            match create_default_file_monitor(config.file_monitoring.clone()) {
                Ok(mut monitor) => {
                    if let Err(e) = monitor.start() {
                        warn!("Failed to start file monitor: {}", e);
                    } else {
                        // Store the monitor in the plugin
                        let file_monitor = Arc::new(Mutex::new(monitor));
                        // Cast self to mutable (this is safe because we're only modifying our own field)
                        let this = unsafe { &*(self as *const Self as *mut Self) };
                        this.file_monitor = Some(file_monitor);
                        info!("File monitor started");
                    }
                }
                Err(e) => {
                    warn!("Failed to create file monitor: {}", e);
                }
            }
        }
        
        // ... other monitors ...
        
        info!("Endpoint Protection plugin initialized successfully");
        Ok(())
    }
    
    // ... existing methods ...
}
```

## Running and Testing the File Monitor

Let's create a simple example to demonstrate the file monitor in action:

```rust
fn main() -> Result<()> {
    // Initialize logging
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info")).init();
    
    // Create a configuration for file monitoring
    let config = EndpointProtectionConfig::default();
    
    // Create and initialize the Endpoint Protection plugin
    let plugin = EndpointProtectionPlugin::new(None)?;
    plugin.initialize()?;
    
    println!("File monitor is running. Press Enter to stop...");
    let mut input = String::new();
    std::io::stdin().read_line(&mut input)?;
    
    // Cast to mutable reference (safe because we own the plugin)
    let plugin_mut = unsafe { &*((&plugin) as *const _ as *mut EndpointProtectionPlugin) };
    plugin_mut.stop_all()?;
    
    Ok(())
}
```

## Rust Concepts and Design Decisions

### Advanced Rust Concepts Used

#### 1. Channels for Inter-thread Communication

We've used `crossbeam_channel` for communication between threads:

```rust
// Create a channel for receiving file system events
let (event_tx, event_rx) = bounded(1000);
```

Channels provide a safe way for threads to communicate without shared mutable state.

#### 2. Thread Safety with Arc and RwLock

We've used `Arc` (Atomic Reference Counting) and `RwLock` (Read-Write Lock) to safely share data between threads:

```rust
config: Arc<RwLock<FileMonitoringConfig>>,
```

- `Arc` allows multiple threads to share ownership of data
- `RwLock` provides thread-safe access to data, allowing multiple readers or a single writer

#### 3. Traits for Polymorphism

We've defined the `FileEventHandler` trait to allow different types of event handlers:

```rust
pub trait FileEventHandler: Send + Sync {
    fn handle_event(&self, event: FileEvent);
}
```

This provides a flexible, extensible design where new event handlers can be added without changing existing code.

#### 4. Error Handling with anyhow

We've used the `anyhow` crate for ergonomic error handling:

```rust
fn compute_file_hash<P: AsRef<Path>>(path: P) -> Result<String> {
    // ...
    let mut file = fs::File::open(path)
        .with_context(|| format!("Failed to open file for hashing: {}", path.display()))?;
    // ...
}
```

This approach provides detailed error messages with context.

#### 5. Concurrency Control with DashMap

We've used `DashMap` as a thread-safe alternative to `HashMap`:

```rust
recent_events: Arc<DashMap<PathBuf, Instant>>,
```

`DashMap` allows concurrent access to a map without requiring a global lock.

### Design Decisions

#### Event-Driven Architecture

We've chosen an event-driven architecture where:

1. The `notify` library generates file system events
2. Our code processes these events and converts them to `FileEvent` objects
3. Event handlers process these events

This approach is efficient and scalable, as it only processes events when they occur.

#### Filtering to Reduce Noise

We've implemented several filtering mechanisms to reduce noise:

1. File extension filtering to only monitor certain file types
2. Recent events tracking to avoid processing duplicate events
3. Configuration options to control which event types to monitor

#### Hashing for File Integrity

We compute SHA-256 hashes of files to:

1. Detect file modifications even when modification timestamps aren't updated
2. Identify known malicious files by hash
3. Provide forensic evidence for security incidents

#### Graceful Shutdown

We've implemented a graceful shutdown mechanism:

1. A stop channel signals the monitoring thread to stop
2. The monitoring thread cleans up resources before exiting
3. The main thread waits for the monitoring thread to finish

### Alternatives Considered

#### Alternative 1: Polling Instead of Events

We could have implemented file monitoring using periodic polling instead of events. This would be simpler but less efficient, especially for large file systems.

#### Alternative 2: Synchronous Processing

We could have processed file events synchronously in the same thread that receives them. This would be simpler but could cause events to be missed if processing is slow.

#### Alternative 3: Platform-Specific APIs

We could have used platform-specific APIs directly instead of the `notify` crate. This might provide more features but would require separate implementations for each platform.

## Exercise

Try extending the `FileMonitor` with a new event handler that:

1. Detects files with specific patterns (e.g., `.ransomware` extension)
2. Alerts the user when suspicious files are created or modified
3. Logs detailed information about suspicious files, including their hashes

## Next Steps

In the next section, we'll implement process monitoring to detect suspicious processes and behaviors.

## Navigation

- Previous: [Plugin Implementation](./02-plugin-implementation.md)
- Next: [Process Monitoring](./04-process-monitoring.md)
