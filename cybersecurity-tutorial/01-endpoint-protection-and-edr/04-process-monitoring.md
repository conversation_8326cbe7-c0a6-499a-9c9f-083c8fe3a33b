# Process Monitoring

In this section, we'll implement the process monitoring component of our Endpoint Protection plugin. Process monitoring is essential for detecting malicious or suspicious activities on a system by observing process behavior.

## Understanding Process Monitoring

Process monitoring involves tracking processes running on a system, including:

1. Process creation and termination
2. Process resource usage (CPU, memory, etc.)
3. File and network operations performed by processes
4. Parent-child relationships between processes
5. Command-line arguments and environment variables

Effective process monitoring helps detect:

- Malware execution
- Privilege escalation attempts
- Unusual system behavior
- Potentially unwanted programs
- Living-off-the-land attacks (misuse of legitimate tools)

## Implementing the Process Monitor

We'll use the `sysinfo` crate to monitor processes across different operating systems. Let's implement our process monitoring component:

**src/plugins/endpoint_protection/process_monitor.rs**:

```rust
//! Process monitoring for the Endpoint Protection plugin

use anyhow::{Context, Result};
use crossbeam_channel::{bounded, Receiver, Sender};
use dashmap::DashMap;
use log::{debug, error, info, warn};
use std::collections::{HashMap, HashSet};
use std::sync::{Arc, RwLock};
use std::thread;
use std::time::{Duration, Instant};
use sysinfo::{PidExt, Process, ProcessExt, System, SystemExt};

use super::config::ProcessMonitoringConfig;

/// Represents a process event
#[derive(Debug, Clone)]
pub struct ProcessEvent {
    /// Process ID
    pub pid: u32,
    /// Process name
    pub name: String,
    /// Command line arguments
    pub cmd_line: String,
    /// Parent process ID
    pub parent_pid: Option<u32>,
    /// Process event type
    pub event_type: ProcessEventType,
    /// Executable path
    pub executable_path: String,
    /// Memory usage (in bytes)
    pub memory_usage: u64,
    /// CPU usage (percentage)
    pub cpu_usage: f32,
    /// Timestamp when the event was detected
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Types of process events
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ProcessEventType {
    /// Process started
    Started,
    /// Process terminated
    Terminated,
    /// Process exceeded CPU threshold
    HighCpuUsage,
    /// Process exceeded memory threshold
    HighMemoryUsage,
    /// Suspicious process detected
    Suspicious,
}

/// Handler for process events
pub trait ProcessEventHandler: Send + Sync {
    /// Called when a process event is detected
    fn handle_event(&self, event: ProcessEvent);
}

/// Simple process event handler that logs events
#[derive(Debug)]
pub struct LoggingProcessEventHandler;

impl ProcessEventHandler for LoggingProcessEventHandler {
    fn handle_event(&self, event: ProcessEvent) {
        match event.event_type {
            ProcessEventType::Started => {
                info!(
                    "Process started: {} (PID: {}, Parent: {})",
                    event.name,
                    event.pid,
                    event.parent_pid.map_or_else(|| "none".to_string(), |p| p.to_string())
                );
                debug!("Command line: {}", event.cmd_line);
                debug!("Executable: {}", event.executable_path);
            }
            ProcessEventType::Terminated => {
                info!("Process terminated: {} (PID: {})", event.name, event.pid);
            }
            ProcessEventType::HighCpuUsage => {
                warn!(
                    "High CPU usage: {} (PID: {}, Usage: {:.1}%)",
                    event.name, event.pid, event.cpu_usage
                );
            }
            ProcessEventType::HighMemoryUsage => {
                warn!(
                    "High memory usage: {} (PID: {}, Usage: {} MB)",
                    event.name,
                    event.pid,
                    event.memory_usage / 1_024_000
                );
            }
            ProcessEventType::Suspicious => {
                warn!(
                    "Suspicious process: {} (PID: {})",
                    event.name, event.pid
                );
                warn!("Command line: {}", event.cmd_line);
                warn!("Executable: {}", event.executable_path);
            }
        }
    }
}

/// Monitors processes for suspicious activity
#[derive(Debug)]
pub struct ProcessMonitor {
    /// Configuration for process monitoring
    config: Arc<RwLock<ProcessMonitoringConfig>>,
    /// Channel for sending stop signals to the monitoring thread
    stop_tx: Option<Sender<()>>,
    /// Event handlers
    event_handlers: Arc<Vec<Box<dyn ProcessEventHandler>>>,
    /// Thread handle for the monitoring thread
    monitor_thread: Option<thread::JoinHandle<()>>,
    /// Previously seen processes
    previous_processes: Arc<RwLock<HashMap<u32, ProcessInfo>>>,
}

/// Information about a process
#[derive(Debug, Clone)]
struct ProcessInfo {
    /// Process ID
    pid: u32,
    /// Process name
    name: String,
    /// Command line arguments
    cmd_line: String,
    /// Parent process ID
    parent_pid: Option<u32>,
    /// Executable path
    executable_path: String,
    /// Memory usage (in bytes)
    memory_usage: u64,
    /// CPU usage (percentage)
    cpu_usage: f32,
    /// When the process was last seen
    last_seen: Instant,
}

impl ProcessMonitor {
    /// Creates a new process monitor with the given configuration
    pub fn new(
        config: ProcessMonitoringConfig,
        event_handlers: Vec<Box<dyn ProcessEventHandler>>,
    ) -> Result<Self> {
        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            stop_tx: None,
            event_handlers: Arc::new(event_handlers),
            monitor_thread: None,
            previous_processes: Arc::new(RwLock::new(HashMap::new())),
        })
    }
    
    /// Starts the process monitor
    pub fn start(&mut self) -> Result<()> {
        if self.monitor_thread.is_some() {
            warn!("Process monitor is already running");
            return Ok(());
        }
        
        let config = match self.config.read() {
            Ok(config) => config.clone(),
            Err(e) => {
                error!("Failed to read configuration: {}", e);
                return Err(anyhow::anyhow!("Failed to read configuration"));
            }
        };
        
        if !config.enabled {
            info!("Process monitoring is disabled in configuration");
            return Ok(());
        }
        
        // Create a channel for stopping the monitoring thread
        let (stop_tx, stop_rx) = bounded(1);
        self.stop_tx = Some(stop_tx);
        
        // Clone necessary data for the monitoring thread
        let event_handlers = self.event_handlers.clone();
        let previous_processes = self.previous_processes.clone();
        let check_interval_ms = config.check_interval_ms;
        let suspicious_processes = config.suspicious_processes.clone();
        let monitor_new_processes = config.monitor_new_processes;
        let monitor_terminated_processes = config.monitor_terminated_processes;
        let monitor_cpu_usage = config.monitor_cpu_usage;
        let cpu_threshold = config.cpu_threshold;
        let monitor_memory_usage = config.monitor_memory_usage;
        let memory_threshold = config.memory_threshold;
        
        // Start a thread to monitor processes
        let monitor_thread = thread::spawn(move || {
            info!("Process monitor thread started");
            
            // Initialize sysinfo
            let mut system = System::new_all();
            
            loop {
                // Check if we should stop
                if stop_rx.try_recv().is_ok() {
                    info!("Received stop signal, shutting down process monitor");
                    break;
                }
                
                // Refresh system information
                system.refresh_all();
                
                // Track currently seen processes
                let mut current_pids = HashSet::new();
                
                // Process all current processes
                for (pid, process) in system.processes() {
                    let pid_u32 = pid.as_u32();
                    current_pids.insert(pid_u32);
                    
                    // Create process info
                    let process_info = Self::create_process_info(pid_u32, process);
                    
                    // Check for events
                    Self::check_process_events(
                        &process_info,
                        &previous_processes,
                        &event_handlers,
                        &suspicious_processes,
                        monitor_new_processes,
                        monitor_cpu_usage,
                        cpu_threshold,
                        monitor_memory_usage,
                        memory_threshold,
                    );
                    
                    // Update previous processes
                    if let Ok(mut previous) = previous_processes.write() {
                        previous.insert(pid_u32, process_info);
                    }
                }
                
                // Check for terminated processes
                if monitor_terminated_processes {
                    if let Ok(mut previous) = previous_processes.write() {
                        let mut terminated = Vec::new();
                        
                        for (pid, info) in previous.iter() {
                            if !current_pids.contains(pid) {
                                // Process no longer exists
                                terminated.push(*pid);
                                
                                // Create termination event
                                let event = ProcessEvent {
                                    pid: *pid,
                                    name: info.name.clone(),
                                    cmd_line: info.cmd_line.clone(),
                                    parent_pid: info.parent_pid,
                                    event_type: ProcessEventType::Terminated,
                                    executable_path: info.executable_path.clone(),
                                    memory_usage: info.memory_usage,
                                    cpu_usage: info.cpu_usage,
                                    timestamp: chrono::Utc::now(),
                                };
                                
                                // Notify handlers
                                for handler in event_handlers.iter() {
                                    handler.handle_event(event.clone());
                                }
                            }
                        }
                        
                        // Remove terminated processes
                        for pid in terminated {
                            previous.remove(&pid);
                        }
                    }
                }
                
                // Sleep until next check
                thread::sleep(Duration::from_millis(check_interval_ms));
            }
            
            info!("Process monitor thread stopped");
        });
        
        self.monitor_thread = Some(monitor_thread);
        info!("Process monitor started");
        
        Ok(())
    }
    
    /// Creates ProcessInfo from sysinfo::Process
    fn create_process_info(pid: u32, process: &Process) -> ProcessInfo {
        ProcessInfo {
            pid,
            name: process.name().to_string(),
            cmd_line: process.cmd().join(" "),
            parent_pid: process.parent().map(|p| p.as_u32()),
            executable_path: process.exe().map_or_else(
                || String::from("unknown"), 
                |p| p.to_string_lossy().to_string()
            ),
            memory_usage: process.memory(),
            cpu_usage: process.cpu_usage(),
            last_seen: Instant::now(),
        }
    }
    
    /// Checks for process events
    #[allow(clippy::too_many_arguments)]
    fn check_process_events(
        process_info: &ProcessInfo,
        previous_processes: &Arc<RwLock<HashMap<u32, ProcessInfo>>>,
        event_handlers: &Arc<Vec<Box<dyn ProcessEventHandler>>>,
        suspicious_processes: &[String],
        monitor_new_processes: bool,
        monitor_cpu_usage: bool,
        cpu_threshold: f32,
        monitor_memory_usage: bool,
        memory_threshold: f32,
    ) {
        let pid = process_info.pid;
        let is_new_process = {
            if let Ok(previous) = previous_processes.read() {
                !previous.contains_key(&pid)
            } else {
                false
            }
        };
        
        // Check for new processes
        if is_new_process && monitor_new_processes {
            // Create new process event
            let event = ProcessEvent {
                pid: process_info.pid,
                name: process_info.name.clone(),
                cmd_line: process_info.cmd_line.clone(),
                parent_pid: process_info.parent_pid,
                event_type: ProcessEventType::Started,
                executable_path: process_info.executable_path.clone(),
                memory_usage: process_info.memory_usage,
                cpu_usage: process_info.cpu_usage,
                timestamp: chrono::Utc::now(),
            };
            
            // Notify handlers
            for handler in event_handlers.iter() {
                handler.handle_event(event.clone());
            }
        }
        
        // Check for suspicious processes
        let name_lower = process_info.name.to_lowercase();
        if suspicious_processes.iter().any(|s| name_lower.contains(&s.to_lowercase())) {
            // Create suspicious process event
            let event = ProcessEvent {
                pid: process_info.pid,
                name: process_info.name.clone(),
                cmd_line: process_info.cmd_line.clone(),
                parent_pid: process_info.parent_pid,
                event_type: ProcessEventType::Suspicious,
                executable_path: process_info.executable_path.clone(),
                memory_usage: process_info.memory_usage,
                cpu_usage: process_info.cpu_usage,
                timestamp: chrono::Utc::now(),
            };
            
            // Notify handlers
            for handler in event_handlers.iter() {
                handler.handle_event(event.clone());
            }
        }
        
        // Check for high CPU usage
        if monitor_cpu_usage && process_info.cpu_usage > cpu_threshold {
            // Create high CPU usage event
            let event = ProcessEvent {
                pid: process_info.pid,
                name: process_info.name.clone(),
                cmd_line: process_info.cmd_line.clone(),
                parent_pid: process_info.parent_pid,
                event_type: ProcessEventType::HighCpuUsage,
                executable_path: process_info.executable_path.clone(),
                memory_usage: process_info.memory_usage,
                cpu_usage: process_info.cpu_usage,
                timestamp: chrono::Utc::now(),
            };
            
            // Notify handlers
            for handler in event_handlers.iter() {
                handler.handle_event(event.clone());
            }
        }
        
        // Check for high memory usage
        if monitor_memory_usage {
            // Convert memory usage to percentage
            let total_memory = {
                let mut system = System::new_all();
                system.refresh_memory();
                system.total_memory()
            };
            
            let memory_percent = if total_memory > 0 {
                (process_info.memory_usage as f32 / total_memory as f32) * 100.0
            } else {
                0.0
            };
            
            if memory_percent > memory_threshold {
                // Create high memory usage event
                let event = ProcessEvent {
                    pid: process_info.pid,
                    name: process_info.name.clone(),
                    cmd_line: process_info.cmd_line.clone(),
                    parent_pid: process_info.parent_pid,
                    event_type: ProcessEventType::HighMemoryUsage,
                    executable_path: process_info.executable_path.clone(),
                    memory_usage: process_info.memory_usage,
                    cpu_usage: process_info.cpu_usage,
                    timestamp: chrono::Utc::now(),
                };
                
                // Notify handlers
                for handler in event_handlers.iter() {
                    handler.handle_event(event.clone());
                }
            }
        }
    }
    
    /// Stops the process monitor
    pub fn stop(&mut self) -> Result<()> {
        if let Some(stop_tx) = self.stop_tx.take() {
            if let Err(e) = stop_tx.send(()) {
                error!("Failed to send stop signal: {}", e);
            }
            
            if let Some(thread) = self.monitor_thread.take() {
                match thread.join() {
                    Ok(_) => {
                        info!("Process monitor thread joined successfully");
                    }
                    Err(e) => {
                        error!("Failed to join process monitor thread: {:?}", e);
                    }
                }
            }
        }
        
        Ok(())
    }
}

/// Creates a new process monitor with default configuration
pub fn create_default_process_monitor(config: ProcessMonitoringConfig) -> Result<ProcessMonitor> {
    let mut handlers: Vec<Box<dyn ProcessEventHandler>> = Vec::new();
    handlers.push(Box::new(LoggingProcessEventHandler));
    
    ProcessMonitor::new(config, handlers)
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Mutex;
    
    /// A mock event handler that captures events for testing
    struct MockProcessEventHandler {
        events: Arc<Mutex<Vec<ProcessEvent>>>,
    }
    
    impl MockProcessEventHandler {
        fn new() -> (Self, Arc<Mutex<Vec<ProcessEvent>>>) {
            let events = Arc::new(Mutex::new(Vec::new()));
            (Self { events: events.clone() }, events)
        }
    }
    
    impl ProcessEventHandler for MockProcessEventHandler {
        fn handle_event(&self, event: ProcessEvent) {
            let mut events = self.events.lock().unwrap();
            events.push(event);
        }
    }
    
    #[test]
    fn test_process_monitor_creation() {
        let config = ProcessMonitoringConfig {
            enabled: false, // Disable for testing
            check_interval_ms: 1000,
            suspicious_processes: vec!["test".to_string()],
            monitor_new_processes: true,
            monitor_terminated_processes: true,
            monitor_cpu_usage: true,
            cpu_threshold: 80.0,
            monitor_memory_usage: true,
            memory_threshold: 80.0,
        };
        
        let result = create_default_process_monitor(config);
        assert!(result.is_ok());
    }
}
```

## Updating the Plugin Implementation

Now, let's update the `EndpointProtectionPlugin` to use our new `ProcessMonitor` implementation:

**src/plugins/endpoint_protection/mod.rs** (partial update):

```rust
// ... existing imports ...
use self::process_monitor::{create_default_process_monitor, ProcessMonitor};
// ... other imports ...

/// Endpoint Protection plugin for CyberShield
#[derive(Debug)]
pub struct EndpointProtectionPlugin {
    name: String,
    version: String,
    description: String,
    config: Arc<RwLock<EndpointProtectionConfig>>,
    file_monitor: Option<Arc<Mutex<FileMonitor>>>,
    process_monitor: Option<Arc<Mutex<ProcessMonitor>>>,
    threat_detector: Option<Arc<ThreatDetector>>,
    system_health_monitor: Option<Arc<SystemHealthMonitor>>,
}

impl EndpointProtectionPlugin {
    // ... existing methods ...

    /// Stops all monitoring components
    pub fn stop_all(&mut self) -> Result<()> {
        info!("Stopping all endpoint protection components");
        
        // Stop file monitoring
        if let Some(monitor) = &self.file_monitor {
            if let Ok(mut monitor) = monitor.lock() {
                if let Err(e) = monitor.stop() {
                    warn!("Error stopping file monitor: {}", e);
                }
            } else {
                warn!("Failed to acquire lock on file monitor");
            }
        }
        
        // Stop process monitoring
        if let Some(monitor) = &self.process_monitor {
            if let Ok(mut monitor) = monitor.lock() {
                if let Err(e) = monitor.stop() {
                    warn!("Error stopping process monitor: {}", e);
                }
            } else {
                warn!("Failed to acquire lock on process monitor");
            }
        }
        
        // ... other monitors ...
        
        Ok(())
    }
}

impl Plugin for EndpointProtectionPlugin {
    // ... existing methods ...
    
    fn initialize(&self) -> Result<()> {
        info!("Initializing Endpoint Protection plugin");
        
        // Access configuration in a thread-safe way
        let config = match self.config.read() {
            Ok(config) => config.clone(),
            Err(e) => {
                warn!("Failed to read configuration: {}", e);
                return Err(anyhow::anyhow!("Failed to read configuration"));
            }
        };
        
        // Initialize file monitoring if enabled
        if config.file_monitoring.enabled {
            debug!("File monitoring is enabled");
            
            // Create and start a file monitor
            match create_default_file_monitor(config.file_monitoring.clone()) {
                Ok(mut monitor) => {
                    if let Err(e) = monitor.start() {
                        warn!("Failed to start file monitor: {}", e);
                    } else {
                        // Store the monitor in the plugin
                        let file_monitor = Arc::new(Mutex::new(monitor));
                        // Cast self to mutable (this is safe because we're only modifying our own field)
                        let this = unsafe { &*(self as *const Self as *mut Self) };
                        this.file_monitor = Some(file_monitor);
                        info!("File monitor started");
                    }
                }
                Err(e) => {
                    warn!("Failed to create file monitor: {}", e);
                }
            }
        }
        
        // Initialize process monitoring if enabled
        if config.process_monitoring.enabled {
            debug!("Process monitoring is enabled");
            
            // Create and start a process monitor
            match create_default_process_monitor(config.process_monitoring.clone()) {
                Ok(mut monitor) => {
                    if let Err(e) = monitor.start() {
                        warn!("Failed to start process monitor: {}", e);
                    } else {
                        // Store the monitor in the plugin
                        let process_monitor = Arc::new(Mutex::new(monitor));
                        // Cast self to mutable (this is safe because we're only modifying our own field)
                        let this = unsafe { &*(self as *const Self as *mut Self) };
                        this.process_monitor = Some(process_monitor);
                        info!("Process monitor started");
                    }
                }
                Err(e) => {
                    warn!("Failed to create process monitor: {}", e);
                }
            }
        }
        
        // ... other monitors ...
        
        info!("Endpoint Protection plugin initialized successfully");
        Ok(())
    }
    
    // ... existing methods ...
}
```

## Running and Testing the Process Monitor

Let's create a simple example to demonstrate the process monitor in action:

```rust
fn main() -> Result<()> {
    // Initialize logging
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info")).init();
    
    // Create a configuration for process monitoring
    let mut config = ProcessMonitoringConfig::default();
    config.check_interval_ms = 2000; // Check every 2 seconds
    
    // Create and start a process monitor
    let mut monitor = create_default_process_monitor(config)?;
    monitor.start()?;
    
    println!("Process monitor is running. Press Enter to stop...");
    let mut input = String::new();
    std::io::stdin().read_line(&mut input)?;
    
    // Stop the monitor
    monitor.stop()?;
    
    Ok(())
}
```

## Rust Concepts and Design Decisions

### Advanced Rust Concepts Used

#### 1. Thread-Safe Data Structures with RwLock

We've used `RwLock` to safely share and modify data between threads:

```rust
previous_processes: Arc<RwLock<HashMap<u32, ProcessInfo>>>,
```

The `RwLock` allows multiple readers or a single writer, which is ideal for our use case where we frequently read the process list but only occasionally update it.

#### 2. Safe Interior Mutability

We've used interior mutability patterns to modify data through shared references where necessary:

```rust
let this = unsafe { &*(self as *const Self as *mut Self) };
this.process_monitor = Some(process_monitor);
```

While this uses unsafe code, it's safe in this context because we're only modifying our own fields when we know no other thread is accessing them.

#### 3. Trait Objects for Extensibility

We've used trait objects to allow different types of event handlers:

```rust
event_handlers: Arc<Vec<Box<dyn ProcessEventHandler>>>,
```

This allows users to implement their own event handlers to respond to process events in custom ways.

#### 4. Cross-Platform System Information

We've used the `sysinfo` crate to gather process information in a cross-platform manner:

```rust
let mut system = System::new_all();
system.refresh_all();
```

This abstraction lets our code work on different operating systems without platform-specific implementations.

### Design Decisions

#### Event-Driven Architecture

Similar to our file monitoring component, we've chosen an event-driven architecture where:

1. The process monitor periodically checks for process events
2. Events are detected and converted to `ProcessEvent` objects
3. Event handlers process these events

This approach is flexible and decouples event generation from handling.

#### Continuous Monitoring vs. Event Callbacks

Unlike file monitoring, which uses callbacks from the OS, process monitoring typically requires periodic polling. We've implemented this using a background thread that checks processes at configurable intervals.

#### Resource Usage Considerations

Process monitoring can be resource-intensive, especially on systems with many processes. We've addressed this by:

1. Making the check interval configurable
2. Focusing only on relevant processes and events
3. Using efficient data structures like `HashMap` for quick lookups

#### Thread Safety

We've implemented thread-safe design:

1. Using `Arc` for shared ownership across threads
2. Using `RwLock` for synchronizing access to shared data
3. Implementing proper shutdown mechanisms to avoid resource leaks

### Alternatives Considered

#### Alternative 1: OS-Specific Process Monitoring

We could have used platform-specific APIs like Windows Management Instrumentation (WMI) on Windows or procfs on Linux. This might provide more detailed information but would require separate implementations for each platform.

#### Alternative 2: Event-Based Process Monitoring

Some platforms offer event-based process monitoring, where the OS notifies applications when processes start or stop. This would be more efficient but less portable across operating systems.

#### Alternative 3: Single-Threaded Design

We could have implemented process monitoring in the main application thread, avoiding the complexity of multi-threading. This would be simpler but would make the application less responsive.

## Exercise

Try extending the `ProcessMonitor` with a new event handler that:

1. Tracks process relationships (parent-child hierarchy)
2. Detects suspicious process patterns (like a web browser spawning a command shell)
3. Generates alerts for processes that access sensitive files or resources

## Next Steps

In the next section, we'll implement threat detection capabilities that will use information from both our file and process monitors to identify potential security threats.

## Navigation

- Previous: [File System Monitoring](./03-file-system-monitoring.md)
- Next: [Threat Detection](./05-threat-detection.md)
