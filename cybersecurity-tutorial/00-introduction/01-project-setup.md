# Project Setup and Foundations

In this first section, we'll set up our development environment and create the foundation for our modular cybersecurity software. We'll define the project structure, core architecture, and plugin system that will allow us to extend functionality in future modules.

## Setting Up the Development Environment

### 1. Install Rust

First, ensure you have Rust installed on your system:

```bash
# Windows PowerShell
Invoke-WebRequest https://sh.rustup.rs -UseBasicParsing | Invoke-Expression
```

Verify the installation:

```bash
rustc --version
cargo --version
```

### 2. Create a New Project

We'll create a new Rust binary project called "cybershield":

```bash
cargo new cybershield
cd cybershield
```

### 3. Recommended VS Code Extensions

For optimal development experience, we recommend installing:

- rust-analyzer - Provides intelligent code completion and analysis
- CodeLLDB - For debugging Rust applications
- Better TOML - For editing configuration files
- Error Lens - For enhanced error visualization
- Even Better TOML - For improved TOML file support

## Project Structure Design

We'll create a modular architecture that allows for a plugin-based system. Here's our initial project structure:

```
cybershield/
├── Cargo.toml
├── src/
│   ├── main.rs           # Application entry point
│   ├── core/             # Core functionality
│   │   ├── mod.rs        # Module declaration
│   │   ├── config.rs     # Configuration handling
│   │   └── plugin.rs     # Plugin management
│   ├── plugins/          # Plugin interface
│   │   ├── mod.rs        # Module declaration
│   │   └── interface.rs  # Plugin trait definitions
│   └── utils/            # Utility functions
│       ├── mod.rs        # Module declaration
│       └── logger.rs     # Logging functionality
└── tests/                # Integration tests
```

### Project Design Decisions

#### Why This Structure?

1. **Separation of Concerns**: Core functionality is separated from plugins.
2. **Modularity**: Each security feature will be implemented as a plugin.
3. **Extensibility**: New security modules can be added without modifying core code.
4. **Testability**: Each component can be tested individually.

#### Alternatives Considered

1. **Single Binary Approach**: We could have built everything into a single binary without a plugin system. This would be simpler initially but would make the project less extensible.

2. **Microservices Architecture**: Each security module could be a separate service. While this would offer better isolation, it would increase operational complexity and introduce network communication overhead.

3. **Library-Only Approach**: We could focus on building a library instead of an application. This would improve reusability but would require users to write their own integrations.

## Creating the Base Project

### 1. Update Cargo.toml

Let's add our initial dependencies:

```toml
[package]
name = "cybershield"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A modular cybersecurity software with plugin support"

[dependencies]
# Core dependencies
log = "0.4"           # Logging facade
env_logger = "0.10"   # Logger implementation
serde = { version = "1.0", features = ["derive"] }  # Serialization
serde_json = "1.0"    # JSON serialization
toml = "0.7"          # TOML configuration file parsing
clap = { version = "4.3", features = ["derive"] }  # Command line argument parsing
anyhow = "1.0"        # Error handling

[dev-dependencies]
tempfile = "3.8"     # Temporary file creation for tests
assert_cmd = "2.0"   # Testing command line applications
```

### 2. Rust Concepts: The Module System

Rust's module system allows us to organize code and control visibility. Some key concepts:

- `mod` declares a module
- `pub` makes an item public
- `use` brings items into scope

Let's create our first modules.

### 3. Create Core Files

**src/main.rs**:

```rust
mod core;
mod plugins;
mod utils;

use anyhow::Result;
use clap::Parser;
use log::{info, LevelFilter};

/// CyberShield - A modular cybersecurity software with plugin support
#[derive(Parser)]
#[clap(version, about)]
struct Args {
    /// Configuration file path
    #[clap(short, long, default_value = "config.toml")]
    config: String,
    
    /// Verbose output
    #[clap(short, long, action = clap::ArgAction::Count)]
    verbose: u8,
}

fn main() -> Result<()> {
    let args = Args::parse();
    
    // Initialize logger
    let log_level = match args.verbose {
        0 => LevelFilter::Info,
        1 => LevelFilter::Debug,
        _ => LevelFilter::Trace,
    };
    
    utils::logger::init_logger(log_level)?;
    info!("Starting CyberShield v{}", env!("CARGO_PKG_VERSION"));
    
    // Load configuration
    let config = core::config::Config::load_from_file(&args.config)?;
    
    // Initialize plugin system
    let plugin_manager = core::plugin::PluginManager::new(config.plugins_dir);
    let loaded_plugins = plugin_manager.load_plugins()?;
    
    info!("Loaded {} plugins", loaded_plugins.len());
    
    // Just for demonstration in this version
    for plugin in loaded_plugins {
        info!("Plugin: {}", plugin.name());
        plugin.initialize()?;
    }
    
    info!("CyberShield initialized successfully");
    
    Ok(())
}
```

**src/core/mod.rs**:

```rust
pub mod config;
pub mod plugin;
```

**src/plugins/mod.rs**:

```rust
pub mod interface;
```

**src/utils/mod.rs**:

```rust
pub mod logger;
```

### 4. Plugin Interface Definition

**src/plugins/interface.rs**:

```rust
use anyhow::Result;
use std::fmt::Debug;

/// Trait that all plugins must implement
pub trait Plugin: Debug + Send + Sync {
    /// Returns the name of the plugin
    fn name(&self) -> &str;
    
    /// Returns the version of the plugin
    fn version(&self) -> &str;
    
    /// Returns a description of the plugin
    fn description(&self) -> &str;
    
    /// Initializes the plugin
    fn initialize(&self) -> Result<()>;
    
    /// Shuts down the plugin
    fn shutdown(&self) -> Result<()>;
}
```

### 5. Configuration Management

**src/core/config.rs**:

```rust
use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::{Path, PathBuf};

/// Application configuration
#[derive(Debug, Deserialize, Serialize)]
pub struct Config {
    /// Application name
    pub name: String,
    
    /// Directory containing plugins
    pub plugins_dir: PathBuf,
    
    /// List of enabled plugins
    pub enabled_plugins: Vec<String>,
    
    /// Log configuration
    pub log: LogConfig,
}

/// Log configuration
#[derive(Debug, Deserialize, Serialize)]
pub struct LogConfig {
    /// Log level
    pub level: String,
    
    /// Log file path
    pub file: Option<String>,
}

impl Config {
    /// Creates a default configuration
    pub fn default() -> Self {
        Self {
            name: "CyberShield".to_string(),
            plugins_dir: PathBuf::from("plugins"),
            enabled_plugins: Vec::new(),
            log: LogConfig {
                level: "info".to_string(),
                file: None,
            },
        }
    }
    
    /// Loads configuration from a file
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path = path.as_ref();
        
        if !path.exists() {
            // Create default config if file doesn't exist
            let config = Self::default();
            let toml = toml::to_string_pretty(&config)?;
            fs::write(path, toml)?;
            return Ok(config);
        }
        
        let content = fs::read_to_string(path)
            .with_context(|| format!("Failed to read config file: {}", path.display()))?;
            
        let config: Self = toml::from_str(&content)
            .with_context(|| format!("Failed to parse config file: {}", path.display()))?;
            
        Ok(config)
    }
    
    /// Saves configuration to a file
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let toml = toml::to_string_pretty(self)?;
        fs::write(path, toml)?;
        Ok(())
    }
}
```

### 6. Plugin Management

**src/core/plugin.rs**:

```rust
use anyhow::{Context, Result};
use log::{error, info};
use std::path::PathBuf;

use crate::plugins::interface::Plugin;

/// Manages loading and running plugins
pub struct PluginManager {
    plugins_dir: PathBuf,
}

impl PluginManager {
    /// Creates a new plugin manager
    pub fn new(plugins_dir: PathBuf) -> Self {
        Self { plugins_dir }
    }
    
    /// Loads plugins from the plugins directory
    pub fn load_plugins(&self) -> Result<Vec<Box<dyn Plugin>>> {
        // In a real implementation, this would dynamically load plugins
        // For now, we'll just return an empty vector
        info!("Looking for plugins in: {}", self.plugins_dir.display());
        
        if !self.plugins_dir.exists() {
            std::fs::create_dir_all(&self.plugins_dir)
                .with_context(|| format!("Failed to create plugins directory: {}", self.plugins_dir.display()))?;
        }
        
        // This is just a placeholder. In a real implementation, we would:
        // 1. Scan the plugins directory for plugin files
        // 2. Dynamically load each plugin
        // 3. Return the loaded plugins
        
        Ok(Vec::new())
    }
}
```

### 7. Logger Utility

**src/utils/logger.rs**:

```rust
use anyhow::Result;
use env_logger::{Builder, Env};
use log::LevelFilter;

/// Initializes the logger
pub fn init_logger(level: LevelFilter) -> Result<()> {
    Builder::from_env(Env::default().default_filter_or(level.to_string()))
        .format_timestamp_secs()
        .init();
    
    Ok(())
}
```

## Rust Concepts Introduced

In this module, we've introduced several foundational Rust concepts:

1. **Project Organization**: Using Cargo for project management
2. **Module System**: Organizing code into modules
3. **Error Handling**: Using `anyhow` for ergonomic error handling
4. **Traits**: Defining plugin behavior with the `Plugin` trait
5. **Ownership**: Basic usage of owned types, references, and borrowing
6. **Generics**: Using generic types in functions like `load_from_file<P: AsRef<Path>>`

## Building and Testing

To build the project:

```bash
cargo build
```

To run the application:

```bash
cargo run -- --verbose
```

To run tests:

```bash
cargo test
```

## Next Steps

In the next module, we'll implement our first real plugin: an Endpoint Protection module. We'll learn more about Rust's ownership system, error handling, and begin working with system-level APIs to monitor and protect endpoints.

## Knowledge Check

Before moving on, answer these questions:
1. What command installs Rust on Windows?
2. Why is a plugin-based architecture useful for cybersecurity tools?
3. Which VS Code extension helps with Rust code completion?

## Navigation

- Previous: [Tutorial Overview](../00-introduction/00-overview.md)
- Next: [Endpoint Protection Module](../01-endpoint-protection-and-edr/00-overview.md)
