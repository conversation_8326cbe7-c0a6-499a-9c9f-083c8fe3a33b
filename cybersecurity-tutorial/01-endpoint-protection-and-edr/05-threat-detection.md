# Threat Detection

In this section, we'll implement the threat detection component of our Endpoint Protection plugin. This component will analyze data from both the file system monitor and the process monitor to detect potential security threats.

## Understanding Threat Detection

Threat detection involves analyzing system behavior and patterns to identify potential security threats. Our threat detection component will use a combination of approaches:

1. **Signature-based detection**: Looking for known indicators of compromise
2. **Heuristic detection**: Using rule-based algorithms to detect suspicious behavior
3. **Behavioral analysis**: Analyzing patterns of activity to identify anomalies
4. **Correlation**: Combining multiple indicators to identify threats with higher confidence

Effective threat detection helps identify:

- Malware infections
- Ransomware activity
- Data exfiltration attempts
- Unauthorized access
- Suspicious system changes

## Implementing the Threat Detector

Let's implement our threat detection component:

**src/plugins/endpoint_protection/threat_detection.rs**:

```rust
//! Threat detection for the Endpoint Protection plugin

use anyhow::{Context, Result};
use crossbeam_channel::{bounded, select, Receiver, Sender};
use dashmap::DashMap;
use log::{debug, error, info, warn};
use regex::Regex;
use std::collections::VecDeque;
use std::path::PathBuf;
use std::sync::Arc;
use std::thread;
use std::time::{Duration, Instant};

use super::file_monitor::{FileEvent, FileEventHandler, FileEventType};
use super::process_monitor::{ProcessEvent, ProcessEventHandler, ProcessEventType};
use super::config::ThreatDetectionConfig;

/// Severity levels for threats
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum ThreatSeverity {
    /// Informational - low confidence or impact
    Info,
    /// Low severity - potential threat with low confidence or impact
    Low,
    /// Medium severity - potential threat with moderate confidence or impact
    Medium,
    /// High severity - confirmed threat with significant impact
    High,
    /// Critical severity - severe threat requiring immediate attention
    Critical,
}

/// Types of threats
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ThreatType {
    /// Malware infection
    Malware,
    /// Ransomware activity
    Ransomware,
    /// Suspicious process behavior
    SuspiciousProcess,
    /// Suspicious file activity
    SuspiciousFile,
    /// Potential data exfiltration
    DataExfiltration,
    /// System modification
    SystemModification,
    /// Other types of threats
    Other(String),
}

/// Represents a detected threat
#[derive(Debug, Clone)]
pub struct Threat {
    /// Threat ID (UUID)
    pub id: String,
    /// Threat type
    pub threat_type: ThreatType,
    /// Threat severity
    pub severity: ThreatSeverity,
    /// Threat description
    pub description: String,
    /// Affected file path (if applicable)
    pub file_path: Option<PathBuf>,
    /// Affected process ID (if applicable)
    pub process_id: Option<u32>,
    /// Process name (if applicable)
    pub process_name: Option<String>,
    /// Time when the threat was detected
    pub detection_time: chrono::DateTime<chrono::Utc>,
    /// Evidence supporting the threat detection
    pub evidence: Vec<String>,
    /// Recommended actions
    pub recommendations: Vec<String>,
}

/// Handler for detected threats
pub trait ThreatHandler: Send + Sync {
    /// Called when a threat is detected
    fn handle_threat(&self, threat: Threat);
}

/// Simple threat handler that logs threats
#[derive(Debug)]
pub struct LoggingThreatHandler;

impl ThreatHandler for LoggingThreatHandler {
    fn handle_threat(&self, threat: Threat) {
        let severity_str = match threat.severity {
            ThreatSeverity::Info => "INFO",
            ThreatSeverity::Low => "LOW",
            ThreatSeverity::Medium => "MEDIUM",
            ThreatSeverity::High => "HIGH",
            ThreatSeverity::Critical => "CRITICAL",
        };
        
        let threat_type = match threat.threat_type {
            ThreatType::Malware => "Malware",
            ThreatType::Ransomware => "Ransomware",
            ThreatType::SuspiciousProcess => "Suspicious Process",
            ThreatType::SuspiciousFile => "Suspicious File",
            ThreatType::DataExfiltration => "Data Exfiltration",
            ThreatType::SystemModification => "System Modification",
            ThreatType::Other(ref s) => s,
        };
        
        match threat.severity {
            ThreatSeverity::Info => {
                info!("[{}] {} detected: {}", severity_str, threat_type, threat.description);
            }
            ThreatSeverity::Low => {
                warn!("[{}] {} detected: {}", severity_str, threat_type, threat.description);
            }
            ThreatSeverity::Medium | ThreatSeverity::High | ThreatSeverity::Critical => {
                error!("[{}] {} detected: {}", severity_str, threat_type, threat.description);
                
                if let Some(path) = &threat.file_path {
                    error!("Affected file: {}", path.display());
                }
                
                if let Some(pid) = threat.process_id {
                    error!("Affected process: {} (PID: {})", 
                           threat.process_name.as_deref().unwrap_or("unknown"), 
                           pid);
                }
                
                error!("Detection time: {}", threat.detection_time);
                error!("Evidence:");
                for evidence in &threat.evidence {
                    error!("  - {}", evidence);
                }
                
                error!("Recommendations:");
                for recommendation in &threat.recommendations {
                    error!("  - {}", recommendation);
                }
            }
        }
    }
}

/// Detects threats based on file and process activities
#[derive(Debug)]
pub struct ThreatDetector {
    /// Configuration for threat detection
    config: ThreatDetectionConfig,
    /// Channel for sending stop signals to the detection thread
    stop_tx: Option<Sender<()>>,
    /// Channel for receiving file events
    file_event_rx: Option<Receiver<FileEvent>>,
    /// Channel for sending file events
    file_event_tx: Option<Sender<FileEvent>>,
    /// Channel for receiving process events
    process_event_rx: Option<Receiver<ProcessEvent>>,
    /// Channel for sending process events
    process_event_tx: Option<Sender<ProcessEvent>>,
    /// Threat handlers
    threat_handlers: Arc<Vec<Box<dyn ThreatHandler>>>,
    /// Detection thread handle
    detection_thread: Option<thread::JoinHandle<()>>,
    /// Compiled suspicious file patterns
    suspicious_file_patterns: Vec<Regex>,
    /// Compiled suspicious process patterns
    suspicious_process_patterns: Vec<Regex>,
    /// Known malicious file hashes
    malicious_file_hashes: Arc<DashMap<String, String>>,
    /// Recent file events for correlation
    recent_file_events: Arc<DashMap<PathBuf, VecDeque<FileEvent>>>,
    /// Recent process events for correlation
    recent_process_events: Arc<DashMap<u32, VecDeque<ProcessEvent>>>,
}

/// File event handler for the threat detector
#[derive(Debug, Clone)]
struct ThreatDetectorFileEventHandler {
    /// Channel for sending file events
    file_event_tx: Sender<FileEvent>,
}

impl FileEventHandler for ThreatDetectorFileEventHandler {
    fn handle_event(&self, event: FileEvent) {
        if let Err(e) = self.file_event_tx.send(event) {
            error!("Failed to send file event to threat detector: {}", e);
        }
    }
}

/// Process event handler for the threat detector
#[derive(Debug, Clone)]
struct ThreatDetectorProcessEventHandler {
    /// Channel for sending process events
    process_event_tx: Sender<ProcessEvent>,
}

impl ProcessEventHandler for ThreatDetectorProcessEventHandler {
    fn handle_event(&self, event: ProcessEvent) {
        if let Err(e) = self.process_event_tx.send(event) {
            error!("Failed to send process event to threat detector: {}", e);
        }
    }
}

impl ThreatDetector {
    /// Creates a new threat detector
    pub fn new(
        config: ThreatDetectionConfig,
        threat_handlers: Vec<Box<dyn ThreatHandler>>,
    ) -> Result<Self> {
        // Compile patterns
        let suspicious_file_patterns = config
            .suspicious_file_patterns
            .iter()
            .map(|pattern| {
                Regex::new(pattern).with_context(|| {
                    format!("Failed to compile suspicious file pattern: {}", pattern)
                })
            })
            .collect::<Result<Vec<_>>>()?;
            
        let suspicious_process_patterns = config
            .suspicious_process_patterns
            .iter()
            .map(|pattern| {
                Regex::new(pattern).with_context(|| {
                    format!("Failed to compile suspicious process pattern: {}", pattern)
                })
            })
            .collect::<Result<Vec<_>>>()?;
            
        // Create malicious file hash map
        let malicious_file_hashes = Arc::new(DashMap::new());
        for hash in &config.malicious_file_hashes {
            let parts: Vec<&str> = hash.split(':').collect();
            if parts.len() >= 2 {
                malicious_file_hashes.insert(parts[0].to_string(), parts[1].to_string());
            }
        }
        
        // Create channels for file and process events
        let (file_event_tx, file_event_rx) = bounded(1000);
        let (process_event_tx, process_event_rx) = bounded(1000);
        
        Ok(Self {
            config,
            stop_tx: None,
            file_event_rx: Some(file_event_rx),
            file_event_tx: Some(file_event_tx),
            process_event_rx: Some(process_event_rx),
            process_event_tx: Some(process_event_tx),
            threat_handlers: Arc::new(threat_handlers),
            detection_thread: None,
            suspicious_file_patterns,
            suspicious_process_patterns,
            malicious_file_hashes,
            recent_file_events: Arc::new(DashMap::new()),
            recent_process_events: Arc::new(DashMap::new()),
        })
    }
    
    /// Creates a file event handler for this threat detector
    pub fn create_file_event_handler(&self) -> Option<Box<dyn FileEventHandler>> {
        self.file_event_tx.as_ref().map(|tx| {
            Box::new(ThreatDetectorFileEventHandler {
                file_event_tx: tx.clone(),
            }) as Box<dyn FileEventHandler>
        })
    }
    
    /// Creates a process event handler for this threat detector
    pub fn create_process_event_handler(&self) -> Option<Box<dyn ProcessEventHandler>> {
        self.process_event_tx.as_ref().map(|tx| {
            Box::new(ThreatDetectorProcessEventHandler {
                process_event_tx: tx.clone(),
            }) as Box<dyn ProcessEventHandler>
        })
    }
    
    /// Starts the threat detector
    pub fn start(&mut self) -> Result<()> {
        if self.detection_thread.is_some() {
            warn!("Threat detector is already running");
            return Ok(());
        }
        
        if !self.config.enabled {
            info!("Threat detection is disabled in configuration");
            return Ok(());
        }
        
        // Create a channel for stopping the detection thread
        let (stop_tx, stop_rx) = bounded(1);
        self.stop_tx = Some(stop_tx);
        
        // Take ownership of the event channels
        let file_event_rx = self.file_event_rx.take()
            .ok_or_else(|| anyhow::anyhow!("File event receiver not initialized"))?;
        let process_event_rx = self.process_event_rx.take()
            .ok_or_else(|| anyhow::anyhow!("Process event receiver not initialized"))?;
            
        // Clone necessary data for the detection thread
        let threat_handlers = self.threat_handlers.clone();
        let suspicious_file_patterns = self.suspicious_file_patterns.clone();
        let suspicious_process_patterns = self.suspicious_process_patterns.clone();
        let malicious_file_hashes = self.malicious_file_hashes.clone();
        let recent_file_events = self.recent_file_events.clone();
        let recent_process_events = self.recent_process_events.clone();
        
        // Start the detection thread
        let detection_thread = thread::spawn(move || {
            info!("Threat detection thread started");
            
            loop {
                select! {
                    recv(file_event_rx) -> result => {
                        match result {
                            Ok(event) => {
                                Self::process_file_event(
                                    event,
                                    &threat_handlers,
                                    &suspicious_file_patterns,
                                    &malicious_file_hashes,
                                    &recent_file_events,
                                );
                            }
                            Err(e) => {
                                error!("Failed to receive file event: {}", e);
                            }
                        }
                    }
                    recv(process_event_rx) -> result => {
                        match result {
                            Ok(event) => {
                                Self::process_process_event(
                                    event,
                                    &threat_handlers,
                                    &suspicious_process_patterns,
                                    &recent_process_events,
                                );
                            }
                            Err(e) => {
                                error!("Failed to receive process event: {}", e);
                            }
                        }
                    }
                    recv(stop_rx) -> _ => {
                        info!("Received stop signal, shutting down threat detector");
                        break;
                    }
                }
                
                // Check for correlations between events
                Self::check_correlations(
                    &threat_handlers,
                    &recent_file_events,
                    &recent_process_events,
                );
                
                // Clean up old events
                Self::cleanup_old_events(&recent_file_events, &recent_process_events);
            }
            
            info!("Threat detection thread stopped");
        });
        
        self.detection_thread = Some(detection_thread);
        info!("Threat detector started");
        
        Ok(())
    }
    
    /// Processes a file event
    fn process_file_event(
        event: FileEvent,
        threat_handlers: &Arc<Vec<Box<dyn ThreatHandler>>>,
        suspicious_patterns: &[Regex],
        malicious_hashes: &Arc<DashMap<String, String>>,
        recent_events: &Arc<DashMap<PathBuf, VecDeque<FileEvent>>>,
    ) {
        // Store the event for correlation
        let path = event.path.clone();
        recent_events
            .entry(path.clone())
            .or_insert_with(|| VecDeque::with_capacity(10))
            .push_back(event.clone());
            
        // Check for maximum event history
        if let Some(mut events) = recent_events.get_mut(&path) {
            while events.len() > 10 {
                events.pop_front();
            }
        }
        
        // Check for malicious file hash
        if let Some(hash) = &event.file_hash {
            if let Some(description) = malicious_hashes.get(hash) {
                // Create threat for known malicious hash
                let threat = Threat {
                    id: uuid::Uuid::new_v4().to_string(),
                    threat_type: ThreatType::Malware,
                    severity: ThreatSeverity::High,
                    description: format!("Known malicious file detected: {}", description),
                    file_path: Some(event.path.clone()),
                    process_id: None,
                    process_name: None,
                    detection_time: chrono::Utc::now(),
                    evidence: vec![
                        format!("File path: {}", event.path.display()),
                        format!("File hash (SHA-256): {}", hash),
                        format!("Event type: {:?}", event.event_type),
                        format!("Detection mechanism: Hash-based detection"),
                    ],
                    recommendations: vec![
                        "Quarantine the file immediately".to_string(),
                        "Scan the system for additional malware".to_string(),
                        "Investigate how the file was introduced to the system".to_string(),
                    ],
                };
                
                // Notify handlers
                for handler in threat_handlers.iter() {
                    handler.handle_threat(threat.clone());
                }
                
                return; // No need to check further
            }
        }
        
        // Check for suspicious file patterns
        let path_str = event.path.to_string_lossy();
        for pattern in suspicious_patterns {
            if pattern.is_match(&path_str) {
                // Create threat for suspicious file pattern
                let threat = Threat {
                    id: uuid::Uuid::new_v4().to_string(),
                    threat_type: ThreatType::SuspiciousFile,
                    severity: ThreatSeverity::Medium,
                    description: format!("Suspicious file activity detected: {}", path_str),
                    file_path: Some(event.path.clone()),
                    process_id: None,
                    process_name: None,
                    detection_time: chrono::Utc::now(),
                    evidence: vec![
                        format!("File path: {}", event.path.display()),
                        format!("Event type: {:?}", event.event_type),
                        format!("Matched pattern: {}", pattern),
                        format!("Detection mechanism: Pattern-based detection"),
                    ],
                    recommendations: vec![
                        "Investigate the file and its contents".to_string(),
                        "Check which process created or modified the file".to_string(),
                        "Consider scanning the file with an antivirus".to_string(),
                    ],
                };
                
                // Notify handlers
                for handler in threat_handlers.iter() {
                    handler.handle_threat(threat.clone());
                }
                
                return; // No need to check further
            }
        }
        
        // Check for ransomware-like behavior (e.g., rapid file modifications with extension changes)
        if let Some(events) = recent_events.get(&path) {
            let create_count = events.iter().filter(|e| matches!(e.event_type, FileEventType::Create)).count();
            let modify_count = events.iter().filter(|e| matches!(e.event_type, FileEventType::Modify)).count();
            
            // Check if there's a high rate of file changes
            if create_count + modify_count >= 5 {
                // Check if the file extension has changed
                if let Some(ext) = event.path.extension() {
                    let ext_str = ext.to_string_lossy().to_lowercase();
                    // Common ransomware extensions
                    let ransomware_extensions = ["locked", "encrypted", "crypted", "crypt", "cryp", "ransom"];
                    
                    if ransomware_extensions.iter().any(|&e| ext_str == e) 
                        || ext_str.len() >= 3 && ext_str.len() <= 10 {
                        // Create threat for potential ransomware activity
                        let threat = Threat {
                            id: uuid::Uuid::new_v4().to_string(),
                            threat_type: ThreatType::Ransomware,
                            severity: ThreatSeverity::Critical,
                            description: format!("Potential ransomware activity detected on {}", path_str),
                            file_path: Some(event.path.clone()),
                            process_id: None,
                            process_name: None,
                            detection_time: chrono::Utc::now(),
                            evidence: vec![
                                format!("File path: {}", event.path.display()),
                                format!("Suspicious extension: {}", ext_str),
                                format!("High file change rate: {} creations, {} modifications", create_count, modify_count),
                                format!("Detection mechanism: Behavioral analysis"),
                            ],
                            recommendations: vec![
                                "Disconnect the system from the network immediately".to_string(),
                                "Stop all processes that might be involved".to_string(),
                                "Preserve evidence for forensic analysis".to_string(),
                                "Restore from known good backups if available".to_string(),
                            ],
                        };
                        
                        // Notify handlers
                        for handler in threat_handlers.iter() {
                            handler.handle_threat(threat.clone());
                        }
                    }
                }
            }
        }
    }
    
    /// Processes a process event
    fn process_process_event(
        event: ProcessEvent,
        threat_handlers: &Arc<Vec<Box<dyn ThreatHandler>>>,
        suspicious_patterns: &[Regex],
        recent_events: &Arc<DashMap<u32, VecDeque<ProcessEvent>>>,
    ) {
        // Store the event for correlation
        let pid = event.pid;
        recent_events
            .entry(pid)
            .or_insert_with(|| VecDeque::with_capacity(10))
            .push_back(event.clone());
            
        // Check for maximum event history
        if let Some(mut events) = recent_events.get_mut(&pid) {
            while events.len() > 10 {
                events.pop_front();
            }
        }
        
        // Check if this is already a suspicious process
        if matches!(event.event_type, ProcessEventType::Suspicious) {
            // Create threat for suspicious process
            let threat = Threat {
                id: uuid::Uuid::new_v4().to_string(),
                threat_type: ThreatType::SuspiciousProcess,
                severity: ThreatSeverity::Medium,
                description: format!("Suspicious process detected: {}", event.name),
                file_path: Some(PathBuf::from(&event.executable_path)),
                process_id: Some(event.pid),
                process_name: Some(event.name.clone()),
                detection_time: chrono::Utc::now(),
                evidence: vec![
                    format!("Process name: {}", event.name),
                    format!("Process ID: {}", event.pid),
                    format!("Command line: {}", event.cmd_line),
                    format!("Executable path: {}", event.executable_path),
                    format!("Detection mechanism: Process name/path matching"),
                ],
                recommendations: vec![
                    "Investigate the process and its activity".to_string(),
                    "Check if the process is legitimate".to_string(),
                    "Consider terminating the process if it's malicious".to_string(),
                ],
            };
            
            // Notify handlers
            for handler in threat_handlers.iter() {
                handler.handle_threat(threat.clone());
            }
            
            return; // No need to check further
        }
        
        // Check for suspicious process patterns
        let cmd_line = &event.cmd_line;
        let executable_path = &event.executable_path;
        
        for pattern in suspicious_patterns {
            if pattern.is_match(cmd_line) || pattern.is_match(executable_path) {
                // Create threat for suspicious process pattern
                let threat = Threat {
                    id: uuid::Uuid::new_v4().to_string(),
                    threat_type: ThreatType::SuspiciousProcess,
                    severity: ThreatSeverity::Medium,
                    description: format!("Suspicious process behavior detected: {}", event.name),
                    file_path: Some(PathBuf::from(&event.executable_path)),
                    process_id: Some(event.pid),
                    process_name: Some(event.name.clone()),
                    detection_time: chrono::Utc::now(),
                    evidence: vec![
                        format!("Process name: {}", event.name),
                        format!("Process ID: {}", event.pid),
                        format!("Command line: {}", event.cmd_line),
                        format!("Executable path: {}", event.executable_path),
                        format!("Matched pattern: {}", pattern),
                        format!("Detection mechanism: Pattern-based detection"),
                    ],
                    recommendations: vec![
                        "Investigate the process and its activity".to_string(),
                        "Check if the process is legitimate".to_string(),
                        "Consider terminating the process if it's malicious".to_string(),
                    ],
                };
                
                // Notify handlers
                for handler in threat_handlers.iter() {
                    handler.handle_threat(threat.clone());
                }
                
                return; // No need to check further
            }
        }
        
        // Check for high resource usage
        if matches!(event.event_type, ProcessEventType::HighCpuUsage | ProcessEventType::HighMemoryUsage) {
            // Only report as a threat if the usage is very high
            if event.cpu_usage > 95.0 || (event.memory_usage > 1_000_000_000) {
                let resource_type = if matches!(event.event_type, ProcessEventType::HighCpuUsage) {
                    "CPU"
                } else {
                    "Memory"
                };
                
                // Create threat for high resource usage
                let threat = Threat {
                    id: uuid::Uuid::new_v4().to_string(),
                    threat_type: ThreatType::Other(format!("High {} Usage", resource_type)),
                    severity: ThreatSeverity::Low,
                    description: format!("Process with high {} usage: {}", resource_type.to_lowercase(), event.name),
                    file_path: Some(PathBuf::from(&event.executable_path)),
                    process_id: Some(event.pid),
                    process_name: Some(event.name.clone()),
                    detection_time: chrono::Utc::now(),
                    evidence: vec![
                        format!("Process name: {}", event.name),
                        format!("Process ID: {}", event.pid),
                        format!("CPU usage: {:.1}%", event.cpu_usage),
                        format!("Memory usage: {} MB", event.memory_usage / 1_024_000),
                        format!("Detection mechanism: Resource monitoring"),
                    ],
                    recommendations: vec![
                        "Investigate if the resource usage is expected".to_string(),
                        "Check for cryptominers or other resource-intensive malware".to_string(),
                        "Consider restarting the application if it's legitimate but misbehaving".to_string(),
                    ],
                };
                
                // Notify handlers
                for handler in threat_handlers.iter() {
                    handler.handle_threat(threat.clone());
                }
            }
        }
    }
    
    /// Checks for correlations between file and process events
    fn check_correlations(
        threat_handlers: &Arc<Vec<Box<dyn ThreatHandler>>>,
        file_events: &Arc<DashMap<PathBuf, VecDeque<FileEvent>>>,
        process_events: &Arc<DashMap<u32, VecDeque<ProcessEvent>>>,
    ) {
        // Look for processes creating suspicious files
        // This is a simple example; a real implementation would be more sophisticated
        for file_entry in file_events.iter() {
            let path = file_entry.key();
            let events = file_entry.value();
            
            // Skip if there are no events
            if events.is_empty() {
                continue;
            }
            
            // Check for suspicious file extensions
            if let Some(ext) = path.extension() {
                let ext_str = ext.to_string_lossy().to_lowercase();
                let suspicious_extensions = ["exe", "dll", "bat", "ps1", "vbs", "js"];
                
                if suspicious_extensions.contains(&ext_str.as_ref()) {
                    // Look for a process that created this file
                    for process_entry in process_events.iter() {
                        let pid = *process_entry.key();
                        let process = &process_entry.value()[0]; // Use the first event for process info
                        
                        // This is where we would correlate file creation events with processes
                        // For now, we'll just use a simple heuristic: if the process is recent and suspicious
                        
                        if process.name.to_lowercase().contains("powershell") 
                            || process.name.to_lowercase().contains("cmd") 
                            || process.cmd_line.contains("download") {
                            
                            // Create threat for suspicious file creation
                            let threat = Threat {
                                id: uuid::Uuid::new_v4().to_string(),
                                threat_type: ThreatType::SuspiciousFile,
                                severity: ThreatSeverity::High,
                                description: format!("Suspicious file created by potentially malicious process"),
                                file_path: Some(path.clone()),
                                process_id: Some(pid),
                                process_name: Some(process.name.clone()),
                                detection_time: chrono::Utc::now(),
                                evidence: vec![
                                    format!("File path: {}", path.display()),
                                    format!("File extension: {}", ext_str),
                                    format!("Process name: {}", process.name),
                                    format!("Process ID: {}", pid),
                                    format!("Command line: {}", process.cmd_line),
                                    format!("Detection mechanism: Event correlation"),
                                ],
                                recommendations: vec![
                                    "Investigate the file and its contents".to_string(),
                                    "Investigate the process that created the file".to_string(),
                                    "Consider quarantining the file and terminating the process".to_string(),
                                ],
                            };
                            
                            // Notify handlers
                            for handler in threat_handlers.iter() {
                                handler.handle_threat(threat.clone());
                            }
                            
                            break; // No need to check other processes
                        }
                    }
                }
            }
        }
    }
    
    /// Cleans up old events to prevent memory buildup
    fn cleanup_old_events(
        file_events: &Arc<DashMap<PathBuf, VecDeque<FileEvent>>>,
        process_events: &Arc<DashMap<u32, VecDeque<ProcessEvent>>>,
    ) {
        // Remove file events older than 5 minutes
        let file_cutoff = chrono::Utc::now() - chrono::Duration::minutes(5);
        file_events.retain(|_, events| {
            // Remove old events from the queue
            while let Some(event) = events.front() {
                if event.timestamp < file_cutoff {
                    events.pop_front();
                } else {
                    break;
                }
            }
            // Keep the entry if there are still events
            !events.is_empty()
        });
        
        // Remove process events older than 5 minutes
        let process_cutoff = chrono::Utc::now() - chrono::Duration::minutes(5);
        process_events.retain(|_, events| {
            // Remove old events from the queue
            while let Some(event) = events.front() {
                if event.timestamp < process_cutoff {
                    events.pop_front();
                } else {
                    break;
                }
            }
            // Keep the entry if there are still events
            !events.is_empty()
        });
    }
    
    /// Stops the threat detector
    pub fn stop(&mut self) -> Result<()> {
        if let Some(stop_tx) = self.stop_tx.take() {
            if let Err(e) = stop_tx.send(()) {
                error!("Failed to send stop signal: {}", e);
            }
            
            if let Some(thread) = self.detection_thread.take() {
                match thread.join() {
                    Ok(_) => {
                        info!("Threat detection thread joined successfully");
                    }
                    Err(e) => {
                        error!("Failed to join threat detection thread: {:?}", e);
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// Adds a malicious file hash
    pub fn add_malicious_hash(&self, hash: &str, description: &str) {
        self.malicious_file_hashes.insert(hash.to_string(), description.to_string());
    }
    
    /// Checks if a hash is known to be malicious
    pub fn is_hash_malicious(&self, hash: &str) -> Option<String> {
        self.malicious_file_hashes.get(hash).map(|s| s.clone())
    }
}

/// Creates a new threat detector with default configuration
pub fn create_default_threat_detector(config: ThreatDetectionConfig) -> Result<ThreatDetector> {
    let mut handlers: Vec<Box<dyn ThreatHandler>> = Vec::new();
    handlers.push(Box::new(LoggingThreatHandler));
    
    ThreatDetector::new(config, handlers)
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Mutex;
    
    /// A mock threat handler that captures threats for testing
    struct MockThreatHandler {
        threats: Arc<Mutex<Vec<Threat>>>,
    }
    
    impl MockThreatHandler {
        fn new() -> (Self, Arc<Mutex<Vec<Threat>>>) {
            let threats = Arc::new(Mutex::new(Vec::new()));
            (Self { threats: threats.clone() }, threats)
        }
    }
    
    impl ThreatHandler for MockThreatHandler {
        fn handle_threat(&self, threat: Threat) {
            let mut threats = self.threats.lock().unwrap();
            threats.push(threat);
        }
    }
    
    #[test]
    fn test_threat_detector_creation() {
        let config = ThreatDetectionConfig {
            enabled: true,
            suspicious_file_patterns: vec![r".*\.evil$".to_string()],
            suspicious_process_patterns: vec![r"^malware\.exe$".to_string()],
            malicious_file_hashes: vec![],
        };
        
        let result = create_default_threat_detector(config);
        assert!(result.is_ok());
    }
}
```

## Updating the Plugin Implementation

Now, let's update the `EndpointProtectionPlugin` to use our new `ThreatDetector` implementation:

**src/plugins/endpoint_protection/mod.rs** (partial update):

```rust
// ... existing imports ...
use self::threat_detection::{create_default_threat_detector, ThreatDetector};
// ... other imports ...

/// Endpoint Protection plugin for CyberShield
#[derive(Debug)]
pub struct EndpointProtectionPlugin {
    name: String,
    version: String,
    description: String,
    config: Arc<RwLock<EndpointProtectionConfig>>,
    file_monitor: Option<Arc<Mutex<FileMonitor>>>,
    process_monitor: Option<Arc<Mutex<ProcessMonitor>>>,
    threat_detector: Option<Arc<Mutex<ThreatDetector>>>,
    system_health_monitor: Option<Arc<SystemHealthMonitor>>,
}

impl EndpointProtectionPlugin {
    // ... existing methods ...

    /// Stops all monitoring components
    pub fn stop_all(&mut self) -> Result<()> {
        info!("Stopping all endpoint protection components");
        
        // Stop file monitoring
        if let Some(monitor) = &self.file_monitor {
            if let Ok(mut monitor) = monitor.lock() {
                if let Err(e) = monitor.stop() {
                    warn!("Error stopping file monitor: {}", e);
                }
            } else {
                warn!("Failed to acquire lock on file monitor");
            }
        }
        
        // Stop process monitoring
        if let Some(monitor) = &self.process_monitor {
            if let Ok(mut monitor) = monitor.lock() {
                if let Err(e) = monitor.stop() {
                    warn!("Error stopping process monitor: {}", e);
                }
            } else {
                warn!("Failed to acquire lock on process monitor");
            }
        }
        
        // Stop threat detection
        if let Some(detector) = &self.threat_detector {
            if let Ok(mut detector) = detector.lock() {
                if let Err(e) = detector.stop() {
                    warn!("Error stopping threat detector: {}", e);
                }
            } else {
                warn!("Failed to acquire lock on threat detector");
            }
        }
        
        // ... other monitors ...
        
        Ok(())
    }
}

impl Plugin for EndpointProtectionPlugin {
    // ... existing methods ...
    
    fn initialize(&self) -> Result<()> {
        info!("Initializing Endpoint Protection plugin");
        
        // Access configuration in a thread-safe way
        let config = match self.config.read() {
            Ok(config) => config.clone(),
            Err(e) => {
                warn!("Failed to read configuration: {}", e);
                return Err(anyhow::anyhow!("Failed to read configuration"));
            }
        };
        
        // Initialize threat detection first so we can get event handlers
        let mut threat_detector_opt = None;
        if config.threat_detection.enabled {
            debug!("Threat detection is enabled");
            
            // Create a threat detector
            match create_default_threat_detector(config.threat_detection.clone()) {
                Ok(detector) => {
                    threat_detector_opt = Some(Arc::new(Mutex::new(detector)));
                    info!("Threat detector created");
                }
                Err(e) => {
                    warn!("Failed to create threat detector: {}", e);
                }
            }
        }
        
        // Initialize file monitoring if enabled
        if config.file_monitoring.enabled {
            debug!("File monitoring is enabled");
            
            // Get file event handlers from threat detector
            let mut file_event_handlers = Vec::new();
            if let Some(ref detector) = threat_detector_opt {
                if let Ok(detector) = detector.lock() {
                    if let Some(handler) = detector.create_file_event_handler() {
                        file_event_handlers.push(handler);
                    }
                }
            }
            
            // Create and start a file monitor with our custom event handlers
            match create_file_monitor_with_handlers(config.file_monitoring.clone(), file_event_handlers) {
                Ok(mut monitor) => {
                    if let Err(e) = monitor.start() {
                        warn!("Failed to start file monitor: {}", e);
                    } else {
                        // Store the monitor in the plugin
                        let file_monitor = Arc::new(Mutex::new(monitor));
                        // Cast self to mutable (this is safe because we're only modifying our own field)
                        let this = unsafe { &*(self as *const Self as *mut Self) };
                        this.file_monitor = Some(file_monitor);
                        info!("File monitor started");
                    }
                }
                Err(e) => {
                    warn!("Failed to create file monitor: {}", e);
                }
            }
        }
        
        // Initialize process monitoring if enabled
        if config.process_monitoring.enabled {
            debug!("Process monitoring is enabled");
            
            // Get process event handlers from threat detector
            let mut process_event_handlers = Vec::new();
            if let Some(ref detector) = threat_detector_opt {
                if let Ok(detector) = detector.lock() {
                    if let Some(handler) = detector.create_process_event_handler() {
                        process_event_handlers.push(handler);
                    }
                }
            }
            
            // Create and start a process monitor with our custom event handlers
            match create_process_monitor_with_handlers(config.process_monitoring.clone(), process_event_handlers) {
                Ok(mut monitor) => {
                    if let Err(e) = monitor.start() {
                        warn!("Failed to start process monitor: {}", e);
                    } else {
                        // Store the monitor in the plugin
                        let process_monitor = Arc::new(Mutex::new(monitor));
                        // Cast self to mutable (this is safe because we're only modifying our own field)
                        let this = unsafe { &*(self as *const Self as *mut Self) };
                        this.process_monitor = Some(process_monitor);
                        info!("Process monitor started");
                    }
                }
                Err(e) => {
                    warn!("Failed to create process monitor: {}", e);
                }
            }
        }
        
        // Start threat detector if it was created
        if let Some(ref detector) = threat_detector_opt {
            if let Ok(mut detector) = detector.lock() {
                if let Err(e) = detector.start() {
                    warn!("Failed to start threat detector: {}", e);
                } else {
                    // Store the detector in the plugin
                    let this = unsafe { &*(self as *const Self as *mut Self) };
                    this.threat_detector = Some(detector_opt.clone());
                    info!("Threat detector started");
                }
            }
        }
        
        // ... other monitors ...
        
        info!("Endpoint Protection plugin initialized successfully");
        Ok(())
    }
    
    // ... existing methods ...
}

/// Creates a file monitor with custom event handlers
fn create_file_monitor_with_handlers(
    config: FileMonitoringConfig,
    mut custom_handlers: Vec<Box<dyn FileEventHandler>>,
) -> Result<FileMonitor> {
    // Add default logging handler
    custom_handlers.push(Box::new(file_monitor::LoggingEventHandler));
    
    file_monitor::FileMonitor::new(config, custom_handlers)
}

/// Creates a process monitor with custom event handlers
fn create_process_monitor_with_handlers(
    config: ProcessMonitoringConfig,
    mut custom_handlers: Vec<Box<dyn ProcessEventHandler>>,
) -> Result<ProcessMonitor> {
    // Add default logging handler
    custom_handlers.push(Box::new(process_monitor::LoggingProcessEventHandler));
    
    process_monitor::ProcessMonitor::new(config, custom_handlers)
}
```

## Running and Testing the Threat Detector

Let's create a simple example to demonstrate the threat detection system:

```rust
fn main() -> Result<()> {
    // Initialize logging
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info")).init();
    
    // Create configurations
    let file_config = FileMonitoringConfig::default();
    let process_config = ProcessMonitoringConfig::default();
    let threat_config = ThreatDetectionConfig {
        enabled: true,
        suspicious_file_patterns: vec![
            r".*\.evil$".to_string(),
            r".*\.ransom$".to_string(),
            r".*\.encrypted$".to_string(),
        ],
        suspicious_process_patterns: vec![
            r".*powershell.*-enc.*".to_string(),
            r".*cmd.*\/c.*download.*".to_string(),
        ],
        malicious_file_hashes: vec![
            "e1a010c12eca7688a697ea7e22741c4ac0304075effe0c733057d9513d69c1b4:EICAR Test File".to_string(),
        ],
    };
    
    // Create and initialize the Endpoint Protection plugin
    let plugin = EndpointProtectionPlugin::new(None)?;
    plugin.initialize()?;
    
    println!("Threat detection is running. Press Enter to stop...");
    let mut input = String::new();
    std::io::stdin().read_line(&mut input)?;
    
    // Cast to mutable reference (safe because we own the plugin)
    let plugin_mut = unsafe { &*((&plugin) as *const _ as *mut EndpointProtectionPlugin) };
    plugin_mut.stop_all()?;
    
    Ok(())
}
```

## Rust Concepts and Design Decisions

### Advanced Rust Concepts Used

#### 1. Event-Driven Architecture with Channels

We've used `crossbeam_channel` to create an event-driven architecture:

```rust
let (file_event_tx, file_event_rx) = bounded(1000);
let (process_event_tx, process_event_rx) = bounded(1000);
```

This allows efficient communication between different components of our system.

#### 2. Select for Multi-Channel Receiving

We've used the `select!` macro from `crossbeam_channel` to efficiently wait on multiple channels simultaneously:

```rust
select! {
    recv(file_event_rx) -> result => { ... }
    recv(process_event_rx) -> result => { ... }
    recv(stop_rx) -> _ => { ... }
}
```

This pattern allows our code to respond to events from different sources without busy waiting or complex synchronization.

#### 3. Pattern Matching with Regular Expressions

We've used the `regex` crate to implement pattern matching for threat detection:

```rust
let suspicious_file_patterns = config
    .suspicious_file_patterns
    .iter()
    .map(|pattern| Regex::new(pattern).with_context(...))
    .collect::<Result<Vec<_>>>()?;
```

Regular expressions provide a powerful way to define complex patterns for matching suspicious files and processes.

#### 4. Thread-Safe Collection with DashMap

We've used `DashMap` as a thread-safe alternative to `HashMap`:

```rust
recent_file_events: Arc<DashMap<PathBuf, VecDeque<FileEvent>>>,
```

This allows concurrent access to our event history without requiring locks around every access.

#### 5. Event Correlation Logic

We've implemented event correlation to detect complex threat patterns:

```rust
fn check_correlations(
    threat_handlers: &Arc<Vec<Box<dyn ThreatHandler>>>,
    file_events: &Arc<DashMap<PathBuf, VecDeque<FileEvent>>>,
    process_events: &Arc<DashMap<u32, VecDeque<ProcessEvent>>>,
) { ... }
```

This approach allows us to detect threats that involve multiple related events, rather than just individual suspicious activities.

### Design Decisions

#### Hierarchical Event Processing

We've designed our threat detection system with a hierarchical event processing model:

1. **Event Sources**: File and process monitors generate low-level events
2. **Event Handlers**: Pass events to the threat detector
3. **Threat Detection**: Analyzes events to identify threats
4. **Threat Handling**: Acts on detected threats through threat handlers

This separation of concerns allows each component to focus on its specific responsibility.

#### Multi-Layered Detection Approach

We've implemented multiple layers of detection:

1. **Signature-based detection**: Using known file hashes
2. **Pattern-based detection**: Using regular expressions to match suspicious patterns
3. **Behavioral analysis**: Identifying unusual patterns of activity
4. **Correlation-based detection**: Connecting related events to identify complex threats

This multi-layered approach increases the chance of detecting various types of threats.

#### Event History for Context

We maintain a history of recent events to provide context for detection:

```rust
recent_file_events: Arc<DashMap<PathBuf, VecDeque<FileEvent>>>,
recent_process_events: Arc<DashMap<u32, VecDeque<ProcessEvent>>>,
```

This allows our detector to identify patterns that evolve over time, such as ransomware encrypting multiple files or processes exhibiting unusual behavior sequences.

#### Resource Management

We've implemented cleanup mechanisms to prevent memory growth:

```rust
fn cleanup_old_events(
    file_events: &Arc<DashMap<PathBuf, VecDeque<FileEvent>>>,
    process_events: &Arc<DashMap<u32, VecDeque<ProcessEvent>>>,
) { ... }
```

This ensures our application remains efficient even when running for extended periods.

### Alternatives Considered

#### Alternative 1: Rule Engine Approach

We could have implemented a formal rule engine where detection rules are defined in a domain-specific language. This would offer more flexibility but would be more complex to implement and might introduce performance overhead.

#### Alternative 2: Machine Learning-Based Detection

We could have integrated machine learning models for anomaly detection. This would potentially catch more sophisticated threats but would require training data and would add significant complexity.

#### Alternative 3: Centralized Event Bus

Instead of direct connections between components via channels, we could have implemented a centralized event bus. This would be more flexible for adding new components but would introduce more indirection and potentially more overhead.

#### Alternative 4: Database-Backed Event Storage

We could have stored events in a database instead of in-memory structures. This would allow for longer retention and more complex queries but would introduce significant I/O overhead and external dependencies.

## Exercise

Try extending the `ThreatDetector` with:

1. A new detection algorithm that identifies "living off the land" attacks (where attackers use legitimate system tools for malicious purposes)
2. A threat handler that generates system notifications when critical threats are detected
3. A mechanism to save threat reports to a file for later analysis

## Next Steps

In the next section, we'll implement system health monitoring to track the overall security posture of the endpoint and provide diagnostic information.

## Navigation

- Previous: [Process Monitoring](./04-process-monitoring.md)
- Next: [System Health Reporting](./06-system-health.md)
