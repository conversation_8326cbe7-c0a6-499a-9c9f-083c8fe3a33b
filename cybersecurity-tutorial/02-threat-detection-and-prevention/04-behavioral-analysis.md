# Behavioral Analysis

Behavioral analysis is a powerful approach to threat detection that focuses on identifying suspicious activities and patterns in how programs and users interact with a system. Unlike signature-based detection, behavioral analysis can identify previously unknown threats by monitoring for unusual behaviors.

## Overview

In this section, we'll implement behavioral analysis techniques in Rust for detecting potential security threats by:

1. Monitoring process behavior over time
2. Tracking file system interactions
3. Analyzing network activity patterns
4. Creating behavioral baselines
5. Detecting deviations from normal behavior

## Core Concepts

### Behavioral Patterns

Behavioral analysis works by establishing patterns of normal behavior and identifying anomalies. Some key behaviors to monitor:

- Process creation/termination sequences
- File access patterns
- Network connection patterns
- Registry/configuration modifications
- Resource utilization (CPU, memory, disk)
- User activity patterns

### Stateful Analysis

Unlike signature-based detection, behavioral analysis requires maintaining state over time to build a profile of normal behavior.

## Implementation

### 1. Behavior Monitoring Base Structure

Let's create the foundation of our behavioral analysis system:

```rust
use std::collections::{HashMap, VecDeque};
use std::time::{Duration, Instant};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

// A generic event that can represent any system activity
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BehaviorEvent {
    pub id: String,  
    pub timestamp: u64,
    pub event_type: BehaviorEventType,
    pub process_id: Option<u32>,
    pub process_name: Option<String>,
    pub user: Option<String>,
    pub resource: Option<String>,
    pub details: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum BehaviorEventType {
    ProcessStart,
    ProcessExit,
    FileAccess,
    FileModification,
    FileExecution,
    NetworkConnection,
    NetworkDataTransfer,
    ConfigurationChange,
    PrivilegeEscalation,
    UserAuthentication,
    Other(String),
}

impl BehaviorEvent {
    pub fn new(event_type: BehaviorEventType) -> Self {
        BehaviorEvent {
            id: Uuid::new_v4().to_string(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            event_type,
            process_id: None,
            process_name: None,
            user: None,
            resource: None,
            details: HashMap::new(),
        }
    }
    
    pub fn with_process(mut self, pid: u32, name: impl Into<String>) -> Self {
        self.process_id = Some(pid);
        self.process_name = Some(name.into());
        self
    }
    
    pub fn with_user(mut self, user: impl Into<String>) -> Self {
        self.user = Some(user.into());
        self
    }
    
    pub fn with_resource(mut self, resource: impl Into<String>) -> Self {
        self.resource = Some(resource.into());
        self
    }
    
    pub fn add_detail(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.details.insert(key.into(), value.into());
        self
    }
}
```

### 2. Event Collection System

To analyze behavior, we need to collect and store events for analysis:

```rust
pub struct BehaviorCollector {
    events: VecDeque<BehaviorEvent>,
    max_events: usize,
    window_duration: Duration,
}

impl BehaviorCollector {
    pub fn new(max_events: usize, window_duration: Duration) -> Self {
        BehaviorCollector {
            events: VecDeque::with_capacity(max_events),
            max_events,
            window_duration,
        }
    }
    
    pub fn add_event(&mut self, event: BehaviorEvent) {
        // Add the new event
        self.events.push_back(event);
        
        // Remove events that are too old or if we exceed capacity
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
            
        // Remove old events outside our time window
        while let Some(front) = self.events.front() {
            if current_time - front.timestamp > self.window_duration.as_secs() {
                self.events.pop_front();
            } else {
                break;
            }
        }
        
        // If still over capacity, remove oldest
        while self.events.len() > self.max_events {
            self.events.pop_front();
        }
    }
    
    pub fn get_events_by_type(&self, event_type: &BehaviorEventType) -> Vec<&BehaviorEvent> {
        self.events
            .iter()
            .filter(|event| event.event_type == *event_type)
            .collect()
    }
    
    pub fn get_events_by_process(&self, pid: u32) -> Vec<&BehaviorEvent> {
        self.events
            .iter()
            .filter(|event| event.process_id == Some(pid))
            .collect()
    }
    
    pub fn clear(&mut self) {
        self.events.clear();
    }
}
```

### 3. Process Behavior Analysis

Let's implement a system for tracking and analyzing process behavior:

```rust
pub struct ProcessBehaviorAnalyzer {
    collector: BehaviorCollector,
    process_profiles: HashMap<u32, ProcessProfile>,
}

#[derive(Debug, Clone)]
pub struct ProcessProfile {
    pid: u32,
    name: String,
    first_seen: u64,
    last_seen: u64,
    file_access_count: usize,
    network_connections: Vec<NetworkConnection>,
    sensitive_operations: Vec<SensitiveOperation>,
    behavior_score: f64, // Higher is more suspicious
}

#[derive(Debug, Clone)]
pub struct NetworkConnection {
    timestamp: u64,
    destination: String,
    port: u16,
    protocol: String,
    bytes_sent: usize,
    bytes_received: usize,
}

#[derive(Debug, Clone)]
pub struct SensitiveOperation {
    timestamp: u64, 
    operation_type: String,
    resource: String,
    details: String,
}

impl ProcessBehaviorAnalyzer {
    pub fn new(window_duration: Duration) -> Self {
        ProcessBehaviorAnalyzer {
            collector: BehaviorCollector::new(10000, window_duration),
            process_profiles: HashMap::new(),
        }
    }
    
    pub fn process_event(&mut self, event: BehaviorEvent) -> Option<f64> {
        // Add to the collector
        self.collector.add_event(event.clone());
        
        // Process the event based on type
        match &event.event_type {
            BehaviorEventType::ProcessStart => {
                if let (Some(pid), Some(name)) = (event.process_id, &event.process_name) {
                    self.register_new_process(pid, name.clone(), event.timestamp);
                }
                None
            },
            
            BehaviorEventType::ProcessExit => {
                if let Some(pid) = event.process_id {
                    // Clean up the process profile if needed
                    if let Some(profile) = self.process_profiles.get_mut(&pid) {
                        profile.last_seen = event.timestamp;
                    }
                }
                None
            },
            
            BehaviorEventType::FileAccess | BehaviorEventType::FileModification | BehaviorEventType::FileExecution => {
                if let Some(pid) = event.process_id {
                    self.update_file_activity(pid, event);
                    // Return anomaly score for this process
                    self.process_profiles.get(&pid).map(|p| p.behavior_score)
                } else {
                    None
                }
            },
            
            BehaviorEventType::NetworkConnection | BehaviorEventType::NetworkDataTransfer => {
                if let Some(pid) = event.process_id {
                    self.update_network_activity(pid, event);
                    // Return anomaly score for this process
                    self.process_profiles.get(&pid).map(|p| p.behavior_score)
                } else {
                    None
                }
            },
            
            BehaviorEventType::ConfigurationChange | BehaviorEventType::PrivilegeEscalation => {
                if let Some(pid) = event.process_id {
                    self.update_sensitive_operations(pid, event);
                    // These are higher risk operations, so return the score
                    self.process_profiles.get(&pid).map(|p| p.behavior_score)
                } else {
                    None
                }
            },
            
            _ => None
        }
    }
    
    fn register_new_process(&mut self, pid: u32, name: String, timestamp: u64) {
        let profile = ProcessProfile {
            pid,
            name,
            first_seen: timestamp,
            last_seen: timestamp,
            file_access_count: 0,
            network_connections: vec![],
            sensitive_operations: vec![],
            behavior_score: 0.0,
        };
        
        self.process_profiles.insert(pid, profile);
    }
    
    fn update_file_activity(&mut self, pid: u32, event: BehaviorEvent) {
        if let Some(profile) = self.process_profiles.get_mut(&pid) {
            profile.file_access_count += 1;
            profile.last_seen = event.timestamp;
            
            // Update behavior score based on file activity
            // Example: Processes that suddenly access many files are suspicious
            let recent_file_events = self.collector.get_events_by_process(pid)
                .iter()
                .filter(|e| matches!(e.event_type, 
                    BehaviorEventType::FileAccess | 
                    BehaviorEventType::FileModification | 
                    BehaviorEventType::FileExecution))
                .count();
                
            // Calculate rate of file accesses (per minute)
            let time_window = 60; // seconds
            let current_rate = recent_file_events as f64 / time_window as f64;
            
            // Adjust score - higher rates are more suspicious
            // This is a simple example - real systems would use more sophisticated calculations
            profile.behavior_score = profile.behavior_score * 0.8 + current_rate * 2.0;
        }
    }
    
    fn update_network_activity(&mut self, pid: u32, event: BehaviorEvent) {
        if let Some(profile) = self.process_profiles.get_mut(&pid) {
            profile.last_seen = event.timestamp;
            
            // Create network connection record
            if event.event_type == BehaviorEventType::NetworkConnection {
                if let (Some(destination), Some(bytes_sent)) = (
                    event.details.get("destination"),
                    event.details.get("bytes_sent").and_then(|s| s.parse::<usize>().ok())
                ) {
                    let port = event.details.get("port")
                        .and_then(|p| p.parse::<u16>().ok())
                        .unwrap_or(0);
                    
                    let protocol = event.details.get("protocol")
                        .cloned()
                        .unwrap_or_else(|| "unknown".to_string());
                        
                    let bytes_received = event.details.get("bytes_received")
                        .and_then(|b| b.parse::<usize>().ok())
                        .unwrap_or(0);
                        
                    let conn = NetworkConnection {
                        timestamp: event.timestamp,
                        destination: destination.clone(),
                        port,
                        protocol,
                        bytes_sent,
                        bytes_received,
                    };
                    
                    profile.network_connections.push(conn);
                    
                    // Analyze network behavior
                    // Example: Programs that suddenly start networking are suspicious
                    if profile.network_connections.len() == 1 && profile.file_access_count > 10 {
                        // This process accessed files first, then started networking - could be data exfiltration
                        profile.behavior_score += 10.0;
                    }
                }
            }
        }
    }
    
    fn update_sensitive_operations(&mut self, pid: u32, event: BehaviorEvent) {
        if let Some(profile) = self.process_profiles.get_mut(&pid) {
            profile.last_seen = event.timestamp;
            
            // Extract operation details
            let operation_type = match event.event_type {
                BehaviorEventType::ConfigurationChange => "ConfigChange",
                BehaviorEventType::PrivilegeEscalation => "PrivEscalation",
                _ => "Unknown",
            }.to_string();
            
            let resource = event.resource.clone().unwrap_or_else(|| "unknown".to_string());
            
            let details = event.details.get("operation")
                .cloned()
                .unwrap_or_else(|| "unknown".to_string());
                
            // Record the sensitive operation
            let operation = SensitiveOperation {
                timestamp: event.timestamp,
                operation_type,
                resource,
                details,
            };
            
            profile.sensitive_operations.push(operation);
            
            // Sensitive operations are inherently risky
            profile.behavior_score += 5.0;
            
            // Multiple sensitive operations in quick succession are very suspicious
            let recent_sensitive_ops = profile.sensitive_operations.iter()
                .filter(|op| event.timestamp - op.timestamp < 300) // within 5 minutes
                .count();
                
            if recent_sensitive_ops > 3 {
                profile.behavior_score += 20.0;
            }
        }
    }
    
    pub fn get_suspicious_processes(&self, threshold: f64) -> Vec<(&u32, &ProcessProfile)> {
        self.process_profiles
            .iter()
            .filter(|(_, profile)| profile.behavior_score > threshold)
            .collect()
    }
}
```

### 4. Sequence Pattern Detection

Let's implement a system to detect suspicious sequences of events:

```rust
pub struct SequenceDetector {
    event_sequences: HashMap<Vec<BehaviorEventType>, f64>, // Map of sequence to suspicion score
    current_sequences: HashMap<u32, VecDeque<BehaviorEventType>>, // Track sequence by process ID
    max_sequence_length: usize,
}

impl SequenceDetector {
    pub fn new(max_sequence_length: usize) -> Self {
        let mut detector = SequenceDetector {
            event_sequences: HashMap::new(),
            current_sequences: HashMap::new(),
            max_sequence_length,
        };
        
        // Define known suspicious sequences
        // Format: Vec<BehaviorEventType>, score
        
        // Example: Process starts, accesses files, makes network connection (potential data exfil)
        detector.add_suspicious_sequence(vec![
            BehaviorEventType::ProcessStart,
            BehaviorEventType::FileAccess,
            BehaviorEventType::FileAccess,
            BehaviorEventType::NetworkConnection,
        ], 75.0);
        
        // Example: Process starts, modifies system configuration, gains privileges
        detector.add_suspicious_sequence(vec![
            BehaviorEventType::ProcessStart,
            BehaviorEventType::ConfigurationChange,
            BehaviorEventType::PrivilegeEscalation,
        ], 90.0);
        
        // Example: Process starts, executes new file, that file connects to network
        detector.add_suspicious_sequence(vec![
            BehaviorEventType::ProcessStart,
            BehaviorEventType::FileExecution,
            BehaviorEventType::NetworkConnection,
        ], 65.0);
        
        detector
    }
    
    pub fn add_suspicious_sequence(&mut self, sequence: Vec<BehaviorEventType>, score: f64) {
        self.event_sequences.insert(sequence, score);
    }
    
    pub fn process_event(&mut self, event: &BehaviorEvent) -> Option<f64> {
        let pid = match event.process_id {
            Some(pid) => pid,
            None => return None,
        };
        
        // Get or create the sequence for this process
        let sequence = self.current_sequences
            .entry(pid)
            .or_insert_with(|| VecDeque::with_capacity(self.max_sequence_length));
            
        // Add the current event type
        sequence.push_back(event.event_type.clone());
        
        // Keep sequence within max length
        if sequence.len() > self.max_sequence_length {
            sequence.pop_front();
        }
        
        // Check if current sequence matches any known suspicious patterns
        let mut max_score = 0.0;
        
        for (suspicious_seq, score) in &self.event_sequences {
            if self.contains_subsequence(sequence, suspicious_seq) {
                max_score = max_score.max(*score);
            }
        }
        
        if max_score > 0.0 {
            Some(max_score)
        } else {
            None
        }
    }
    
    // Check if the current sequence contains a subsequence matching a suspicious pattern
    fn contains_subsequence(&self, 
                          haystack: &VecDeque<BehaviorEventType>, 
                          needle: &[BehaviorEventType]) -> bool {
        if needle.len() > haystack.len() {
            return false;
        }
        
        // Try finding the pattern at each starting position
        for i in 0..=haystack.len() - needle.len() {
            let mut matches = true;
            
            for j in 0..needle.len() {
                if haystack[i + j] != needle[j] {
                    matches = false;
                    break;
                }
            }
            
            if matches {
                return true;
            }
        }
        
        false
    }
}
```

### 5. Integration with the Threat Detection Engine

Now, let's integrate behavioral analysis with our main threat detection engine:

```rust
use std::sync::{Arc, Mutex};
use std::time::Duration;

pub struct BehavioralDetectionEngine {
    process_analyzer: Arc<Mutex<ProcessBehaviorAnalyzer>>,
    sequence_detector: Arc<Mutex<SequenceDetector>>,
    suspicion_threshold: f64,
}

impl BehavioralDetectionEngine {
    pub fn new(suspicion_threshold: f64) -> Self {
        BehavioralDetectionEngine {
            process_analyzer: Arc::new(Mutex::new(
                ProcessBehaviorAnalyzer::new(Duration::from_secs(3600))
            )),
            sequence_detector: Arc::new(Mutex::new(
                SequenceDetector::new(10)
            )),
            suspicion_threshold,
        }
    }
    
    pub fn analyze_event(&self, event: BehaviorEvent) -> Option<ThreatAlert> {
        let mut total_score = 0.0;
        
        // Process analyzer score
        if let Some(score) = self.process_analyzer.lock().unwrap().process_event(event.clone()) {
            total_score += score;
        }
        
        // Sequence detector score
        if let Some(score) = self.sequence_detector.lock().unwrap().process_event(&event) {
            total_score += score;
        }
        
        // If we exceed the threshold, generate an alert
        if total_score > self.suspicion_threshold {
            let pid = event.process_id.unwrap_or(0);
            let process_name = event.process_name.clone().unwrap_or_else(|| "Unknown".to_string());
            
            Some(ThreatAlert {
                timestamp: event.timestamp,
                alert_type: "Behavioral".to_string(),
                severity: if total_score > self.suspicion_threshold * 2.0 { 
                    "Critical" 
                } else { 
                    "Warning" 
                }.to_string(),
                message: format!("Suspicious behavior detected in process {} (PID: {})", 
                                process_name, pid),
                score: total_score,
                details: event.details.clone(),
            })
        } else {
            None
        }
    }
    
    pub fn get_suspicious_processes(&self) -> Vec<(u32, String, f64)> {
        let analyzer = self.process_analyzer.lock().unwrap();
        
        analyzer.get_suspicious_processes(self.suspicion_threshold)
            .iter()
            .map(|(pid, profile)| {
                (**pid, profile.name.clone(), profile.behavior_score)
            })
            .collect()
    }
}

#[derive(Debug, Clone)]
pub struct ThreatAlert {
    pub timestamp: u64,
    pub alert_type: String,
    pub severity: String,
    pub message: String,
    pub score: f64,
    pub details: HashMap<String, String>,
}
```

## Testing Behavioral Detection

Let's build a test harness to verify our behavioral detection:

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;
    
    #[test]
    fn test_sequence_detection() {
        // Create a detector
        let mut detector = SequenceDetector::new(5);
        
        // Add a suspicious sequence (file access then network)
        detector.add_suspicious_sequence(vec![
            BehaviorEventType::FileAccess,
            BehaviorEventType::NetworkConnection,
        ], 50.0);
        
        // Create test events
        let mut events = vec![
            BehaviorEvent::new(BehaviorEventType::ProcessStart)
                .with_process(1000, "test.exe"),
            BehaviorEvent::new(BehaviorEventType::FileAccess)
                .with_process(1000, "test.exe")
                .with_resource("sensitive_file.dat"),
            BehaviorEvent::new(BehaviorEventType::NetworkConnection)
                .with_process(1000, "test.exe")
                .add_detail("destination", "suspicious-server.com")
                .add_detail("port", "443")
        ];
        
        // Process the events
        let mut detected = false;
        
        for event in &events {
            if let Some(score) = detector.process_event(event) {
                detected = true;
                assert!(score > 0.0, "Detection score should be positive");
            }
        }
        
        assert!(detected, "Suspicious sequence should be detected");
    }
    
    #[test]
    fn test_process_behavior() {
        let mut analyzer = ProcessBehaviorAnalyzer::new(Duration::from_secs(60));
        
        // Simulate a process with suspicious behavior
        let start_event = BehaviorEvent::new(BehaviorEventType::ProcessStart)
            .with_process(2000, "suspicious.exe");
            
        analyzer.process_event(start_event);
        
        // Simulate rapid file access
        for i in 0..30 {
            let file_event = BehaviorEvent::new(BehaviorEventType::FileAccess)
                .with_process(2000, "suspicious.exe")
                .with_resource(&format!("file_{}.txt", i));
                
            analyzer.process_event(file_event);
        }
        
        // Simulate network connection after file access
        let network_event = BehaviorEvent::new(BehaviorEventType::NetworkConnection)
            .with_process(2000, "suspicious.exe")
            .add_detail("destination", "unknown-server.net")
            .add_detail("bytes_sent", "1024000"); // 1MB sent
            
        analyzer.process_event(network_event);
        
        // Check if the process is flagged as suspicious
        let suspicious = analyzer.get_suspicious_processes(10.0);
        assert!(!suspicious.is_empty(), "Process should be flagged as suspicious");
    }
}
```

## Practical Usage Example

Here's how to use the behavioral detection system in a real-world application:

```rust
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;

fn main() {
    // Create the behavioral detection engine
    let engine = Arc::new(Mutex::new(BehavioralDetectionEngine::new(50.0)));
    
    // Clone for event generation thread
    let engine_clone = engine.clone();
    
    // Start event collection in a background thread
    thread::spawn(move || {
        let system_monitor = create_system_monitor();
        
        loop {
            // Collect events from the system
            if let Some(event) = system_monitor.poll_next_event() {
                if let Some(alert) = engine_clone.lock().unwrap().analyze_event(event) {
                    println!("ALERT: {} (Score: {})", alert.message, alert.score);
                    println!("Severity: {}", alert.severity);
                    println!("Details: {:?}", alert.details);
                }
            }
            
            thread::sleep(Duration::from_millis(100));
        }
    });
    
    // Periodically check for suspicious processes
    loop {
        thread::sleep(Duration::from_secs(60));
        
        let suspicious = engine.lock().unwrap().get_suspicious_processes();
        
        for (pid, name, score) in suspicious {
            println!("Suspicious Process: {} (PID: {}) - Score: {}", name, pid, score);
        }
    }
}

// Example function - in reality, you would interface with system APIs
fn create_system_monitor() -> impl SystemMonitor {
    // Your implementation of a system monitor that produces BehaviorEvent instances
    // This would integrate with OS-specific APIs or with the endpoint protection
    // module we created earlier
    
    // Placeholder implementation
    MySystemMonitor::new()
}

trait SystemMonitor {
    fn poll_next_event(&self) -> Option<BehaviorEvent>;
}

struct MySystemMonitor {
    // Implementation details
}

impl MySystemMonitor {
    fn new() -> Self {
        // Initialize the monitor
        Self {
            // ...
        }
    }
}

impl SystemMonitor for MySystemMonitor {
    fn poll_next_event(&self) -> Option<BehaviorEvent> {
        // Implementation to collect real system events
        // This would be a bridge to the OS-specific monitoring APIs
        None // Placeholder
    }
}
```

## Advanced Techniques

### Establishing Behavioral Baselines

For effective behavioral detection, establishing normal behavior baselines is crucial:

```rust
pub struct BehavioralBaseline {
    process_name: String,
    typical_file_access_rate: f64,     // Files per minute
    typical_network_activity: f64,     // Connections per minute
    known_file_paths: Vec<String>,     // Files this process typically accesses
    known_network_connections: Vec<String>, // Hosts this process typically connects to
    creation_timestamp: u64,
    last_updated: u64,
    sample_count: usize,
}

pub struct BaselineManager {
    baselines: HashMap<String, BehavioralBaseline>,
    learning_mode: bool,
}

impl BaselineManager {
    pub fn new(learning_mode: bool) -> Self {
        BaselineManager {
            baselines: HashMap::new(),
            learning_mode,
        }
    }
    
    // Update baseline with new observations
    pub fn observe_process(&mut self, process_name: &str, events: &[BehaviorEvent]) {
        if !self.learning_mode {
            return;
        }
        
        let baseline = self.baselines
            .entry(process_name.to_string())
            .or_insert_with(|| BehavioralBaseline {
                process_name: process_name.to_string(),
                typical_file_access_rate: 0.0,
                typical_network_activity: 0.0,
                known_file_paths: Vec::new(),
                known_network_connections: Vec::new(),
                creation_timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs(),
                last_updated: 0,
                sample_count: 0,
            });
        
        // Update the baseline with new observations
        // (This is a simplified example - real implementations would be more sophisticated)
        
        let file_events = events.iter()
            .filter(|e| matches!(e.event_type, 
                BehaviorEventType::FileAccess | 
                BehaviorEventType::FileModification))
            .count() as f64;
            
        let network_events = events.iter()
            .filter(|e| matches!(e.event_type,
                BehaviorEventType::NetworkConnection |
                BehaviorEventType::NetworkDataTransfer))
            .count() as f64;
            
        // Update rates (simple exponential smoothing)
        let alpha = 0.3; // Smoothing factor
        baseline.typical_file_access_rate = 
            baseline.typical_file_access_rate * (1.0 - alpha) + 
            file_events * alpha;
            
        baseline.typical_network_activity = 
            baseline.typical_network_activity * (1.0 - alpha) + 
            network_events * alpha;
            
        // Update file paths
        for event in events.iter().filter(|e| e.event_type == BehaviorEventType::FileAccess) {
            if let Some(resource) = &event.resource {
                if !baseline.known_file_paths.contains(resource) {
                    baseline.known_file_paths.push(resource.clone());
                }
            }
        }
        
        // Update network connections
        for event in events.iter().filter(|e| e.event_type == BehaviorEventType::NetworkConnection) {
            if let Some(destination) = event.details.get("destination") {
                if !baseline.known_network_connections.contains(destination) {
                    baseline.known_network_connections.push(destination.clone());
                }
            }
        }
        
        baseline.sample_count += 1;
        baseline.last_updated = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
    }
    
    // Check if behavior deviates from baseline
    pub fn check_deviation(&self, process_name: &str, events: &[BehaviorEvent]) -> Option<f64> {
        let baseline = match self.baselines.get(process_name) {
            Some(b) => b,
            None => return None, // No baseline exists
        };
        
        if baseline.sample_count < 5 {
            return None; // Not enough samples for a reliable baseline
        }
        
        // Calculate deviation scores
        let mut total_deviation = 0.0;
        
        // File access rate deviation
        let file_events = events.iter()
            .filter(|e| matches!(e.event_type, 
                BehaviorEventType::FileAccess | 
                BehaviorEventType::FileModification))
            .count() as f64;
            
        if file_events > baseline.typical_file_access_rate * 3.0 {
            // More than 3x the normal rate
            total_deviation += 30.0;
        } else if file_events > baseline.typical_file_access_rate * 2.0 {
            // More than 2x the normal rate
            total_deviation += 15.0;
        }
        
        // Network activity deviation
        let network_events = events.iter()
            .filter(|e| matches!(e.event_type,
                BehaviorEventType::NetworkConnection |
                BehaviorEventType::NetworkDataTransfer))
            .count() as f64;
                
        if network_events > baseline.typical_network_activity * 3.0 {
            // More than 3x the normal rate
            total_deviation += 40.0;
        } else if network_events > baseline.typical_network_activity * 2.0 {
            // More than 2x the normal rate
            total_deviation += 20.0;
        }
        
        // Check for access to unusual file paths
        for event in events.iter().filter(|e| e.event_type == BehaviorEventType::FileAccess) {
            if let Some(resource) = &event.resource {
                if !baseline.known_file_paths.contains(resource) {
                    total_deviation += 10.0;
                }
            }
        }
        
        // Check for connections to unusual destinations
        for event in events.iter().filter(|e| e.event_type == BehaviorEventType::NetworkConnection) {
            if let Some(destination) = event.details.get("destination") {
                if !baseline.known_network_connections.contains(destination) {
                    total_deviation += 15.0;
                }
            }
        }
        
        if total_deviation > 0.0 {
            Some(total_deviation)
        } else {
            None
        }
    }
    
    // Switch between learning and detection modes
    pub fn set_learning_mode(&mut self, enabled: bool) {
        self.learning_mode = enabled;
    }
}
```

## Conclusion

Behavioral analysis provides a powerful approach to threat detection by focusing on what entities do rather than what they are. This allows for detection of previously unknown threats based on their behavior patterns.

In this section, we've implemented:

1. A flexible event collection system
2. Process behavior analysis
3. Suspicious sequence detection
4. Baseline-based anomaly detection

These mechanisms help detect sophisticated threats that might evade signature-based detection systems.

## Exercises

1. **Event Collection**: Extend the `BehaviorEvent` structure to include additional telemetry data relevant to your security needs.

2. **New Detectors**: Implement a detector for "living off the land" attacks that use legitimate system tools for malicious purposes.

3. **Performance Optimization**: Optimize the `BehaviorCollector` to use more efficient data structures for high-volume event processing.

4. **Integration**: Connect the behavioral detection engine with the signature-based detection from the previous section to create a comprehensive threat detection system.

5. **Persistence**: Implement a mechanism to persist behavioral baselines between program executions.

## Navigation

- Previous: [Heuristic Analysis](./03-heuristic-analysis.md)
- Next: [Machine Learning Integration](./05-machine-learning.md)
