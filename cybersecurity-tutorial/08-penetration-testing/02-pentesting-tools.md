# Implementing Penetration Testing Tools in Rust

In this section, we'll implement basic penetration testing tools in Rust, including port scanners, service enumerators, and simple exploit scripts.

## Overview

We'll cover:

1. Building a TCP port scanner
2. Service enumeration
3. Simple exploit scripting
4. Reporting findings
5. Best practices for safe testing

## Building a TCP Port Scanner

```rust
use std::net::{TcpStream, SocketAddr, ToSocketAddrs};
use std::time::Duration;

pub fn scan_port(addr: &str, port: u16) -> bool {
    let socket = format!("{}:{}", addr, port);
    TcpStream::connect_timeout(&socket.to_socket_addrs().unwrap().next().unwrap(), Duration::from_secs(1)).is_ok()
}

pub fn scan_ports(addr: &str, ports: &[u16]) -> Vec<u16> {
    ports.iter().cloned().filter(|&p| scan_port(addr, p)).collect()
}
```

## Service Enumeration

Example: Banner grabbing

```rust
use std::io::{Read, Write};

pub fn grab_banner(addr: &str, port: u16) -> Option<String> {
    if let Ok(mut stream) = TcpStream::connect((addr, port)) {
        let _ = stream.write_all(b"\r\n");
        let mut buf = [0u8; 1024];
        if let Ok(n) = stream.read(&mut buf) {
            return Some(String::from_utf8_lossy(&buf[..n]).to_string());
        }
    }
    None
}
```

## Simple Exploit Scripting

Example: Attempting a default credential login (conceptual)

```rust
// Pseudocode: Attempt login with default credentials
fn try_default_login(addr: &str, port: u16, username: &str, password: &str) -> bool {
    // Connect to service and attempt login
    false // placeholder
}
```

## Reporting Findings

```rust
pub fn report_open_ports(addr: &str, open_ports: &[u16]) {
    println!("Open ports on {}: {:?}", addr, open_ports);
}
```

## Example Usage

```rust
fn main() {
    let target = "************";
    let ports = vec![22, 80, 443, 3306];
    let open_ports = scan_ports(target, &ports);
    report_open_ports(target, &open_ports);
    for port in open_ports {
        if let Some(banner) = grab_banner(target, port) {
            println!("Port {} banner: {}", port, banner);
        }
    }
}
```

## Best Practices for Safe Testing

- Always have authorization before scanning or exploiting
- Limit scans to the defined scope
- Avoid disruptive or destructive actions
- Log all activities for review

## Conclusion

We've implemented basic pentesting tools in Rust. In the next section, we'll cover advanced techniques and reporting.

---

🔗 **Previous**: [01-penetration-testing-concepts.md](./01-penetration-testing-concepts.md)

🔗 **Next**: [03-advanced-pentesting.md](./03-advanced-pentesting.md)
