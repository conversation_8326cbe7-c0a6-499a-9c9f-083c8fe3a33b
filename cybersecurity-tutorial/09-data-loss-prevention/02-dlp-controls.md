# Implementing DLP Controls in Rust

In this section, we'll implement basic Data Loss Prevention (DLP) controls in Rust, including data classification, content inspection, and policy enforcement.

## Overview

We'll cover:

1. Data classification and labeling
2. Content inspection (regex, file type, keywords)
3. Policy enforcement actions
4. Logging and alerting
5. Example DLP workflow

## Data Classification and Labeling

Example: Classify data based on regex patterns

```rust
pub fn classify_data(content: &str) -> Option<&'static str> {
    if content.contains("SSN:") {
        Some("PII")
    } else if content.contains("VISA") {
        Some("PCI")
    } else {
        None
    }
}
```

## Content Inspection

Example: Detect sensitive keywords or file types

```rust
pub fn contains_sensitive_keywords(content: &str) -> bool {
    let keywords = ["confidential", "secret", "proprietary"];
    keywords.iter().any(|k| content.to_lowercase().contains(k))
}

pub fn is_sensitive_filetype(filename: &str) -> bool {
    let sensitive_types = [".xls", ".doc", ".pdf"];
    sensitive_types.iter().any(|ext| filename.ends_with(ext))
}
```

## Policy Enforcement Actions

- Block, quarantine, or allow
- Alert user or admin

```rust
pub enum DlpAction {
    Allow,
    Block,
    Quarantine,
    Alert,
}

pub fn enforce_policy(classification: Option<&str>, sensitive: bool) -> DlpAction {
    match (classification, sensitive) {
        (Some("PII"), true) => DlpAction::Block,
        (Some("PCI"), true) => DlpAction::Quarantine,
        (_, true) => DlpAction::Alert,
        _ => DlpAction::Allow,
    }
}
```

## Logging and Alerting

```rust
use chrono::Utc;

pub fn log_dlp_event(event: &str, filename: &str) {
    println!("[{}] {}: {}", Utc::now(), filename, event);
}
```

## Example DLP Workflow

```rust
fn main() {
    let filename = "report.xls";
    let content = "SSN: ***********\nThis is confidential.";
    let classification = classify_data(content);
    let sensitive = contains_sensitive_keywords(content) || is_sensitive_filetype(filename);
    let action = enforce_policy(classification, sensitive);
    match action {
        DlpAction::Block => log_dlp_event("Blocked sensitive data", filename),
        DlpAction::Quarantine => log_dlp_event("Quarantined file", filename),
        DlpAction::Alert => log_dlp_event("Alert: sensitive data detected", filename),
        DlpAction::Allow => log_dlp_event("Allowed file", filename),
    }
}
```

## Conclusion

We've implemented basic DLP controls in Rust. In the next section, we'll explore advanced DLP techniques and integration.

---

🔗 **Previous**: [01-dlp-concepts.md](./01-dlp-concepts.md)

🔗 **Next**: [03-advanced-dlp.md](./03-advanced-dlp.md)
