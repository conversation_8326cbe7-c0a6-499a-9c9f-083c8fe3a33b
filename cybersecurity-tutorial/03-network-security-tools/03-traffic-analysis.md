# Network Traffic Analysis

Network traffic analysis is a core capability for detecting threats, understanding usage patterns, and optimizing network performance. In this section, we'll build tools in Rust to analyze captured network traffic, extract meaningful statistics, and identify anomalies.

## Overview

We'll cover:

1. Traffic flow concepts and metrics
2. Building a traffic analysis engine in Rust
3. Statistical analysis of network flows
4. Detecting anomalies and suspicious patterns
5. Visualizing network traffic data
6. Best practices for scalable analysis

## Traffic Flow Concepts and Metrics

A network flow is a sequence of packets sharing common properties (e.g., source/destination IP and port, protocol). Key metrics include:

- **Total bytes/packets per flow**
- **Duration of flows**
- **Average packet size**
- **Inter-arrival times**
- **Protocol distribution**
- **Top talkers (hosts with most traffic)**

Let's define a Rust structure for tracking flow statistics:

```rust
use std::net::IpAddr;
use std::time::{SystemTime, Duration};
use serde::{Serialize, Deserialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FlowKey {
    pub src_ip: IpAddr,
    pub dst_ip: IpAddr,
    pub src_port: u16,
    pub dst_port: u16,
    pub protocol: u8, // e.g., TCP=6, UDP=17
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FlowStats {
    pub key: FlowKey,
    pub first_seen: SystemTime,
    pub last_seen: SystemTime,
    pub packet_count: u64,
    pub byte_count: u64,
    pub min_packet_size: usize,
    pub max_packet_size: usize,
    pub total_interarrival: Duration,
    pub last_packet_time: Option<SystemTime>,
}

impl FlowStats {
    pub fn new(key: FlowKey, timestamp: SystemTime, packet_size: usize) -> Self {
        FlowStats {
            key,
            first_seen: timestamp,
            last_seen: timestamp,
            packet_count: 1,
            byte_count: packet_size as u64,
            min_packet_size: packet_size,
            max_packet_size: packet_size,
            total_interarrival: Duration::ZERO,
            last_packet_time: Some(timestamp),
        }
    }
    pub fn update(&mut self, timestamp: SystemTime, packet_size: usize) {
        self.last_seen = timestamp;
        self.packet_count += 1;
        self.byte_count += packet_size as u64;
        if packet_size < self.min_packet_size {
            self.min_packet_size = packet_size;
        }
        if packet_size > self.max_packet_size {
            self.max_packet_size = packet_size;
        }
        if let Some(last_time) = self.last_packet_time {
            let delta = timestamp.duration_since(last_time).unwrap_or(Duration::ZERO);
            self.total_interarrival += delta;
        }
        self.last_packet_time = Some(timestamp);
    }
    pub fn avg_packet_size(&self) -> f64 {
        self.byte_count as f64 / self.packet_count as f64
    }
    pub fn avg_interarrival(&self) -> f64 {
        if self.packet_count > 1 {
            self.total_interarrival.as_secs_f64() / (self.packet_count as f64 - 1.0)
        } else {
            0.0
        }
    }
}
```

## Building a Traffic Analysis Engine

We'll create a `TrafficAnalyzer` struct to process packets and maintain flow statistics:

```rust
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use crate::network::models::NetworkPacket;

pub struct TrafficAnalyzer {
    flows: Mutex<HashMap<FlowKey, FlowStats>>,
}

impl TrafficAnalyzer {
    pub fn new() -> Self {
        TrafficAnalyzer {
            flows: Mutex::new(HashMap::new()),
        }
    }
    pub fn process_packet(&self, packet: &NetworkPacket) {
        if let (Some(src_ip), Some(dst_ip), Some(src_port), Some(dst_port)) =
            (packet.src_ip, packet.dst_ip, packet.src_port, packet.dst_port)
        {
            let key = FlowKey {
                src_ip,
                dst_ip,
                src_port,
                dst_port,
                protocol: packet.protocol as u8,
            };
            let mut flows = self.flows.lock().unwrap();
            let now = SystemTime::now();
            let entry = flows.entry(key.clone()).or_insert_with(||
                FlowStats::new(key, now, packet.length)
            );
            entry.update(now, packet.length);
        }
    }
    pub fn get_top_talkers(&self, n: usize) -> Vec<FlowStats> {
        let flows = self.flows.lock().unwrap();
        let mut stats: Vec<_> = flows.values().cloned().collect();
        stats.sort_by(|a, b| b.byte_count.cmp(&a.byte_count));
        stats.into_iter().take(n).collect()
    }
    pub fn protocol_distribution(&self) -> HashMap<u8, u64> {
        let flows = self.flows.lock().unwrap();
        let mut dist = HashMap::new();
        for flow in flows.values() {
            *dist.entry(flow.key.protocol).or_insert(0) += flow.byte_count;
        }
        dist
    }
}
```

## Statistical Analysis and Anomaly Detection

We can use statistical methods to detect unusual traffic patterns. For example, flagging flows with unusually high byte counts or short inter-arrival times:

```rust
pub fn detect_anomalies(analyzer: &TrafficAnalyzer, byte_threshold: u64, rate_threshold: f64) -> Vec<FlowStats> {
    let flows = analyzer.flows.lock().unwrap();
    flows.values()
        .filter(|flow| flow.byte_count > byte_threshold || flow.avg_interarrival() < rate_threshold)
        .cloned()
        .collect()
}
```

## Visualizing Network Traffic Data

For visualization, you can export flow statistics to CSV or JSON for use with tools like Excel, Grafana, or Python's matplotlib. Example CSV export:

```rust
use std::fs::File;
use std::io::Write;

pub fn export_flows_to_csv(analyzer: &TrafficAnalyzer, path: &str) -> std::io::Result<()> {
    let flows = analyzer.flows.lock().unwrap();
    let mut file = File::create(path)?;
    writeln!(file, "src_ip,dst_ip,src_port,dst_port,protocol,bytes,packets,first_seen,last_seen")?;
    for flow in flows.values() {
        writeln!(file, "{},{},{},{},{},{},{},{:?},{:?}",
            flow.key.src_ip, flow.key.dst_ip, flow.key.src_port, flow.key.dst_port, flow.key.protocol,
            flow.byte_count, flow.packet_count, flow.first_seen, flow.last_seen)?;
    }
    Ok(())
}
```

## Example Usage

Here's how you might use the traffic analyzer in your main program:

```rust
fn main() {
    let analyzer = Arc::new(TrafficAnalyzer::new());
    // ... set up packet capture as in previous sections ...
    let analyzer_clone = analyzer.clone();
    capture.add_handler(move |packet| {
        analyzer_clone.process_packet(packet);
    });
    // ... run capture ...
    // After capture, analyze results:
    let top_talkers = analyzer.get_top_talkers(10);
    println!("Top 10 talkers:");
    for flow in top_talkers {
        println!("{:?}", flow);
    }
    let anomalies = detect_anomalies(&analyzer, 10_000_000, 0.001);
    println!("Anomalous flows:");
    for flow in anomalies {
        println!("{:?}", flow);
    }
    export_flows_to_csv(&analyzer, "flows.csv").unwrap();
}
```

## Best Practices for Scalable Analysis

- Use efficient data structures (e.g., hash maps, ring buffers)
- Periodically clean up expired flows to save memory
- Offload heavy analysis to background threads
- Use streaming or batch export for large datasets
- Integrate with visualization dashboards for real-time monitoring

## Quiz: Traffic Analysis
1. What is the difference between deep packet inspection and shallow packet inspection?
2. Name a tool commonly used for network traffic analysis.
3. How can encrypted traffic be analyzed for threats?

## Diagram: Packet Flow

```mermaid
graph LR
    A[Network Interface] --> B[Packet Capture]
    B --> C[Traffic Analysis Engine]
    C --> D[Alert/Log]
```

## Conclusion

In this section, we've built a traffic analysis engine in Rust that can:

1. Track and summarize network flows
2. Compute key traffic metrics
3. Detect anomalies using statistical methods
4. Export data for visualization and reporting

These capabilities are essential for network security monitoring and incident response. In the next section, we'll implement intrusion detection features to identify malicious activity in network traffic.

---

🔗 **Previous**: [02-packet-capture.md](./02-packet-capture.md)

🔗 **Next**: [04-intrusion-detection.md](./04-intrusion-detection.md)
