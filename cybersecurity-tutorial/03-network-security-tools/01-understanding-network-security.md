# Understanding Network Security

Network security is a complex field that focuses on protecting computer systems from unauthorized access, misuse, or denial of service attacks. In this section, we'll explore the fundamentals of network security and set up the foundation for building our network security tools in Rust.

## Overview

We'll cover:

1. Network models and layers
2. Common network threats and attack vectors
3. Defense-in-depth strategies
4. Setting up our Rust environment for network programming

## Network Models and Layers

### The OSI Model

The Open Systems Interconnection (OSI) model provides a conceptual framework for understanding network interactions through seven distinct layers:

```
┌───────────────────┐
│ 7. Application    │ HTTP, FTP, SMTP, DNS
├───────────────────┤
│ 6. Presentation   │ Encryption, Compression
├───────────────────┤
│ 5. Session        │ Session Management
├───────────────────┤
│ 4. Transport      │ TCP, UDP
├───────────────────┤
│ 3. Network        │ IP, ICMP, Routing
├───────────────────┤
│ 2. Data Link      │ Ethernet, MAC Addresses
├───────────────────┤
│ 1. Physical       │ Cables, Signals
└───────────────────┘
```

Let's implement a simple Rust structure to represent this model:

```rust
/// Represents the seven layers of the OSI model
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum OsiLayer {
    Physical = 1,
    DataLink = 2,
    Network = 3,
    Transport = 4,
    Session = 5,
    Presentation = 6,
    Application = 7,
}

impl OsiLayer {
    /// Returns the name of the layer
    pub fn name(&self) -> &'static str {
        match self {
            OsiLayer::Physical => "Physical",
            OsiLayer::DataLink => "Data Link",
            OsiLayer::Network => "Network",
            OsiLayer::Transport => "Transport",
            OsiLayer::Session => "Session",
            OsiLayer::Presentation => "Presentation",
            OsiLayer::Application => "Application",
        }
    }
    
    /// Returns common protocols for this layer
    pub fn common_protocols(&self) -> Vec<&'static str> {
        match self {
            OsiLayer::Physical => vec!["Ethernet", "USB", "Bluetooth", "IEEE 802.11"],
            OsiLayer::DataLink => vec!["MAC", "PPP", "IEEE 802.2", "Frame Relay"],
            OsiLayer::Network => vec!["IPv4", "IPv6", "ICMP", "IGMP", "IPsec"],
            OsiLayer::Transport => vec!["TCP", "UDP", "SCTP", "DCCP"],
            OsiLayer::Session => vec!["NetBIOS", "RPC", "SOCKS", "SDP"],
            OsiLayer::Presentation => vec!["TLS/SSL", "MIME", "XDR", "ASCII/Unicode"],
            OsiLayer::Application => vec!["HTTP", "FTP", "SMTP", "DNS", "SSH"],
        }
    }
}
```

### The TCP/IP Model

The TCP/IP model is a more practical, simplified version with four layers:

```
┌───────────────────┐
│ 4. Application    │ HTTP, FTP, SMTP, DNS
├───────────────────┤
│ 3. Transport      │ TCP, UDP
├───────────────────┤
│ 2. Internet       │ IP, ICMP, Routing
├───────────────────┤
│ 1. Link           │ Ethernet, MAC Addressing
└───────────────────┘
```

Let's implement this model as well:

```rust
/// Represents the four layers of the TCP/IP model
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TcpIpLayer {
    Link = 1,
    Internet = 2,
    Transport = 3,
    Application = 4,
}

impl TcpIpLayer {
    /// Returns the name of the layer
    pub fn name(&self) -> &'static str {
        match self {
            TcpIpLayer::Link => "Link",
            TcpIpLayer::Internet => "Internet",
            TcpIpLayer::Transport => "Transport",
            TcpIpLayer::Application => "Application",
        }
    }
    
    /// Maps a TCP/IP layer to corresponding OSI layers
    pub fn to_osi_layers(&self) -> Vec<OsiLayer> {
        match self {
            TcpIpLayer::Link => vec![OsiLayer::Physical, OsiLayer::DataLink],
            TcpIpLayer::Internet => vec![OsiLayer::Network],
            TcpIpLayer::Transport => vec![OsiLayer::Transport],
            TcpIpLayer::Application => vec![
                OsiLayer::Session,
                OsiLayer::Presentation,
                OsiLayer::Application,
            ],
        }
    }
    
    /// Returns common protocols for this layer
    pub fn common_protocols(&self) -> Vec<&'static str> {
        match self {
            TcpIpLayer::Link => vec!["ARP", "NDP", "OSPF", "Ethernet", "PPP"],
            TcpIpLayer::Internet => vec!["IPv4", "IPv6", "ICMP", "ECN", "IPsec"],
            TcpIpLayer::Transport => vec!["TCP", "UDP", "DCCP", "SCTP", "QUIC"],
            TcpIpLayer::Application => vec!["HTTP", "HTTPS", "FTP", "SMTP", "DNS", "TLS", "SSH"],
        }
    }
}
```

## Common Network Threats

Network threats can affect different layers of the network stack. Let's categorize common threats:

```rust
use std::collections::HashMap;

/// Represents different categories of network security threats
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ThreatCategory {
    Reconnaissance,
    AccessAttacks,
    DenialOfService,
    ManInTheMiddle,
    ApplicationLayer,
    MalwareBasedAttacks,
    SocialEngineering,
    PhysicalThreats,
}

/// Represents a specific network security threat
#[derive(Debug, Clone)]
pub struct NetworkThreat {
    pub name: String,
    pub category: ThreatCategory,
    pub description: String,
    pub affected_layers: Vec<OsiLayer>,
    pub common_mitigations: Vec<String>,
}

/// Helper function to create a catalog of common network threats
pub fn create_threat_catalog() -> HashMap<String, NetworkThreat> {
    let mut catalog = HashMap::new();
    
    // Reconnaissance threats
    catalog.insert(
        "Port Scanning".to_string(),
        NetworkThreat {
            name: "Port Scanning".to_string(),
            category: ThreatCategory::Reconnaissance,
            description: "Probing a host for open ports to discover available services".to_string(),
            affected_layers: vec![OsiLayer::Transport],
            common_mitigations: vec![
                "Firewall configuration".to_string(),
                "IDS/IPS deployment".to_string(),
                "Port knocking".to_string(),
            ],
        },
    );
    
    // Access attacks
    catalog.insert(
        "Brute Force".to_string(),
        NetworkThreat {
            name: "Brute Force".to_string(),
            category: ThreatCategory::AccessAttacks,
            description: "Systematically checking all possible passwords until the correct one is found".to_string(),
            affected_layers: vec![OsiLayer::Application],
            common_mitigations: vec![
                "Account lockout policies".to_string(),
                "Multi-factor authentication".to_string(),
                "Strong password policies".to_string(),
                "CAPTCHA implementation".to_string(),
            ],
        },
    );
    
    // Denial of Service
    catalog.insert(
        "SYN Flood".to_string(),
        NetworkThreat {
            name: "SYN Flood".to_string(),
            category: ThreatCategory::DenialOfService,
            description: "Sending many SYN packets without completing the TCP handshake".to_string(),
            affected_layers: vec![OsiLayer::Transport],
            common_mitigations: vec![
                "SYN cookies".to_string(),
                "Rate limiting".to_string(),
                "Connection timeouts".to_string(),
                "DDoS protection services".to_string(),
            ],
        },
    );
    
    // Man in the Middle
    catalog.insert(
        "ARP Poisoning".to_string(),
        NetworkThreat {
            name: "ARP Poisoning".to_string(),
            category: ThreatCategory::ManInTheMiddle,
            description: "Manipulating Address Resolution Protocol to intercept network traffic".to_string(),
            affected_layers: vec![OsiLayer::DataLink],
            common_mitigations: vec![
                "Static ARP entries".to_string(),
                "ARP spoofing detection".to_string(),
                "Network segmentation".to_string(),
                "Encryption".to_string(),
            ],
        },
    );
    
    // Application Layer
    catalog.insert(
        "SQL Injection".to_string(),
        NetworkThreat {
            name: "SQL Injection".to_string(),
            category: ThreatCategory::ApplicationLayer,
            description: "Inserting malicious SQL code into application database queries".to_string(),
            affected_layers: vec![OsiLayer::Application],
            common_mitigations: vec![
                "Prepared statements".to_string(),
                "Input validation".to_string(),
                "Least privilege database accounts".to_string(),
                "Web application firewall".to_string(),
            ],
        },
    );
    
    // Add more threats...
    
    catalog
}
```

## Defense-in-Depth Strategy

Network security relies on multiple layers of defense. Let's model a defense-in-depth approach:

```rust
/// Represents different defensive layers in a defense-in-depth strategy
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum DefenseLayer {
    Physical,       // Physical security controls
    Perimeter,      // Edge firewalls, DMZ
    Network,        // Network segmentation, VLANs
    Host,           // Host-based firewalls, IDS
    Application,    // Secure coding, input validation
    Data,           // Encryption, access controls
    User,           // Training, awareness, policies
}

/// Represents a specific security control
#[derive(Debug, Clone)]
pub struct SecurityControl {
    pub name: String,
    pub defense_layer: DefenseLayer,
    pub description: String,
    pub effectiveness: f32,      // 0.0 to 1.0
    pub implementation_cost: u32, // 1 to 10
    pub addresses_threats: Vec<ThreatCategory>,
}

/// Helper function to create a catalog of common security controls
pub fn create_security_controls() -> Vec<SecurityControl> {
    vec![
        SecurityControl {
            name: "Next-Generation Firewall".to_string(),
            defense_layer: DefenseLayer::Perimeter,
            description: "Advanced firewall with deep packet inspection and application awareness".to_string(),
            effectiveness: 0.85,
            implementation_cost: 8,
            addresses_threats: vec![
                ThreatCategory::AccessAttacks,
                ThreatCategory::ApplicationLayer,
                ThreatCategory::Reconnaissance,
            ],
        },
        SecurityControl {
            name: "Network Intrusion Detection System".to_string(),
            defense_layer: DefenseLayer::Network,
            description: "Monitors network traffic for suspicious activity".to_string(),
            effectiveness: 0.75,
            implementation_cost: 7,
            addresses_threats: vec![
                ThreatCategory::AccessAttacks,
                ThreatCategory::ManInTheMiddle,
                ThreatCategory::MalwareBasedAttacks,
            ],
        },
        SecurityControl {
            name: "Data Encryption".to_string(),
            defense_layer: DefenseLayer::Data,
            description: "Encrypts sensitive data at rest and in transit".to_string(),
            effectiveness: 0.90,
            implementation_cost: 6,
            addresses_threats: vec![
                ThreatCategory::ManInTheMiddle,
                ThreatCategory::PhysicalThreats,
            ],
        },
        // Add more security controls...
    ]
}
```

## Setting Up Our Rust Environment for Network Programming

Now let's set up the basic infrastructure for our network security tools. We'll start with a project structure and necessary dependencies.

### Project Structure

Our network security module will have the following structure:

```
src/
├── network/
│   ├── mod.rs              # Main module file
│   ├── models.rs           # Data structures and models
│   ├── packet_capture.rs   # Packet capture functionality
│   ├── analysis.rs         # Traffic analysis engine
│   ├── protocols/          # Protocol-specific parsers
│   │   ├── mod.rs
│   │   ├── ethernet.rs
│   │   ├── ip.rs
│   │   ├── tcp.rs
│   │   ├── udp.rs
│   │   └── http.rs
│   ├── firewall.rs         # Firewall implementation
│   └── ids.rs              # Intrusion detection
└── lib.rs
```

### Required Dependencies

Here's the Cargo.toml section for our network security module:

```rust
// Cargo.toml excerpt
// Actual implementation would be in the real Cargo.toml file

// Dependencies for network security functionality
[dependencies]
pcap = "1.0.0"                # Packet capture library
pnet = "0.33.0"               # Low-level networking
pnet_macros = "0.33.0"        # Macros for pnet
byteorder = "1.4.3"           # Byte manipulation
ipnetwork = "0.20.0"          # IP address manipulation
dns-parser = "0.8.0"          # DNS protocol parsing
tls-parser = "0.11.0"         # TLS protocol parsing
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.28.0", features = ["full"] } # Async runtime
futures = "0.3"               # Async utilities
libloading = "0.8.0"          # Dynamic library loading
crossbeam-channel = "0.5"     # Multi-producer multi-consumer channels
parking_lot = "0.12.1"        # More efficient synchronization primitives
```

### Basic Packet Structure

Let's create a simple representation of network packets:

```rust
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};
use std::time::{SystemTime, UNIX_EPOCH};
use serde::{Serialize, Deserialize};

/// Represents a network interface
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkInterface {
    pub name: String,
    pub description: Option<String>,
    pub mac_address: Option<[u8; 6]>,
    pub ip_addresses: Vec<IpAddr>,
    pub is_up: bool,
    pub is_loopback: bool,
}

/// Represents the direction of a packet
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PacketDirection {
    Inbound,
    Outbound,
    Unknown,
}

/// Protocol at the transport layer
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TransportProtocol {
    TCP,
    UDP,
    ICMP,
    IGMP,
    SCTP,
    Other(u8),
}

/// Represents a captured network packet
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkPacket {
    /// Unique identifier for the packet
    pub id: u64,
    
    /// Timestamp when the packet was captured
    pub timestamp: u64,
    
    /// The network interface where the packet was captured
    pub interface_name: String,
    
    /// Direction of the packet (inbound/outbound)
    pub direction: PacketDirection,
    
    /// Packet length in bytes
    pub length: usize,
    
    /// Source MAC address (if available)
    pub src_mac: Option<[u8; 6]>,
    
    /// Destination MAC address (if available)
    pub dst_mac: Option<[u8; 6]>,
    
    /// Source IP address (if available)
    pub src_ip: Option<IpAddr>,
    
    /// Destination IP address (if available)
    pub dst_ip: Option<IpAddr>,
    
    /// Transport protocol
    pub protocol: TransportProtocol,
    
    /// Source port (for TCP/UDP)
    pub src_port: Option<u16>,
    
    /// Destination port (for TCP/UDP)
    pub dst_port: Option<u16>,
    
    /// TCP flags (if applicable)
    pub tcp_flags: Option<u8>,
    
    /// Raw packet data
    pub data: Vec<u8>,
}

impl NetworkPacket {
    /// Creates a new packet with current timestamp and auto-generated ID
    pub fn new(interface_name: String, data: Vec<u8>) -> Self {
        // Generate a simple ID using timestamp and data length
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_micros() as u64;
            
        let id = timestamp ^ (data.len() as u64);
        
        NetworkPacket {
            id,
            timestamp,
            interface_name,
            direction: PacketDirection::Unknown,
            length: data.len(),
            src_mac: None,
            dst_mac: None,
            src_ip: None,
            dst_ip: None,
            protocol: TransportProtocol::Other(0),
            src_port: None,
            dst_port: None,
            tcp_flags: None,
            data,
        }
    }
    
    /// Returns true if this packet is TCP
    pub fn is_tcp(&self) -> bool {
        matches!(self.protocol, TransportProtocol::TCP)
    }
    
    /// Returns true if this packet is UDP
    pub fn is_udp(&self) -> bool {
        matches!(self.protocol, TransportProtocol::UDP)
    }
    
    /// Returns true if this packet is ICMP
    pub fn is_icmp(&self) -> bool {
        matches!(self.protocol, TransportProtocol::ICMP)
    }
    
    /// Returns a human-readable summary of the packet
    pub fn summary(&self) -> String {
        let src = match (self.src_ip, self.src_port) {
            (Some(ip), Some(port)) => format!("{}:{}", ip, port),
            (Some(ip), None) => format!("{}", ip),
            (None, _) => "unknown".to_string(),
        };
        
        let dst = match (self.dst_ip, self.dst_port) {
            (Some(ip), Some(port)) => format!("{}:{}", ip, port),
            (Some(ip), None) => format!("{}", ip),
            (None, _) => "unknown".to_string(),
        };
        
        let proto = match self.protocol {
            TransportProtocol::TCP => "TCP",
            TransportProtocol::UDP => "UDP",
            TransportProtocol::ICMP => "ICMP",
            TransportProtocol::IGMP => "IGMP",
            TransportProtocol::SCTP => "SCTP",
            TransportProtocol::Other(p) => return format!("Unknown({})", p),
        };
        
        format!("[{}] {} {} → {} ({} bytes)", self.id, proto, src, dst, self.length)
    }
}
```

## Putting It Together

Now let's create a simple program that demonstrates these network security concepts:

```rust
fn main() {
    println!("Network Security Tools - Understanding Network Security");
    
    // Display OSI model
    println!("\nOSI Model Layers:");
    for layer in 1..=7 {
        let osi_layer = unsafe { std::mem::transmute::<u8, OsiLayer>(layer) };
        println!("Layer {}: {} - Protocols: {}", 
            layer,
            osi_layer.name(),
            osi_layer.common_protocols().join(", ")
        );
    }
    
    // Display TCP/IP model mapping to OSI
    println!("\nTCP/IP Model Mapping to OSI:");
    for layer in 1..=4 {
        let tcp_ip_layer = unsafe { std::mem::transmute::<u8, TcpIpLayer>(layer) };
        let osi_layers: Vec<String> = tcp_ip_layer.to_osi_layers()
            .iter()
            .map(|l| format!("{} ({})", l.name(), *l as u8))
            .collect();
            
        println!("TCP/IP Layer {}: {} maps to OSI layers: {}", 
            layer,
            tcp_ip_layer.name(),
            osi_layers.join(", ")
        );
    }
    
    // Load threat catalog
    let threat_catalog = create_threat_catalog();
    println!("\nCommon Network Threats:");
    for (name, threat) in threat_catalog.iter().take(5) {
        println!("- {} (affects OSI layers: {})", 
            name,
            threat.affected_layers.iter()
                .map(|l| l.name())
                .collect::<Vec<_>>()
                .join(", ")
        );
    }
    
    // Defense-in-depth strategy
    let security_controls = create_security_controls();
    println!("\nSecurity Controls in Defense-in-Depth Strategy:");
    for control in security_controls {
        println!("- {} (Layer: {:?}, Effectiveness: {:.1}%, Cost: {})",
            control.name,
            control.defense_layer,
            control.effectiveness * 100.0,
            control.implementation_cost
        );
    }
    
    // Creating a sample network packet
    let mut packet = NetworkPacket::new("eth0".to_string(), vec![0; 64]);
    packet.src_ip = Some(IpAddr::V4(Ipv4Addr::new(192, 168, 1, 100)));
    packet.dst_ip = Some(IpAddr::V4(Ipv4Addr::new(93, 184, 216, 34)));
    packet.protocol = TransportProtocol::TCP;
    packet.src_port = Some(54321);
    packet.dst_port = Some(443);
    packet.direction = PacketDirection::Outbound;
    
    println!("\nSample Network Packet:");
    println!("{}", packet.summary());
}
```

## Conclusion

In this introductory section, we've established a foundation for understanding network security and set up the basic data structures we'll need for our network security tools. We've learned about:

1. Network models (OSI and TCP/IP) and their layers
2. Common network threats and attack vectors
3. Defense-in-depth strategy with multiple security controls
4. Rust data structures for modeling network concepts
5. The basic structure for our network security tools

In the next section, we'll implement packet capture functionality, which is the foundation for all network security monitoring tools.

---

🔗 **Previous**: [00-overview.md](./00-overview.md)

🔗 **Next**: [02-packet-capture.md](./02-packet-capture.md)
