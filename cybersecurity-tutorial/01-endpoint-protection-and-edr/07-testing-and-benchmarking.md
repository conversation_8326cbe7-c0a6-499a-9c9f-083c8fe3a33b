# Testing and Benchmarking

In this section, we'll learn how to properly test and benchmark our endpoint protection system to ensure it's both effective and efficient.

## Table of Contents
- [Introduction to Testing Security Software](#introduction-to-testing-security-software)
- [Unit Testing Our Plugins](#unit-testing-our-plugins)
- [Integration Testing the CyberShield System](#integration-testing-the-cybershield-system)
- [Performance Benchmarking](#performance-benchmarking)
- [Security Testing](#security-testing)
- [Continuous Integration Setup](#continuous-integration-setup)
- [Next Steps](#next-steps)

## Introduction to Testing Security Software

Testing security software presents unique challenges:

1. **Correctness**: Security software must function without false negatives (missed threats)
2. **Performance**: Endpoint protection should have minimal system impact
3. **Reliability**: The software must work consistently under all conditions
4. **Coverage**: Tests must cover a wide range of scenarios and edge cases
5. **Security**: The testing process itself must be secure

For our CyberShield endpoint protection system, we'll implement a comprehensive testing strategy that addresses all these concerns.

## Unit Testing Our Plugins

Let's start with unit tests for our plugins. We'll use the `cargo test` framework and the common testing libraries in the Rust ecosystem.

First, let's create a basic test structure for the file system monitoring plugin:

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;
    use std::fs;
    use std::thread;
    use std::time::Duration;
    use tempfile::tempdir;
    use crossbeam_channel::{unbounded, Receiver};

    #[test]
    fn test_file_system_monitoring_creation() {
        let (sender, _) = unbounded();
        let plugin = FileSystemMonitorPlugin::new(sender);
        
        assert_eq!(plugin.name(), "File System Monitor");
        assert!(plugin.is_enabled());
    }
    
    #[test]
    fn test_enable_disable() {
        let (sender, _) = unbounded();
        let plugin = FileSystemMonitorPlugin::new(sender);
        
        assert!(plugin.is_enabled());
        plugin.disable().unwrap();
        assert!(!plugin.is_enabled());
        plugin.enable().unwrap();
        assert!(plugin.is_enabled());
    }
    
    #[test]
    fn test_path_management() {
        let (sender, _) = unbounded();
        let mut plugin = FileSystemMonitorPlugin::new(sender);
        
        // Test adding paths
        let path1 = PathBuf::from("/tmp/test1");
        let path2 = PathBuf::from("/tmp/test2");
        
        plugin.add_watch_path(path1.clone()).unwrap();
        plugin.add_watch_path(path2.clone()).unwrap();
        
        assert!(plugin.is_watching(&path1));
        assert!(plugin.is_watching(&path2));
        
        // Test removing paths
        plugin.remove_watch_path(&path1).unwrap();
        assert!(!plugin.is_watching(&path1));
        assert!(plugin.is_watching(&path2));
    }
    
    #[test]
    fn test_file_change_detection() {
        // Create a temporary directory for testing
        let temp_dir = tempdir().unwrap();
        let test_file_path = temp_dir.path().join("test_file.txt");
        
        // Create channels for the plugin to communicate through
        let (sender, receiver): (Sender<PluginEvent>, Receiver<PluginEvent>) = unbounded();
        
        // Create and start the plugin
        let mut plugin = FileSystemMonitorPlugin::new(sender);
        plugin.add_watch_path(temp_dir.path().to_path_buf()).unwrap();
        plugin.start().unwrap();
        
        // Wait a moment for the watcher to initialize
        thread::sleep(Duration::from_millis(100));
        
        // Create a new file
        fs::write(&test_file_path, "test content").unwrap();
        
        // Wait for the event to be processed
        thread::sleep(Duration::from_millis(500));
        
        // Check if we received a file creation event
        let event = receiver.try_recv();
        assert!(event.is_ok());
        
        if let Ok(PluginEvent::FileSystem { 
            event_type, 
            path, 
            ..
        }) = event {
            assert_eq!(event_type, "create");
            assert_eq!(path, test_file_path);
        } else {
            panic!("Expected FileSystem event");
        }
        
        // Modify the file
        fs::write(&test_file_path, "modified content").unwrap();
        
        // Wait for the event to be processed
        thread::sleep(Duration::from_millis(500));
        
        // Check if we received a file modification event
        let event = receiver.try_recv();
        assert!(event.is_ok());
        
        if let Ok(PluginEvent::FileSystem { 
            event_type, 
            path, 
            ..
        }) = event {
            assert_eq!(event_type, "modify");
            assert_eq!(path, test_file_path);
        } else {
            panic!("Expected FileSystem event");
        }
        
        // Clean up
        plugin.stop().unwrap();
    }
}
```

Now, let's create similar tests for the process monitoring plugin:

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;
    use std::time::Duration;
    use crossbeam_channel::{unbounded, Receiver};

    #[test]
    fn test_process_monitoring_creation() {
        let (sender, _) = unbounded();
        let plugin = ProcessMonitorPlugin::new(sender);
        
        assert_eq!(plugin.name(), "Process Monitor");
        assert!(plugin.is_enabled());
    }
    
    #[test]
    fn test_process_snapshot() {
        let (sender, _) = unbounded();
        let mut plugin = ProcessMonitorPlugin::new(sender);
        
        // Take initial snapshot
        plugin.take_process_snapshot().unwrap();
        
        // There should be at least a few processes running
        assert!(plugin.process_map.read().unwrap().len() > 0);
    }
    
    #[test]
    fn test_process_detection() {
        // Create channels for the plugin to communicate through
        let (sender, receiver): (Sender<PluginEvent>, Receiver<PluginEvent>) = unbounded();
        
        // Create and start the plugin with a fast refresh rate for testing
        let mut plugin = ProcessMonitorPlugin::new(sender);
        plugin.set_refresh_interval(1); // 1 second refresh rate
        plugin.start().unwrap();
        
        // Start a test process that will be detected
        let child = std::process::Command::new("sleep")
            .arg("5") // Sleep for 5 seconds
            .spawn()
            .expect("Failed to start test process");
        
        let pid = child.id();
        
        // Wait for the plugin to detect the process (2 cycles)
        thread::sleep(Duration::from_secs(3));
        
        // Check for process created event
        let mut found_process = false;
        while let Ok(event) = receiver.try_recv() {
            if let PluginEvent::Process { 
                event_type, 
                process_id, 
                .. 
            } = event {
                if event_type == "created" && process_id as u32 == pid {
                    found_process = true;
                    break;
                }
            }
        }
        
        assert!(found_process, "Process creation event not detected");
        
        // Wait for the process to exit
        thread::sleep(Duration::from_secs(3));
        
        // Check for process exit event
        let mut found_exit = false;
        while let Ok(event) = receiver.try_recv() {
            if let PluginEvent::Process { 
                event_type, 
                process_id, 
                .. 
            } = event {
                if event_type == "terminated" && process_id as u32 == pid {
                    found_exit = true;
                    break;
                }
            }
        }
        
        assert!(found_exit, "Process termination event not detected");
        
        // Clean up
        plugin.stop().unwrap();
    }
}
```

And finally, let's test our threat detection component:

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;
    use crossbeam_channel::unbounded;

    #[test]
    fn test_threat_detection_creation() {
        let (sender, _) = unbounded();
        let plugin = ThreatDetectionPlugin::new(sender);
        
        assert_eq!(plugin.name(), "Threat Detection");
        assert!(plugin.is_enabled());
    }
    
    #[test]
    fn test_signature_management() {
        let (sender, _) = unbounded();
        let mut plugin = ThreatDetectionPlugin::new(sender);
        
        // Add a test signature
        let test_signature = FileSignature {
            id: "TEST001".to_string(),
            name: "Test Malware".to_string(),
            pattern: "malicious_test_pattern".to_string(),
            severity: SeverityLevel::High,
        };
        
        plugin.add_file_signature(test_signature.clone()).unwrap();
        
        // Verify the signature was added
        let signatures = plugin.get_file_signatures();
        assert!(signatures.contains(&test_signature));
        
        // Remove the signature
        plugin.remove_file_signature(&test_signature.id).unwrap();
        let signatures = plugin.get_file_signatures();
        assert!(!signatures.contains(&test_signature));
    }
    
    #[test]
    fn test_file_threat_detection() {
        let (sender, receiver) = unbounded();
        let mut plugin = ThreatDetectionPlugin::new(sender);
        
        // Add a test signature
        let test_signature = FileSignature {
            id: "TEST001".to_string(),
            name: "Test Malware".to_string(),
            pattern: "malicious_test_pattern".to_string(),
            severity: SeverityLevel::High,
        };
        
        plugin.add_file_signature(test_signature).unwrap();
        
        // Create a file event with matching content
        let file_content = b"This file contains malicious_test_pattern that should be detected";
        let file_path = PathBuf::from("/test/path.txt");
        
        // Simulate a file system event
        let fs_event = PluginEvent::FileSystem {
            event_type: "create".to_string(),
            path: file_path.clone(),
            timestamp: chrono::Utc::now(),
        };
        
        // Process the event
        plugin.process_file_event(&fs_event, file_content).unwrap();
        
        // Check if we got a threat detection event
        let event = receiver.try_recv().unwrap();
        
        if let PluginEvent::Threat {
            threat_type,
            path,
            signature_id,
            severity,
            ..
        } = event {
            assert_eq!(threat_type, "file");
            assert_eq!(path, Some(file_path));
            assert_eq!(signature_id, "TEST001");
            assert_eq!(severity, "high");
        } else {
            panic!("Expected Threat event");
        }
    }
    
    #[test]
    fn test_process_threat_detection() {
        let (sender, receiver) = unbounded();
        let mut plugin = ThreatDetectionPlugin::new(sender);
        
        // Add a test process signature
        let test_signature = ProcessSignature {
            id: "PROC001".to_string(),
            name: "Suspicious Process".to_string(),
            process_name: "malware.exe".to_string(),
            command_line_pattern: Some("--steal-data".to_string()),
            severity: SeverityLevel::Critical,
        };
        
        plugin.add_process_signature(test_signature).unwrap();
        
        // Create a process event with matching details
        let process_event = PluginEvent::Process {
            event_type: "created".to_string(),
            process_id: 12345,
            process_name: "malware.exe".to_string(),
            command_line: Some("malware.exe --steal-data".to_string()),
            parent_process_id: Some(1),
            parent_process_name: Some("explorer.exe".to_string()),
            timestamp: chrono::Utc::now(),
        };
        
        // Process the event
        plugin.process_process_event(&process_event).unwrap();
        
        // Check if we got a threat detection event
        let event = receiver.try_recv().unwrap();
        
        if let PluginEvent::Threat {
            threat_type,
            process_id,
            signature_id,
            severity,
            ..
        } = event {
            assert_eq!(threat_type, "process");
            assert_eq!(process_id, Some(12345));
            assert_eq!(signature_id, "PROC001");
            assert_eq!(severity, "critical");
        } else {
            panic!("Expected Threat event");
        }
    }
}
```

## Integration Testing the CyberShield System

Integration tests ensure that our plugins work together correctly as a system. Let's create an integration test for our CyberShield application:

```rust
// tests/integration_tests.rs

use cybershield::{CyberShield, PluginEvent, Plugin};
use crossbeam_channel::{unbounded, Receiver};
use std::path::PathBuf;
use std::fs;
use std::thread;
use std::time::Duration;
use tempfile::tempdir;

#[test]
fn test_end_to_end_file_monitoring() {
    // Create a temporary directory for testing
    let temp_dir = tempdir().unwrap();
    let test_file_path = temp_dir.path().join("suspicious_file.txt");
    
    // Create a channel to receive events
    let (sender, receiver) = unbounded();
    
    // Initialize CyberShield
    let mut cybershield = CyberShield::new(sender);
    
    // Configure the plugins
    let file_monitor = cybershield.get_file_monitor_mut();
    file_monitor.add_watch_path(temp_dir.path().to_path_buf()).unwrap();
    
    let threat_detection = cybershield.get_threat_detector_mut();
    threat_detection.add_file_signature(cybershield::FileSignature {
        id: "TEST001".to_string(),
        name: "Test Malware".to_string(),
        pattern: "malicious_content".to_string(),
        severity: cybershield::SeverityLevel::High,
    }).unwrap();
    
    // Start the cybershield
    cybershield.start().unwrap();
    
    // Give it a moment to initialize
    thread::sleep(Duration::from_millis(500));
    
    // Create a file with malicious content
    fs::write(&test_file_path, "This file contains malicious_content that should trigger detection").unwrap();
    
    // Wait for detection to occur
    thread::sleep(Duration::from_secs(2));
    
    // Check for file events and threat events
    let mut found_file_event = false;
    let mut found_threat_event = false;
    
    while let Ok(event) = receiver.try_recv() {
        match event {
            PluginEvent::FileSystem { path, .. } => {
                if path == test_file_path {
                    found_file_event = true;
                }
            },
            PluginEvent::Threat { 
                threat_type,
                signature_id,
                path: Some(path),
                ..
            } => {
                if threat_type == "file" && 
                   signature_id == "TEST001" && 
                   path == test_file_path {
                    found_threat_event = true;
                }
            },
            _ => {}
        }
    }
    
    assert!(found_file_event, "File system event not detected");
    assert!(found_threat_event, "Threat not detected");
    
    // Clean up
    cybershield.stop().unwrap();
}

#[test]
fn test_end_to_end_process_monitoring() {
    // Create a channel to receive events
    let (sender, receiver) = unbounded();
    
    // Initialize CyberShield
    let mut cybershield = CyberShield::new(sender);
    
    // Configure the threat detection plugin
    let threat_detection = cybershield.get_threat_detector_mut();
    threat_detection.add_process_signature(cybershield::ProcessSignature {
        id: "PROC001".to_string(),
        name: "Test Suspicious Process".to_string(),
        process_name: "sleep".to_string(),
        command_line_pattern: Some("sleep 10".to_string()),
        severity: cybershield::SeverityLevel::Medium,
    }).unwrap();
    
    // Start CyberShield
    cybershield.start().unwrap();
    
    // Give it a moment to initialize
    thread::sleep(Duration::from_millis(500));
    
    // Start a process that matches our signature
    let child = std::process::Command::new("sleep")
        .arg("10")
        .spawn()
        .expect("Failed to start test process");
        
    let pid = child.id();
    
    // Wait for detection
    thread::sleep(Duration::from_secs(3));
    
    // Check for process events and threat events
    let mut found_process_event = false;
    let mut found_threat_event = false;
    
    while let Ok(event) = receiver.try_recv() {
        match event {
            PluginEvent::Process { 
                process_id,
                process_name,
                ..
            } => {
                if process_id as u32 == pid && process_name == "sleep" {
                    found_process_event = true;
                }
            },
            PluginEvent::Threat { 
                threat_type,
                signature_id,
                process_id: Some(process_id),
                ..
            } => {
                if threat_type == "process" && 
                   signature_id == "PROC001" && 
                   process_id as u32 == pid {
                    found_threat_event = true;
                }
            },
            _ => {}
        }
    }
    
    assert!(found_process_event, "Process event not detected");
    assert!(found_threat_event, "Process threat not detected");
    
    // Clean up
    cybershield.stop().unwrap();
    let _ = child.kill();
}
```

## Performance Benchmarking

Performance is critical for security software. Let's create benchmarks to measure the impact of our endpoint protection system:

```rust
// benches/benchmark.rs
use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use cybershield::{FileSystemMonitorPlugin, ProcessMonitorPlugin, ThreatDetectionPlugin, Plugin, PluginEvent};
use crossbeam_channel::unbounded;
use std::path::PathBuf;
use std::time::Duration;

// Benchmark file scanning performance
fn benchmark_file_scan(c: &mut Criterion) {
    let (sender, _) = unbounded();
    let mut threat_plugin = ThreatDetectionPlugin::new(sender.clone());
    
    // Add various signatures to simulate real-world usage
    for i in 0..100 {
        threat_plugin.add_file_signature(cybershield::FileSignature {
            id: format!("SIG{:03}", i),
            name: format!("Test Signature {}", i),
            pattern: format!("malicious_pattern_{}", i),
            severity: cybershield::SeverityLevel::Medium,
        }).unwrap();
    }
    
    // Create test files of different sizes
    let file_sizes = [10, 100, 1000, 10000, 100000];
    let mut group = c.benchmark_group("File Scanning");
    
    for size in &file_sizes {
        // Create a file of the specified size
        let mut content = Vec::with_capacity(*size);
        for i in 0..*size {
            content.extend_from_slice(format!("Line {} of test content\n", i).as_bytes());
        }
        
        // Insert a "malicious" pattern near the end to represent worst-case
        if *size > 50 {
            let pattern = b"malicious_pattern_42";
            let insert_pos = content.len() - pattern.len() - 10;
            content[insert_pos..insert_pos + pattern.len()].copy_from_slice(pattern);
        }
        
        group.bench_with_input(BenchmarkId::from_parameter(size), size, |b, _| {
            b.iter(|| {
                let file_event = PluginEvent::FileSystem {
                    event_type: "create".to_string(),
                    path: PathBuf::from(format!("/test/file_{}.txt", size)),
                    timestamp: chrono::Utc::now(),
                };
                
                threat_plugin.process_file_event(&file_event, &content).unwrap();
            })
        });
    }
    
    group.finish();
}

// Benchmark process event handling performance
fn benchmark_process_events(c: &mut Criterion) {
    let (sender, _) = unbounded();
    let mut plugin = ProcessMonitorPlugin::new(sender);
    plugin.start().unwrap();
    
    // Benchmark how quickly we can process system information
    c.bench_function("process_snapshot", |b| {
        b.iter(|| {
            plugin.take_process_snapshot().unwrap();
        })
    });
    
    plugin.stop().unwrap();
}

// Benchmark file system watcher startup time
fn benchmark_fs_watcher_startup(c: &mut Criterion) {
    c.bench_function("fs_watcher_startup", |b| {
        b.iter(|| {
            let (sender, _) = unbounded();
            let mut plugin = FileSystemMonitorPlugin::new(sender);
            plugin.add_watch_path(PathBuf::from("/tmp")).unwrap();
            plugin.start().unwrap();
            plugin.stop().unwrap();
        })
    });
}

// Benchmark the memory usage of our full system
fn benchmark_memory_usage(c: &mut Criterion) {
    let mut group = c.benchmark_group("Memory Usage");
    group.measurement_time(Duration::from_secs(20));
    
    group.bench_function("full_system", |b| {
        b.iter(|| {
            let (sender, _) = unbounded();
            let mut cybershield = cybershield::CyberShield::new(sender);
            
            // Configure with many watch paths and signatures
            let file_monitor = cybershield.get_file_monitor_mut();
            file_monitor.add_watch_path(PathBuf::from("/tmp")).unwrap();
            file_monitor.add_watch_path(PathBuf::from("/var/log")).unwrap();
            
            let threat_detector = cybershield.get_threat_detector_mut();
            for i in 0..500 {
                threat_detector.add_file_signature(cybershield::FileSignature {
                    id: format!("SIG{:03}", i),
                    name: format!("Test Signature {}", i),
                    pattern: format!("malicious_pattern_{}", i),
                    severity: cybershield::SeverityLevel::Medium,
                }).unwrap();
                
                if i % 5 == 0 {
                    threat_detector.add_process_signature(cybershield::ProcessSignature {
                        id: format!("PROC{:03}", i),
                        name: format!("Process Signature {}", i),
                        process_name: format!("test_proc_{}", i),
                        command_line_pattern: Some(format!("--malicious-flag={}", i)),
                        severity: cybershield::SeverityLevel::Medium,
                    }).unwrap();
                }
            }
            
            cybershield.start().unwrap();
            std::thread::sleep(std::time::Duration::from_secs(5));
            cybershield.stop().unwrap();
        })
    });
    
    group.finish();
}

criterion_group!(
    benches, 
    benchmark_file_scan, 
    benchmark_process_events,
    benchmark_fs_watcher_startup,
    benchmark_memory_usage
);
criterion_main!(benches);
```

To run these benchmarks:

```bash
cargo bench
```

The results will help us understand:
- How quickly we can scan files of different sizes
- The performance impact of our process monitoring
- The startup time of our file system watcher
- The memory usage of our complete system

## Security Testing

Security software itself must be secure. Let's set up some basic security testing:

```rust
#[cfg(test)]
mod security_tests {
    use super::*;
    use std::path::PathBuf;
    
    // Test handling of malformed inputs (fuzzing-like test)
    #[test]
    fn test_malformed_file_paths() {
        let (sender, _) = unbounded();
        let mut plugin = FileSystemMonitorPlugin::new(sender);
        
        // Test with very long paths
        let long_path = PathBuf::from("/tmp/".to_string() + &"a".repeat(10000));
        let result = plugin.add_watch_path(long_path);
        assert!(result.is_err());
        
        // Test with invalid UTF-8 paths
        let path_str = std::ffi::OsString::from_vec(vec![0xFF, 0xFE, 0xFD]);
        let invalid_path = PathBuf::from(path_str);
        let result = plugin.add_watch_path(invalid_path);
        assert!(result.is_err());
    }
    
    // Test resource exhaustion resistance (DoS protection)
    #[test]
    fn test_resource_limit_enforcement() {
        let (sender, _) = unbounded();
        let mut plugin = FileSystemMonitorPlugin::new(sender);
        
        // Try to add more paths than the limit
        let limit = 1000; // This should match the internal limit
        for i in 0..limit + 10 {
            let path = PathBuf::from(format!("/tmp/test_{}", i));
            let _ = plugin.add_watch_path(path);
        }
        
        // Check that we didn't exceed the limit
        assert!(plugin.get_watch_paths().len() <= limit);
    }
    
    // Test privilege separation
    #[test]
    fn test_no_privilege_escalation() {
        let (sender, _) = unbounded();
        let mut plugin = FileSystemMonitorPlugin::new(sender);
        
        // Attempt to monitor a privileged directory
        let result = plugin.add_watch_path(PathBuf::from("/root/.ssh"));
        
        // This should fail unless the test is run as root
        if !nix::unistd::Uid::effective().is_root() {
            assert!(result.is_err());
        }
    }
}
```

## Best Practices
- Always test plugins in a sandboxed environment
- Use automated test suites
- Monitor for performance regressions

## Common Pitfalls
- Not handling plugin errors gracefully
- Failing to update plugins for new threats

## Continuous Integration Setup

To ensure ongoing quality, we'll set up continuous integration for our project. Here's a sample GitHub Actions workflow file for our project:

```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  CARGO_TERM_COLOR: always

jobs:
  test:
    name: Test
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macOS-latest]
        rust: [stable]

    steps:
    - uses: actions/checkout@v2
    
    - name: Install Rust
      uses: actions-rs/toolchain@v1
      with:
        profile: minimal
        toolchain: ${{ matrix.rust }}
        override: true
        components: rustfmt, clippy
    
    - name: Install dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y libdbus-1-dev
    
    - name: Build
      uses: actions-rs/cargo@v1
      with:
        command: build
        args: --verbose
    
    - name: Run tests
      uses: actions-rs/cargo@v1
      with:
        command: test
        args: --verbose
    
    - name: Clippy
      uses: actions-rs/cargo@v1
      with:
        command: clippy
        args: -- -D warnings
    
    - name: Format check
      uses: actions-rs/cargo@v1
      with:
        command: fmt
        args: --all -- --check

  security_audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
      
      - name: Install cargo-audit
        run: cargo install cargo-audit
      
      - name: Run security audit
        run: cargo audit
```

## Next Steps

We've now completed the full implementation and testing of our endpoint protection module. In the next module, we'll explore threat detection and prevention in more detail, building upon the foundation we've established here.

Let's move on to implementing real-time threat intelligence integration, behavioral analysis, and advanced threat detection algorithms.

[Previous: System Health Reporting](06-system-health-reporting.md) | [Next Module: Threat Detection and Prevention](../02-threat-detection-and-prevention/00-overview.md)
