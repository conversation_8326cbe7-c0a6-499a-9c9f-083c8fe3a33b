# Signature-Based Detection

In this section, we'll implement a signature-based detection system for our CyberShield cybersecurity tool. Signature-based detection is one of the oldest and most reliable methods for identifying known threats by comparing files and data against a database of known malicious patterns.

## Table of Contents
- [Understanding Signatures in Cybersecurity](#understanding-signatures-in-cybersecurity)
- [Types of Signatures](#types-of-signatures)
- [Designing a Signature Database](#designing-a-signature-database)
- [Implementation: Signature Scanner](#implementation-signature-scanner)
- [File Hash Verification](#file-hash-verification)
- [Byte Pattern Matching](#byte-pattern-matching)
- [Regular Expression Patterns](#regular-expression-patterns)
- [Signature Updates](#signature-updates)
- [Performance Considerations](#performance-considerations)
- [Integration with CyberShield](#integration-with-cybershield)
- [Exercises](#exercises)
- [Next Steps](#next-steps)

## Understanding Signatures in Cybersecurity

A signature in cybersecurity is a unique identifier or pattern that represents a known threat. Unlike other detection methods, signature-based detection offers high precision for detecting known threats with minimal false positives. The approach is analogous to how antivirus software traditionally works.

## Types of Signatures

Let's explore the main types of signatures we'll implement:

### 1. Cryptographic Hashes

Hashes are fixed-size outputs generated from variable-length inputs. For files, common hash algorithms include:
- MD5 (legacy but still used)
- SHA-1 (also legacy)
- SHA-256 (current standard)
- BLAKE3 (newer, faster algorithm)

### 2. Byte Patterns

Specific sequences of bytes that identify malware, typically represented as hexadecimal patterns:
- Example: `4D 5A 90 00 03 00 00 00` (DOS MZ header)
- Can include wildcards for varying bytes: `4D 5A ?? ?? 03 00`

### 3. Regular Expression Patterns

For text-based threats like malicious scripts:
- Example: `eval\(base64_decode\(.*\)\)` might detect obfuscated PHP malware

## Designing a Signature Database

Our signature database will need to be:
- Fast to query
- Updatable
- Space-efficient

Let's design a simple signature database structure in Rust:

```rust
use std::collections::{HashMap, HashSet};
use sha2::{Sha256, Digest};
use regex::Regex;

/// Represents the type of signature
pub enum SignatureType {
    FileHash,
    BytePattern,
    RegexPattern,
}

/// Represents a malware signature
pub struct Signature {
    id: String,
    signature_type: SignatureType,
    pattern: String,
    threat_name: String,
    severity: ThreatSeverity,
    description: String,
}

/// Severity levels for detected threats
pub enum ThreatSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Main signature database structure
pub struct SignatureDatabase {
    file_hashes: HashSet<String>,
    byte_patterns: Vec<(Vec<u8>, Option<Vec<usize>>, String)>,  // (pattern, wildcard positions, signature_id)
    regex_patterns: Vec<(Regex, String)>,  // (compiled regex, signature_id)
    signature_details: HashMap<String, Signature>,
    last_updated: std::time::SystemTime,
}

impl SignatureDatabase {
    pub fn new() -> Self {
        SignatureDatabase {
            file_hashes: HashSet::new(),
            byte_patterns: Vec::new(),
            regex_patterns: Vec::new(),
            signature_details: HashMap::new(),
            last_updated: std::time::SystemTime::now(),
        }
    }
    
    pub fn add_file_hash(&mut self, hash: &str, signature: Signature) {
        self.file_hashes.insert(hash.to_string());
        self.signature_details.insert(signature.id.clone(), signature);
    }
    
    pub fn add_byte_pattern(&mut self, pattern: Vec<u8>, wildcards: Option<Vec<usize>>, signature: Signature) {
        self.byte_patterns.push((pattern, wildcards, signature.id.clone()));
        self.signature_details.insert(signature.id.clone(), signature);
    }
    
    pub fn add_regex_pattern(&mut self, pattern: &str, signature: Signature) -> Result<(), regex::Error> {
        let compiled = Regex::new(pattern)?;
        self.regex_patterns.push((compiled, signature.id.clone()));
        self.signature_details.insert(signature.id.clone(), signature);
        Ok(())
    }
    
    // Method to check if a file hash exists in the database
    pub fn contains_file_hash(&self, hash: &str) -> bool {
        self.file_hashes.contains(hash)
    }
    
    // Other methods will be implemented below
}
```

## Implementation: Signature Scanner

Now, let's implement the scanner that will use our signature database:

```rust
/// Represents a detected threat
pub struct ThreatDetection {
    pub signature_id: String,
    pub threat_name: String,
    pub severity: ThreatSeverity,
    pub description: String,
    pub detection_type: SignatureType,
    pub detection_location: String,
    pub timestamp: std::time::SystemTime,
}

/// Main signature scanner that performs detection
pub struct SignatureScanner {
    database: SignatureDatabase,
}

impl SignatureScanner {
    pub fn new(database: SignatureDatabase) -> Self {
        SignatureScanner { database }
    }
    
    /// Scan a file for known threats
    pub fn scan_file(&self, path: &std::path::Path) -> Result<Vec<ThreatDetection>, ScanError> {
        let mut detections = Vec::new();
        
        // Check if file exists
        if !path.exists() {
            return Err(ScanError::FileNotFound);
        }
        
        // Step 1: Compute file hash and check against hash database
        if let Some(detection) = self.check_file_hash(path)? {
            detections.push(detection);
        }
        
        // Step 2: Scan file content for byte patterns
        let mut byte_detections = self.scan_file_bytes(path)?;
        detections.append(&mut byte_detections);
        
        // Step 3: For text files, scan for regex patterns
        if self.is_text_file(path) {
            let mut regex_detections = self.scan_file_regex(path)?;
            detections.append(&mut regex_detections);
        }
        
        Ok(detections)
    }
    
    // Implementation details will follow
}

/// Error types for the scanner
pub enum ScanError {
    FileNotFound,
    IoError(std::io::Error),
    HashingError,
    DatabaseError,
}

impl From<std::io::Error> for ScanError {
    fn from(error: std::io::Error) -> Self {
        ScanError::IoError(error)
    }
}
```

## File Hash Verification

Let's implement the file hash verification method:

```rust
impl SignatureScanner {
    // ... (previous methods)
    
    fn check_file_hash(&self, path: &std::path::Path) -> Result<Option<ThreatDetection>, ScanError> {
        // Read the file
        let mut file = std::fs::File::open(path)?;
        
        // Calculate SHA-256 hash
        let mut hasher = Sha256::new();
        std::io::copy(&mut file, &mut hasher)?;
        let hash = format!("{:x}", hasher.finalize());
        
        // Check if hash exists in database
        if self.database.contains_file_hash(&hash) {
            let signature_id = self.get_signature_id_for_hash(&hash)
                .ok_or(ScanError::DatabaseError)?;
                
            let signature = self.database.signature_details.get(signature_id)
                .ok_or(ScanError::DatabaseError)?;
                
            return Ok(Some(ThreatDetection {
                signature_id: signature.id.clone(),
                threat_name: signature.threat_name.clone(),
                severity: signature.severity.clone(),
                description: signature.description.clone(),
                detection_type: SignatureType::FileHash,
                detection_location: path.to_string_lossy().to_string(),
                timestamp: std::time::SystemTime::now(),
            }));
        }
        
        Ok(None)
    }
    
    fn get_signature_id_for_hash(&self, hash: &str) -> Option<&str> {
        // In a real implementation, we would look up the signature ID that corresponds to this hash
        // For simplicity, we're just returning a placeholder
        if self.database.contains_file_hash(hash) {
            // This is a placeholder - in reality, we'd need to store a mapping from hash to signature ID
            Some("placeholder_id")
        } else {
            None
        }
    }
}
```

## Byte Pattern Matching

Next, let's implement byte pattern matching for detecting malware based on specific byte sequences:

```rust
impl SignatureScanner {
    // ... (previous methods)
    
    fn scan_file_bytes(&self, path: &std::path::Path) -> Result<Vec<ThreatDetection>, ScanError> {
        let mut detections = Vec::new();
        
        // Read file content
        let content = std::fs::read(path)?;
        
        // Check each byte pattern
        for (pattern, wildcards, signature_id) in &self.database.byte_patterns {
            if self.matches_byte_pattern(&content, pattern, wildcards) {
                if let Some(signature) = self.database.signature_details.get(signature_id) {
                    detections.push(ThreatDetection {
                        signature_id: signature.id.clone(),
                        threat_name: signature.threat_name.clone(),
                        severity: signature.severity.clone(),
                        description: signature.description.clone(),
                        detection_type: SignatureType::BytePattern,
                        detection_location: path.to_string_lossy().to_string(),
                        timestamp: std::time::SystemTime::now(),
                    });
                }
            }
        }
        
        Ok(detections)
    }
    
    fn matches_byte_pattern(&self, content: &[u8], pattern: &[u8], wildcards: &Option<Vec<usize>>) -> bool {
        // For very large files, we should use more efficient algorithms like Rabin-Karp or Boyer-Moore
        // This is a simple sliding window approach for demonstration
        
        'outer: for window_start in 0..=content.len().saturating_sub(pattern.len()) {
            for i in 0..pattern.len() {
                // Skip comparison if this is a wildcard position
                if let Some(wild_positions) = wildcards {
                    if wild_positions.contains(&i) {
                        continue;
                    }
                }
                
                if content[window_start + i] != pattern[i] {
                    continue 'outer;
                }
            }
            
            // If we get here, we found a match
            return true;
        }
        
        false
    }
    
    fn is_text_file(&self, path: &std::path::Path) -> bool {
        // A simple heuristic to detect text files
        // In a real implementation, we would use more sophisticated methods
        if let Some(extension) = path.extension() {
            let ext = extension.to_string_lossy().to_lowercase();
            return matches!(ext.as_str(), 
                "txt" | "log" | "xml" | "json" | "html" | "htm" | "css" | "js" | 
                "py" | "rs" | "c" | "cpp" | "h" | "hpp" | "sh" | "bat" | "ps1" |
                "php" | "asp" | "aspx" | "jsp" | "md" | "sql");
        }
        
        // For files without extension, we could check the content
        // For simplicity, we'll just return false
        false
    }
}
```

## Regular Expression Patterns

Now let's add regex pattern matching for text files:

```rust
impl SignatureScanner {
    // ... (previous methods)
    
    fn scan_file_regex(&self, path: &std::path::Path) -> Result<Vec<ThreatDetection>, ScanError> {
        let mut detections = Vec::new();
        
        // Read file content as string
        let content = std::fs::read_to_string(path)?;
        
        // Check each regex pattern
        for (regex, signature_id) in &self.database.regex_patterns {
            if regex.is_match(&content) {
                if let Some(signature) = self.database.signature_details.get(signature_id) {
                    detections.push(ThreatDetection {
                        signature_id: signature.id.clone(),
                        threat_name: signature.threat_name.clone(),
                        severity: signature.severity.clone(),
                        description: signature.description.clone(),
                        detection_type: SignatureType::RegexPattern,
                        detection_location: path.to_string_lossy().to_string(),
                        timestamp: std::time::SystemTime::now(),
                    });
                }
            }
        }
        
        Ok(detections)
    }
}
```

## Signature Updates

Keeping signature databases current is critical. Let's implement a basic update mechanism:

```rust
impl SignatureDatabase {
    // ... (previous methods)
    
    pub fn update_from_source(&mut self, source_url: &str) -> Result<u32, UpdateError> {
        // In a real implementation, we would:
        // 1. Download signature updates from the source URL
        // 2. Parse the new signatures
        // 3. Add them to our database
        // 4. Update the last_updated timestamp
        
        // For this tutorial, we'll simulate an update
        println!("Downloading signature updates from {}", source_url);
        println!("Processing signature updates...");
        
        // Simulate adding new signatures
        let new_signature = Signature {
            id: "SIG-2023-001".to_string(),
            signature_type: SignatureType::FileHash,
            pattern: "eaaf22f5a654aa99a3c49d2f84b08893c1e712ef33a653cb5863d81b".to_string(),
            threat_name: "Trojan.FakeAV.2023".to_string(),
            severity: ThreatSeverity::High,
            description: "Fake antivirus malware variant detected in 2023".to_string(),
        };
        
        self.add_file_hash(&new_signature.pattern, new_signature);
        
        // Update timestamp
        self.last_updated = std::time::SystemTime::now();
        
        // Return the number of new signatures added
        Ok(1)
    }
    
    pub fn last_update_time(&self) -> std::time::SystemTime {
        self.last_updated
    }
}

pub enum UpdateError {
    NetworkError,
    ParsingError,
    DatabaseError,
}
```

## Performance Considerations

Signature scanning can be resource-intensive, especially with large files or many signatures. Let's discuss optimization strategies:

```rust
impl SignatureScanner {
    // ... (previous methods)
    
    /// Optimize scanning by prioritizing different detection methods
    pub fn optimized_scan_file(&self, path: &std::path::Path) -> Result<Vec<ThreatDetection>, ScanError> {
        // 1. Check file size first - skip extremely large files or handle them differently
        let metadata = std::fs::metadata(path)?;
        let file_size = metadata.len();
        
        if file_size > 100_000_000 {  // 100 MB threshold
            return self.large_file_scan_strategy(path);
        }
        
        // 2. For small files, compute hash first (fastest check)
        if let Some(detection) = self.check_file_hash(path)? {
            return Ok(vec![detection]);  // Early return on hash match
        }
        
        // 3. For small text files, regex might be faster than byte pattern matching
        if file_size < 1_000_000 && self.is_text_file(path) {  // 1 MB threshold
            let mut detections = self.scan_file_regex(path)?;
            if !detections.is_empty() {
                return Ok(detections);
            }
        }
        
        // 4. Finally check byte patterns
        self.scan_file_bytes(path)
    }
    
    fn large_file_scan_strategy(&self, path: &std::path::Path) -> Result<Vec<ThreatDetection>, ScanError> {
        // For large files, we could:
        // 1. Only scan the beginning and end of the file
        // 2. Use memory mapping (mmap) to avoid loading the entire file
        // 3. Scan in chunks
        
        // For this tutorial, we'll implement a simple chunked scanning approach
        let mut detections = Vec::new();
        let file = std::fs::File::open(path)?;
        let mut reader = std::io::BufReader::new(file);
        
        let chunk_size = 4096 * 1024;  // 4 MB chunks
        let mut buffer = vec![0u8; chunk_size];
        
        loop {
            // Read a chunk
            let bytes_read = reader.read(&mut buffer)?;
            if bytes_read == 0 {
                break;  // End of file
            }
            
            // Check byte patterns in this chunk
            for (pattern, wildcards, signature_id) in &self.database.byte_patterns {
                if self.matches_byte_pattern(&buffer[..bytes_read], pattern, wildcards) {
                    if let Some(signature) = self.database.signature_details.get(signature_id) {
                        detections.push(ThreatDetection {
                            signature_id: signature.id.clone(),
                            threat_name: signature.threat_name.clone(),
                            severity: signature.severity.clone(),
                            description: signature.description.clone(),
                            detection_type: SignatureType::BytePattern,
                            detection_location: path.to_string_lossy().to_string(),
                            timestamp: std::time::SystemTime::now(),
                        });
                        
                        // Once we find one match, we can stop scanning this file
                        return Ok(detections);
                    }
                }
            }
            
            // Handle partial matches across chunk boundaries
            // This would be necessary for a real implementation
        }
        
        Ok(detections)
    }
}
```

## Integration with CyberShield

Now let's see how to integrate our signature-based detection with the rest of our CyberShield system:

```rust
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;

/// Main integration with the CyberShield system
pub struct SignatureDetectionService {
    scanner: Arc<SignatureScanner>,
    detections: Arc<Mutex<Vec<ThreatDetection>>>,
    is_running: Arc<Mutex<bool>>,
}

impl SignatureDetectionService {
    pub fn new(database_path: &std::path::Path) -> Result<Self, std::io::Error> {
        // Load signature database
        let database = Self::load_database(database_path)?;
        let scanner = Arc::new(SignatureScanner::new(database));
        
        Ok(SignatureDetectionService {
            scanner,
            detections: Arc::new(Mutex::new(Vec::new())),
            is_running: Arc::new(Mutex::new(false)),
        })
    }
    
    fn load_database(path: &std::path::Path) -> Result<SignatureDatabase, std::io::Error> {
        // In a real implementation, we would:
        // 1. Load database from file
        // 2. Parse the signatures
        // 3. Populate our SignatureDatabase structure
        
        // For this tutorial, we'll create a sample database
        let mut db = SignatureDatabase::new();
        
        // Add some example signatures
        let sig1 = Signature {
            id: "SIG-2022-001".to_string(),
            signature_type: SignatureType::FileHash,
            pattern: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855".to_string(),
            threat_name: "Malware.EmptyFile.TEST".to_string(),
            severity: ThreatSeverity::Low,
            description: "Test signature for empty file".to_string(),
        };
        db.add_file_hash(&sig1.pattern, sig1);
        
        // Add a byte pattern signature (example: MZ header for PE files)
        let sig2 = Signature {
            id: "SIG-2022-002".to_string(),
            signature_type: SignatureType::BytePattern,
            pattern: "4D5A90000300000004000000FFFF".to_string(), // MZ header
            threat_name: "Suspicious.PE.Header".to_string(),
            severity: ThreatSeverity::Medium,
            description: "Suspicious PE file header".to_string(),
        };
        let bytes = hex::decode("4D5A90000300000004000000FFFF").unwrap();
        db.add_byte_pattern(bytes, None, sig2);
        
        // Add a regex pattern signature
        let sig3 = Signature {
            id: "SIG-2022-003".to_string(),
            signature_type: SignatureType::RegexPattern,
            pattern: r"eval\(base64_decode\(['\"].*['\"]\)\)".to_string(),
            threat_name: "Malicious.PHP.Obfuscation".to_string(),
            severity: ThreatSeverity::High,
            description: "PHP code with base64 obfuscation".to_string(),
        };
        db.add_regex_pattern(&sig3.pattern, sig3).unwrap();
        
        Ok(db)
    }
    
    pub fn start_monitoring(&self, directories: Vec<std::path::PathBuf>, scan_interval: Duration) -> Result<(), std::io::Error> {
        // Set running flag
        let mut is_running = self.is_running.lock().unwrap();
        if *is_running {
            return Ok(());  // Already running
        }
        *is_running = true;
        drop(is_running);
        
        // Clone Arc references for the worker thread
        let scanner = self.scanner.clone();
        let detections = self.detections.clone();
        let is_running = self.is_running.clone();
        
        // Start monitoring thread
        thread::spawn(move || {
            while *is_running.lock().unwrap() {
                // Scan all monitored directories
                for dir in &directories {
                    Self::scan_directory(dir, &scanner, &detections);
                }
                
                // Wait for next scan interval
                thread::sleep(scan_interval);
            }
        });
        
        Ok(())
    }
    
    fn scan_directory(
        dir: &std::path::Path,
        scanner: &Arc<SignatureScanner>,
        detections: &Arc<Mutex<Vec<ThreatDetection>>>
    ) {
        if !dir.is_dir() {
            return;
        }
        
        // Recursively walk the directory
        if let Ok(entries) = std::fs::read_dir(dir) {
            for entry in entries.flatten() {
                let path = entry.path();
                
                if path.is_file() {
                    // Scan file
                    if let Ok(mut file_detections) = scanner.optimized_scan_file(&path) {
                        if !file_detections.is_empty() {
                            // Add detections to the shared list
                            let mut all_detections = detections.lock().unwrap();
                            all_detections.append(&mut file_detections);
                        }
                    }
                } else if path.is_dir() {
                    // Recursively scan subdirectory
                    Self::scan_directory(&path, scanner, detections);
                }
            }
        }
    }
    
    pub fn stop_monitoring(&self) {
        let mut is_running = self.is_running.lock().unwrap();
        *is_running = false;
    }
    
    pub fn get_detections(&self) -> Vec<ThreatDetection> {
        let detections = self.detections.lock().unwrap();
        detections.clone()
    }
    
    pub fn clear_detections(&self) {
        let mut detections = self.detections.lock().unwrap();
        detections.clear();
    }
}
```

## Performance Considerations

When implementing signature-based detection, consider these performance best practices:

1. **Efficient Data Structures**: Use hash tables for fast lookups of file hashes.

2. **Resource Management**: 
   - Scan files in chunks rather than loading them entirely into memory
   - Use memory mapping (mmap) for large files
   - Implement background scanning with limited resource usage

3. **Prioritized Scanning**:
   - Start with the fastest detection method (hash checking)
   - Move to more intensive methods only if needed
   - Skip certain file types or extremely large files in real-time scanning

4. **Optimized Algorithms**:
   - Use Boyer-Moore or Rabin-Karp for byte pattern matching
   - Compile regex patterns in advance
   - Use specialized libraries like hyperscan for regex matching

## Exercises

1. **Basic Implementation**: Create a signature database with at least 10 sample signatures (including file hashes, byte patterns, and regex patterns).

2. **Scanner Enhancement**: Add a feature to the `SignatureScanner` that allows it to scan a memory buffer instead of a file.

3. **Performance Testing**: Create a benchmark to measure the scanning speed on different file sizes and with different numbers of signatures.

4. **Advanced**: Implement a YARA-like rule system that allows combining multiple signature types in one rule.

## Next Steps

In the next section, we'll explore heuristic analysis, which allows us to detect threats based on suspicious characteristics rather than exact matches. This will help us identify previously unknown threats and variants of known malware.

## Navigation

- Previous: [Understanding Threat Detection](./01-understanding-threat-detection.md)
- Next: [Heuristic Analysis](./03-heuristic-analysis.md)
