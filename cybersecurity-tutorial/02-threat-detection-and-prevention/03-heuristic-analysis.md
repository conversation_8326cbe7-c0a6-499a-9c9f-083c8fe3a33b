# Heuristic Analysis for Threat Detection

Heuristic analysis is a proactive approach to identifying potential threats by examining file structures, code patterns, and behaviors that resemble known malicious techniques, without relying on exact signatures. Unlike signature-based detection, heuristic analysis can identify previously unknown or modified threats based on their suspicious characteristics.

## Learning Objectives

- Understand the principles behind heuristic analysis
- Implement different heuristic detection techniques in Rust
- Build a scoring system to evaluate potential threats
- Create adaptable heuristic rules for various threat scenarios

## Heuristic Analysis Fundamentals

Heuristic analysis works by:

1. Examining file structures, code patterns, and behaviors
2. Applying a set of rules or algorithms to identify suspicious characteristics
3. Assigning risk scores based on the analysis
4. Flagging items that exceed a predefined threshold

Heuristic detection provides advantages over signature-based methods:

- Can detect zero-day threats (previously unknown malware)
- Identifies modified variants of known malware
- Adapts to evolving threats through rule adjustments
- Provides flexible detection sensitivity through scoring thresholds

However, it also has limitations:

- Higher false positive rates
- More resource-intensive processing
- Requires careful tuning to balance sensitivity
- Depends on expert knowledge for effective rule creation

## Creating a Heuristic Analysis Engine in Rust

Let's build a basic heuristic analysis engine for executable files:

```rust
use std::fs::File;
use std::io::{self, Read};
use std::path::Path;

#[derive(Debug, Default)]
struct HeuristicEngine {
    rules: Vec<Box<dyn HeuristicRule>>,
    threshold: u32,
}

trait HeuristicRule {
    fn check(&self, file_data: &[u8]) -> u32;
    fn name(&self) -> &str;
}

struct SuspiciousImportsRule {
    suspicious_imports: Vec<String>,
    score: u32,
}

impl HeuristicRule for SuspiciousImportsRule {
    fn check(&self, file_data: &[u8]) -> u32 {
        let content = String::from_utf8_lossy(file_data);
        
        // Count matches of suspicious imports/API calls
        let mut matches = 0;
        for import in &self.suspicious_imports {
            if content.contains(import) {
                matches += 1;
            }
        }
        
        // Return score based on number of matches
        if matches > 0 {
            self.score * matches
        } else {
            0
        }
    }
    
    fn name(&self) -> &str {
        "Suspicious Imports Rule"
    }
}

struct ExecutableHeaderRule {
    score: u32,
}

impl HeuristicRule for ExecutableHeaderRule {
    fn check(&self, file_data: &[u8]) -> u32 {
        // Look for PE header (Windows executables)
        if file_data.len() > 0x40 && 
           file_data[0] == b'M' && 
           file_data[1] == b'Z' {
            
            // Check for more suspicious PE characteristics
            // This is simplified; real implementation would check more header details
            if file_data.len() > 0x200 {
                let pe_offset = u32::from_le_bytes([
                    file_data[0x3C], file_data[0x3D], file_data[0x3E], file_data[0x3F]
                ]) as usize;
                
                if pe_offset < file_data.len() && 
                   file_data[pe_offset] == b'P' && 
                   file_data[pe_offset + 1] == b'E' {
                    return self.score;
                }
            }
        }
        
        0
    }
    
    fn name(&self) -> &str {
        "Executable Header Rule"
    }
}

struct EntropyAnalysisRule {
    threshold: f64,
    score: u32,
}

impl HeuristicRule for EntropyAnalysisRule {
    fn check(&self, file_data: &[u8]) -> u32 {
        // Calculate Shannon entropy to detect obfuscation or encryption
        let entropy = calculate_entropy(file_data);
        
        // High entropy often indicates encryption or obfuscation
        if entropy > self.threshold {
            self.score
        } else {
            0
        }
    }
    
    fn name(&self) -> &str {
        "Entropy Analysis Rule"
    }
}

fn calculate_entropy(data: &[u8]) -> f64 {
    if data.is_empty() {
        return 0.0;
    }

    // Count byte frequencies
    let mut frequencies = [0usize; 256];
    for &byte in data {
        frequencies[byte as usize] += 1;
    }
    
    // Calculate Shannon entropy
    let data_len = data.len() as f64;
    let mut entropy = 0.0;
    
    for &count in &frequencies {
        if count > 0 {
            let probability = count as f64 / data_len;
            entropy -= probability * probability.log2();
        }
    }
    
    entropy
}

impl HeuristicEngine {
    pub fn new(threshold: u32) -> Self {
        Self {
            rules: Vec::new(),
            threshold,
        }
    }
    
    pub fn add_rule(&mut self, rule: Box<dyn HeuristicRule>) {
        self.rules.push(rule);
    }
    
    pub fn scan_file<P: AsRef<Path>>(&self, file_path: P) -> io::Result<HeuristicScanResult> {
        // Read file content
        let mut file = File::open(file_path)?;
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer)?;
        
        self.scan_data(&buffer)
    }
    
    pub fn scan_data(&self, data: &[u8]) -> io::Result<HeuristicScanResult> {
        let mut total_score = 0;
        let mut triggered_rules = Vec::new();
        
        // Apply each rule
        for rule in &self.rules {
            let score = rule.check(data);
            if score > 0 {
                triggered_rules.push((rule.name().to_string(), score));
                total_score += score;
            }
        }
        
        let is_threat = total_score >= self.threshold;
        
        Ok(HeuristicScanResult {
            total_score,
            triggered_rules,
            is_threat,
        })
    }
}

#[derive(Debug)]
struct HeuristicScanResult {
    total_score: u32,
    triggered_rules: Vec<(String, u32)>,
    is_threat: bool,
}

fn main() -> io::Result<()> {
    // Create a heuristic engine with threshold 50
    let mut engine = HeuristicEngine::new(50);
    
    // Add rules
    engine.add_rule(Box::new(SuspiciousImportsRule {
        suspicious_imports: vec![
            "CreateRemoteThread".to_string(),
            "VirtualAllocEx".to_string(),
            "SetWindowsHookEx".to_string(),
            "GetAsyncKeyState".to_string(),
            "WinExec".to_string(),
        ],
        score: 15,
    }));
    
    engine.add_rule(Box::new(ExecutableHeaderRule { score: 10 }));
    
    engine.add_rule(Box::new(EntropyAnalysisRule { 
        threshold: 7.2, 
        score: 30 
    }));
    
    // Example usage
    let result = engine.scan_file("path/to/suspicious_file.exe")?;
    
    println!("Scan completed with score: {}", result.total_score);
    println!("Threat detected: {}", result.is_threat);
    println!("Triggered rules:");
    
    for (rule_name, score) in result.triggered_rules {
        println!("  - {}: {}", rule_name, score);
    }
    
    Ok(())
}
```

## Customizing Heuristic Rules for Different Threat Types

Different types of threats require specialized heuristic rules. Let's expand our engine to include various rules for different threat types:

### Document Analysis Rules

```rust
struct MacroDetectionRule {
    score: u32,
}

impl HeuristicRule for MacroDetectionRule {
    fn check(&self, file_data: &[u8]) -> u32 {
        let content = String::from_utf8_lossy(file_data);
        
        // Check for VBA macro indicators
        let vba_indicators = [
            "Sub ", "Function ", "AutoOpen", "Document_Open", 
            "Workbook_Open", "Shell(", "CreateObject("
        ];
        
        let mut suspicious_count = 0;
        for indicator in &vba_indicators {
            if content.contains(indicator) {
                suspicious_count += 1;
            }
        }
        
        // Return score based on indicators found
        if suspicious_count >= 3 {
            self.score
        } else if suspicious_count > 0 {
            self.score / 2
        } else {
            0
        }
    }
    
    fn name(&self) -> &str {
        "Macro Detection Rule"
    }
}
```

### Script Analysis Rules

```rust
struct ObfuscatedScriptRule {
    score: u32,
}

impl HeuristicRule for ObfuscatedScriptRule {
    fn check(&self, file_data: &[u8]) -> u32 {
        let content = String::from_utf8_lossy(file_data);
        
        // Check for obfuscation techniques
        let obfuscation_indicators = [
            // Long encoded strings
            "base64", "fromCharCode", "unescape", 
            // Excessive character manipulation
            "charAt", "substring", "split('').reverse().join('')",
            // Eval usage
            "eval(", "Function(", "setTimeout(", "setInterval("
        ];
        
        let mut matches = 0;
        for indicator in &obfuscation_indicators {
            if content.contains(indicator) {
                matches += 1;
            }
        }
        
        // Count unusually long strings or variables
        let long_string_count = content
            .match_indices("\"")
            .collect::<Vec<_>>()
            .chunks(2)
            .filter(|chunk| chunk.len() == 2 && chunk[1].0 - chunk[0].0 > 500)
            .count();
        
        if matches >= 3 || long_string_count >= 2 {
            self.score
        } else if matches > 0 || long_string_count > 0 {
            self.score / 2
        } else {
            0
        }
    }
    
    fn name(&self) -> &str {
        "Obfuscated Script Rule"
    }
}
```

## Implementing a Weighted Scoring System

A sophisticated heuristic engine should use a weighted scoring system to balance different indicators:

```rust
struct WeightedHeuristicEngine {
    rules: Vec<(Box<dyn HeuristicRule>, f32)>,  // (rule, weight)
    threshold: f32,
}

impl WeightedHeuristicEngine {
    pub fn new(threshold: f32) -> Self {
        Self {
            rules: Vec::new(),
            threshold,
        }
    }
    
    pub fn add_rule(&mut self, rule: Box<dyn HeuristicRule>, weight: f32) {
        self.rules.push((rule, weight));
    }
    
    pub fn scan_data(&self, data: &[u8]) -> io::Result<WeightedScanResult> {
        let mut total_score: f32 = 0.0;
        let mut triggered_rules = Vec::new();
        
        // Apply each rule with its weight
        for (rule, weight) in &self.rules {
            let score = rule.check(data) as f32;
            if score > 0.0 {
                let weighted_score = score * weight;
                triggered_rules.push((rule.name().to_string(), weighted_score));
                total_score += weighted_score;
            }
        }
        
        let is_threat = total_score >= self.threshold;
        
        Ok(WeightedScanResult {
            total_score,
            triggered_rules,
            is_threat,
        })
    }
}

#[derive(Debug)]
struct WeightedScanResult {
    total_score: f32,
    triggered_rules: Vec<(String, f32)>,
    is_threat: bool,
}
```

## Handling False Positives

One of the biggest challenges with heuristic analysis is managing false positives. We can reduce these by:

1. **Contextual Analysis**: Considering the source and context of the file:

```rust
struct ContextAwareEngine {
    base_engine: HeuristicEngine,
    trusted_sources: Vec<String>,
    sensitive_locations: Vec<String>,
    context_multipliers: std::collections::HashMap<String, f32>,
}

impl ContextAwareEngine {
    // Apply different thresholds based on file context
    pub fn scan_file_with_context<P: AsRef<Path>>(
        &self, 
        file_path: P,
        source: Option<&str>
    ) -> io::Result<HeuristicScanResult> {
        let path = file_path.as_ref();
        let result = self.base_engine.scan_file(path)?;
        
        // Apply context adjustments
        let mut adjusted_score = result.total_score as f32;
        
        // Adjust based on source
        if let Some(src) = source {
            if self.trusted_sources.contains(&src.to_string()) {
                adjusted_score *= 0.7;  // Reduce score for trusted sources
            }
        }
        
        // Adjust based on file location
        if let Some(path_str) = path.to_str() {
            for sensitive_loc in &self.sensitive_locations {
                if path_str.contains(sensitive_loc) {
                    adjusted_score *= 1.3;  // Increase score for sensitive locations
                    break;
                }
            }
            
            // Apply specific context multipliers
            for (context, multiplier) in &self.context_multipliers {
                if path_str.contains(context) {
                    adjusted_score *= multiplier;
                    break;
                }
            }
        }
        
        // Create adjusted result
        Ok(HeuristicScanResult {
            total_score: adjusted_score as u32,
            triggered_rules: result.triggered_rules,
            is_threat: adjusted_score >= self.base_engine.threshold as f32,
        })
    }
}
```

2. **Whitelisting**: Creating exceptions for known good files or patterns:

```rust
struct WhitelistAwareEngine {
    base_engine: HeuristicEngine,
    file_hashes: std::collections::HashSet<String>,
    pattern_exceptions: Vec<regex::Regex>,
}

impl WhitelistAwareEngine {
    pub fn scan_file<P: AsRef<Path>>(&self, file_path: P) -> io::Result<Option<HeuristicScanResult>> {
        use sha2::{Sha256, Digest};
        
        let mut file = File::open(&file_path)?;
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer)?;
        
        // Check file hash against whitelist
        let mut hasher = Sha256::new();
        hasher.update(&buffer);
        let hash = format!("{:x}", hasher.finalize());
        
        if self.file_hashes.contains(&hash) {
            // File is whitelisted, skip scanning
            return Ok(None);
        }
        
        // Check content against whitelisted patterns
        let content = String::from_utf8_lossy(&buffer);
        for pattern in &self.pattern_exceptions {
            if pattern.is_match(&content) {
                // Content contains whitelisted pattern
                return Ok(None);
            }
        }
        
        // Proceed with regular scanning
        self.base_engine.scan_data(&buffer).map(Some)
    }
}
```

## Performance Considerations

Heuristic analysis can be computationally intensive. Here are techniques to optimize performance:

```rust
// Sampling large files instead of full analysis
fn scan_large_file<P: AsRef<Path>>(file_path: P, engine: &HeuristicEngine) -> io::Result<HeuristicScanResult> {
    const MAX_SAMPLE_SIZE: usize = 10 * 1024 * 1024; // 10MB
    const NUM_SAMPLES: usize = 5;
    
    let file = File::open(&file_path)?;
    let file_size = file.metadata()?.len() as usize;
    
    if file_size <= MAX_SAMPLE_SIZE {
        // For small files, scan the entire content
        return engine.scan_file(file_path);
    }
    
    // For large files, take samples from different regions
    let mut file = File::open(file_path)?;
    let mut combined_sample = Vec::with_capacity(NUM_SAMPLES * (MAX_SAMPLE_SIZE / NUM_SAMPLES));
    
    // Read beginning of file
    let mut start_sample = vec![0; MAX_SAMPLE_SIZE / NUM_SAMPLES];
    file.read_exact(&mut start_sample)?;
    combined_sample.extend_from_slice(&start_sample);
    
    // Read samples from throughout the file
    let step = (file_size - (MAX_SAMPLE_SIZE / NUM_SAMPLES)) / (NUM_SAMPLES - 1);
    for i in 1..NUM_SAMPLES {
        file.seek(std::io::SeekFrom::Start((i * step) as u64))?;
        let mut sample = vec![0; MAX_SAMPLE_SIZE / NUM_SAMPLES];
        file.read_exact(&mut sample)?;
        combined_sample.extend_from_slice(&sample);
    }
    
    // Scan the combined samples
    engine.scan_data(&combined_sample)
}

// Parallel scanning for multiple files
fn parallel_scan(files: Vec<String>, engine: &HeuristicEngine) -> Vec<(String, Result<HeuristicScanResult, io::Error>)> {
    use rayon::prelude::*;
    
    files.par_iter().map(|file_path| {
        let result = engine.scan_file(file_path);
        (file_path.clone(), result)
    }).collect()
}
```

## Practical Exercise: Building a Custom Heuristic Engine

Now, let's create a complete, customizable heuristic analysis engine:

```rust
use std::collections::{HashMap, HashSet};
use std::fs::File;
use std::io::{self, Read, Seek};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use rayon::prelude::*;
use serde::{Serialize, Deserialize};

#[derive(Clone, Debug, Serialize, Deserialize)]
struct HeuristicRule {
    name: String,
    patterns: Vec<Pattern>,
    score: u32,
    description: String,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
enum Pattern {
    ByteSequence(Vec<u8>),
    StringPattern(String),
    RegexPattern(String),
    FileSizeRange { min: Option<u64>, max: Option<u64> },
    EntropyThreshold(f64),
    ImportSignature(Vec<String>),
    Custom(String), // Custom rule identifier to be handled separately
}

#[derive(Debug)]
struct ConfigurableHeuristicEngine {
    rules: Vec<HeuristicRule>,
    whitelist: HashSet<String>,
    context_modifiers: HashMap<String, f32>,
    base_threshold: u32,
}

#[derive(Debug, Serialize)]
struct ScanResult {
    file_path: PathBuf,
    total_score: u32,
    triggered_rules: Vec<(String, u32)>,
    is_threat: bool,
    scan_time_ms: u64,
    details: HashMap<String, String>,
}

impl ConfigurableHeuristicEngine {
    pub fn from_config_file<P: AsRef<Path>>(config_path: P) -> io::Result<Self> {
        let mut file = File::open(config_path)?;
        let mut content = String::new();
        file.read_to_end(&mut content)?;
        
        let config: EngineConfig = serde_json::from_str(&content)?;
        
        Ok(Self {
            rules: config.rules,
            whitelist: config.whitelist.into_iter().collect(),
            context_modifiers: config.context_modifiers,
            base_threshold: config.threshold,
        })
    }
    
    pub fn scan_file<P: AsRef<Path>>(&self, file_path: P) -> io::Result<ScanResult> {
        let path = file_path.as_ref();
        let start_time = std::time::Instant::now();
        
        // Check whitelist
        if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
            if self.whitelist.contains(file_name) {
                return Ok(ScanResult {
                    file_path: path.to_path_buf(),
                    total_score: 0,
                    triggered_rules: Vec::new(),
                    is_threat: false,
                    scan_time_ms: start_time.elapsed().as_millis() as u64,
                    details: HashMap::from([("result".to_string(), "Whitelisted".to_string())]),
                });
            }
        }
        
        // Read file 
        let mut file = File::open(path)?;
        let file_size = file.metadata()?.len();
        
        // Determine if we need sampling
        let data = if file_size > 10_000_000 { // 10MB
            self.sample_large_file(&mut file, file_size)?
        } else {
            let mut buffer = Vec::new();
            file.read_to_end(&mut buffer)?;
            buffer
        };
        
        let text_content = String::from_utf8_lossy(&data);
        
        // Apply rules
        let mut total_score = 0;
        let mut triggered_rules = Vec::new();
        let mut details = HashMap::new();
        
        for rule in &self.rules {
            let mut rule_score = 0;
            let mut rule_matches = Vec::new();
            
            for pattern in &rule.patterns {
                let match_score = match pattern {
                    Pattern::ByteSequence(bytes) => {
                        if data.windows(bytes.len()).any(|window| window == bytes.as_slice()) {
                            rule.score
                        } else {
                            0
                        }
                    },
                    Pattern::StringPattern(s) => {
                        if text_content.contains(s) {
                            rule.score
                        } else {
                            0
                        }
                    },
                    Pattern::RegexPattern(r) => {
                        match regex::Regex::new(r) {
                            Ok(regex) => {
                                if regex.is_match(&text_content) {
                                    rule.score
                                } else {
                                    0
                                }
                            },
                            Err(_) => 0,
                        }
                    },
                    Pattern::FileSizeRange { min, max } => {
                        let mut matches = true;
                        if let Some(min_size) = min {
                            matches &= file_size >= *min_size;
                        }
                        if let Some(max_size) = max {
                            matches &= file_size <= *max_size;
                        }
                        if matches { rule.score } else { 0 }
                    },
                    Pattern::EntropyThreshold(threshold) => {
                        let entropy = calculate_entropy(&data);
                        if entropy > *threshold {
                            rule.score
                        } else {
                            0
                        }
                    },
                    Pattern::ImportSignature(imports) => {
                        let mut match_count = 0;
                        for import in imports {
                            if text_content.contains(import) {
                                match_count += 1;
                            }
                        }
                        
                        if match_count > imports.len() / 2 {
                            rule.score
                        } else {
                            0
                        }
                    },
                    Pattern::Custom(custom_id) => {
                        // Handle custom rules separately
                        match custom_id.as_str() {
                            "pe_header_check" => check_pe_header(&data),
                            "script_obfuscation" => check_script_obfuscation(&text_content),
                            _ => 0,
                        }
                    }
                };
                
                if match_score > 0 {
                    rule_score += match_score;
                    rule_matches.push(pattern.clone());
                }
            }
            
            if rule_score > 0 {
                total_score += rule_score;
                triggered_rules.push((rule.name.clone(), rule_score));
                details.insert(
                    rule.name.clone(),
                    format!("{} (score: {})", rule.description, rule_score)
                );
            }
        }
        
        // Apply context modifiers
        let file_str = path.to_string_lossy();
        for (context, modifier) in &self.context_modifiers {
            if file_str.contains(context) {
                total_score = (total_score as f32 * modifier) as u32;
                details.insert("context_modifier".to_string(), 
                              format!("Applied modifier {} for context '{}'", modifier, context));
                break;
            }
        }
        
        let is_threat = total_score >= self.base_threshold;
        let scan_time = start_time.elapsed().as_millis() as u64;
        
        Ok(ScanResult {
            file_path: path.to_path_buf(),
            total_score,
            triggered_rules,
            is_threat,
            scan_time_ms: scan_time,
            details,
        })
    }
    
    fn sample_large_file(&self, file: &mut File, file_size: u64) -> io::Result<Vec<u8>> {
        const SAMPLE_SIZE: usize = 1024 * 1024; // 1MB per sample
        const NUM_SAMPLES: usize = 5;
        
        let mut combined_sample = Vec::with_capacity(NUM_SAMPLES * SAMPLE_SIZE);
        
        // Beginning of file
        let mut buffer = vec![0u8; SAMPLE_SIZE];
        file.read_exact(&mut buffer)?;
        combined_sample.extend_from_slice(&buffer);
        
        // Samples from throughout the file
        if file_size > SAMPLE_SIZE as u64 {
            let step = (file_size - SAMPLE_SIZE as u64) / (NUM_SAMPLES as u64 - 1);
            
            for i in 1..NUM_SAMPLES {
                file.seek(io::SeekFrom::Start(i as u64 * step))?;
                file.read_exact(&mut buffer)?;
                combined_sample.extend_from_slice(&buffer);
            }
        }
        
        Ok(combined_sample)
    }
    
    pub fn batch_scan<P: AsRef<Path>>(&self, directory: P, recursive: bool) -> io::Result<Vec<ScanResult>> {
        let files = self.collect_files(directory, recursive)?;
        
        let results: Vec<_> = files.par_iter()
            .map(|path| self.scan_file(path))
            .collect();
        
        // Filter out errors
        let valid_results: Vec<_> = results.into_iter()
            .filter_map(|r| r.ok())
            .collect();
            
        Ok(valid_results)
    }
    
    fn collect_files<P: AsRef<Path>>(&self, directory: P, recursive: bool) -> io::Result<Vec<PathBuf>> {
        let mut files = Vec::new();
        let dir = directory.as_ref();
        
        for entry in std::fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_file() {
                files.push(path);
            } else if recursive && path.is_dir() {
                let mut subdir_files = self.collect_files(&path, recursive)?;
                files.append(&mut subdir_files);
            }
        }
        
        Ok(files)
    }
}

#[derive(Deserialize)]
struct EngineConfig {
    rules: Vec<HeuristicRule>,
    whitelist: Vec<String>,
    context_modifiers: HashMap<String, f32>,
    threshold: u32,
}

fn calculate_entropy(data: &[u8]) -> f64 {
    // Same entropy calculation as before
    if data.is_empty() {
        return 0.0;
    }

    let mut frequencies = [0usize; 256];
    for &byte in data {
        frequencies[byte as usize] += 1;
    }
    
    let data_len = data.len() as f64;
    let mut entropy = 0.0;
    
    for &count in &frequencies {
        if count > 0 {
            let probability = count as f64 / data_len;
            entropy -= probability * probability.log2();
        }
    }
    
    entropy
}

fn check_pe_header(data: &[u8]) -> u32 {
    // More sophisticated PE header check
    if data.len() < 64 {
        return 0;
    }
    
    // Check MZ header
    if data[0] != b'M' || data[1] != b'Z' {
        return 0;
    }
    
    // Get PE header offset
    let pe_offset = u32::from_le_bytes([
        data[0x3C], data[0x3D], data[0x3E], data[0x3F]
    ]) as usize;
    
    if pe_offset + 4 > data.len() {
        return 0;
    }
    
    // Check PE signature
    if data[pe_offset] == b'P' && data[pe_offset + 1] == b'E' && 
       data[pe_offset + 2] == 0 && data[pe_offset + 3] == 0 {
        // Additional checks could be performed here
        25
    } else {
        0
    }
}

fn check_script_obfuscation(content: &str) -> u32 {
    let mut score = 0;
    
    // Check for obfuscation techniques
    let obfuscation_patterns = [
        "eval(", "Function(", "fromCharCode", "unescape(", 
        "String.fromCharCode", "parseInt(", "atob(", "btoa("
    ];
    
    for pattern in &obfuscation_patterns {
        if content.contains(pattern) {
            score += 5;
        }
    }
    
    // Check for long strings
    let long_string_count = content
        .match_indices("\"")
        .collect::<Vec<_>>()
        .chunks(2)
        .filter(|chunk| chunk.len() == 2 && chunk[1].0 - chunk[0].0 > 300)
        .count();
    
    score += (long_string_count as u32) * 3;
    
    // Check for base64-encoded content
    let base64_patterns = ["base64", "==", "eyJ"];
    for pattern in &base64_patterns {
        if content.contains(pattern) {
            score += 8;
            break;
        }
    }
    
    // Limit maximum score
    std::cmp::min(score, 35)
}
```

## Example Configuration File

Here's how we could define the engine's configuration in a JSON file:

```json
{
  "threshold": 70,
  "rules": [
    {
      "name": "Suspicious PE Characteristics",
      "patterns": [
        {"Custom": "pe_header_check"},
        {"ImportSignature": ["VirtualAlloc", "CreateRemoteThread", "WriteProcessMemory"]}
      ],
      "score": 25,
      "description": "Detection of suspicious characteristics in PE files"
    },
    {
      "name": "Script Obfuscation",
      "patterns": [
        {"Custom": "script_obfuscation"},
        {"RegexPattern": "\\\\x[0-9a-fA-F]{2}"}
      ],
      "score": 30,
      "description": "Detection of obfuscated script content"
    },
    {
      "name": "High Entropy Content",
      "patterns": [
        {"EntropyThreshold": 7.2}
      ],
      "score": 20,
      "description": "Detection of high entropy content indicative of encryption/packing"
    },
    {
      "name": "Document with Macros",
      "patterns": [
        {"StringPattern": "VBA/"},
        {"StringPattern": "word/vbaProject.bin"},
        {"StringPattern": "Auto_Open"},
        {"StringPattern": "Document_Open"}
      ],
      "score": 15,
      "description": "Detection of documents with macro content"
    }
  ],
  "whitelist": [
    "windows_update.exe",
    "system32.dll",
    "trusteddoc.docx"
  ],
  "context_modifiers": {
    "downloads": 1.2,
    "system32": 0.8,
    "program_files": 0.9,
    "temp": 1.3
  }
}
```

## Conclusion

Heuristic analysis offers a powerful approach to detecting unknown threats that signature-based methods might miss. By combining multiple detection techniques and implementing a weighted scoring system, we can create a flexible and robust threat detection system.

Key points to remember:

1. Heuristic analysis examines patterns and behaviors rather than exact signatures
2. A scoring system helps quantify the risk level of potential threats
3. Context awareness and whitelisting reduce false positives
4. Performance optimizations are crucial for analyzing large files
5. Combining multiple rules and techniques provides more accurate detection

In the next chapter, we'll explore behavioral analysis, which complements heuristic analysis by monitoring the actual behavior of files and processes during execution.

## Exercise

1. Implement a custom rule for detecting potentially malicious PDF files
2. Create a heuristic engine that can be updated with new rules at runtime
3. Design a system to collect and analyze false positives to improve rule accuracy
