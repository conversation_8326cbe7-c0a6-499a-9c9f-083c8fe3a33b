# Packet Capture Implementation

Packet capture is the foundation of network security monitoring and analysis. In this section, we'll implement a robust packet capture system in Rust that can intercept, decode, and analyze network traffic in real-time.

## Overview

We'll cover:

1. Understanding packet capture concepts
2. Using the pcap library in Rust
3. Implementing a flexible packet capture interface
4. Creating parsers for Ethernet, IP, TCP, and UDP packets
5. Building a packet filtering system
6. Handling high-volume traffic efficiently

## Packet Capture Concepts

Packet capture, often referred to as "packet sniffing" or "network tapping," involves intercepting and logging network traffic. This technique is essential for:

- Network traffic analysis
- Troubleshooting network issues
- Intrusion detection
- Network forensics
- Performance optimization

However, packet capture presents several challenges:

1. **Performance**: High-speed networks generate massive volumes of data
2. **Completeness**: Capturing all packets without drops
3. **Timing**: Accurate packet timestamps
4. **Permissions**: Special privileges required to capture raw packets
5. **Wire formats**: Different protocols require different parsing logic

Let's address these challenges as we build our packet capture system.

## Setting Up the pcap Library

The `libpcap` library (and its Windows equivalent, `Npcap` or `WinPcap`) provides a portable API for capturing network packets. We'll use the `pcap` crate, which provides Rust bindings for libpcap.

First, we'll create a module for our packet capture functionality:

```rust
use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use std::thread;

use pcap::{Capture, Device, Active};
use pnet::packet::{Packet, ethernet::{EthernetPacket, EtherTypes}};
use pnet::packet::ip::{IpNextHeaderProtocol, IpNextHeaderProtocols};
use pnet::packet::ipv4::Ipv4Packet;
use pnet::packet::ipv6::Ipv6Packet;
use pnet::packet::tcp::TcpPacket;
use pnet::packet::udp::UdpPacket;
use pnet::packet::icmp::IcmpPacket;
use serde::{Serialize, Deserialize};

use crate::network::models::{NetworkPacket, TransportProtocol, PacketDirection, NetworkInterface};

/// Statistics for a packet capture session
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct CaptureStats {
    pub start_time: u64,
    pub packets_captured: u64,
    pub bytes_captured: u64,
    pub packets_dropped: u64,
    pub running_time_secs: u64,
    
    // Protocol statistics
    pub ethernet_packets: u64,
    pub ipv4_packets: u64,
    pub ipv6_packets: u64,
    pub arp_packets: u64,
    pub tcp_packets: u64,
    pub udp_packets: u64,
    pub icmp_packets: u64,
    pub other_packets: u64,
}

impl CaptureStats {
    pub fn new() -> Self {
        CaptureStats {
            start_time: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            ..Default::default()
        }
    }
    
    pub fn update_running_time(&mut self) {
        self.running_time_secs = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() - self.start_time;
    }
    
    pub fn packets_per_second(&self) -> f64 {
        if self.running_time_secs == 0 {
            return 0.0;
        }
        self.packets_captured as f64 / self.running_time_secs as f64
    }
    
    pub fn bytes_per_second(&self) -> f64 {
        if self.running_time_secs == 0 {
            return 0.0;
        }
        self.bytes_captured as f64 / self.running_time_secs as f64
    }
}

/// Filter to apply to packets during capture
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PacketFilter {
    pub src_ip: Option<IpAddr>,
    pub dst_ip: Option<IpAddr>,
    pub src_port: Option<u16>,
    pub dst_port: Option<u16>,
    pub protocol: Option<TransportProtocol>,
    pub min_size: Option<usize>,
    pub max_size: Option<usize>,
    pub bpf_filter: Option<String>,  // Berkeley Packet Filter expression
}

impl PacketFilter {
    /// Creates a new empty packet filter
    pub fn new() -> Self {
        PacketFilter {
            src_ip: None,
            dst_ip: None,
            src_port: None,
            dst_port: None,
            protocol: None,
            min_size: None,
            max_size: None,
            bpf_filter: None,
        }
    }
    
    /// Checks if a packet matches this filter
    pub fn matches(&self, packet: &NetworkPacket) -> bool {
        // Source IP check
        if let Some(src_ip) = self.src_ip {
            if let Some(packet_src_ip) = packet.src_ip {
                if packet_src_ip != src_ip {
                    return false;
                }
            } else {
                return false;
            }
        }
        
        // Destination IP check
        if let Some(dst_ip) = self.dst_ip {
            if let Some(packet_dst_ip) = packet.dst_ip {
                if packet_dst_ip != dst_ip {
                    return false;
                }
            } else {
                return false;
            }
        }
        
        // Source port check
        if let Some(src_port) = self.src_port {
            if let Some(packet_src_port) = packet.src_port {
                if packet_src_port != src_port {
                    return false;
                }
            } else {
                return false;
            }
        }
        
        // Destination port check
        if let Some(dst_port) = self.dst_port {
            if let Some(packet_dst_port) = packet.dst_port {
                if packet_dst_port != dst_port {
                    return false;
                }
            } else {
                return false;
            }
        }
        
        // Protocol check
        if let Some(protocol) = self.protocol {
            if packet.protocol != protocol {
                return false;
            }
        }
        
        // Size checks
        if let Some(min_size) = self.min_size {
            if packet.length < min_size {
                return false;
            }
        }
        
        if let Some(max_size) = self.max_size {
            if packet.length > max_size {
                return false;
            }
        }
        
        // All checks passed, packet matches filter
        true
    }
    
    /// Converts this filter to a BPF expression
    pub fn to_bpf_expression(&self) -> String {
        let mut expressions = Vec::new();
        
        if let Some(src_ip) = self.src_ip {
            expressions.push(format!("src host {}", src_ip));
        }
        
        if let Some(dst_ip) = self.dst_ip {
            expressions.push(format!("dst host {}", dst_ip));
        }
        
        if let Some(src_port) = self.src_port {
            expressions.push(format!("src port {}", src_port));
        }
        
        if let Some(dst_port) = self.dst_port {
            expressions.push(format!("dst port {}", dst_port));
        }
        
        if let Some(protocol) = self.protocol {
            let proto_str = match protocol {
                TransportProtocol::TCP => "tcp",
                TransportProtocol::UDP => "udp",
                TransportProtocol::ICMP => "icmp",
                TransportProtocol::IGMP => "igmp",
                _ => "",
            };
            
            if !proto_str.is_empty() {
                expressions.push(proto_str.to_string());
            }
        }
        
        if let Some(ref bpf) = self.bpf_filter {
            expressions.push(bpf.clone());
        }
        
        if expressions.is_empty() {
            return "".to_string();
        }
        
        expressions.join(" and ")
    }
}

/// Configuration for a packet capture session
#[derive(Debug, Clone)]
pub struct CaptureConfig {
    pub interface_name: String,
    pub promiscuous_mode: bool,
    pub snapshot_length: i32,
    pub buffer_size: usize,
    pub timeout_ms: i32,
    pub filter: PacketFilter,
}

impl Default for CaptureConfig {
    fn default() -> Self {
        CaptureConfig {
            interface_name: "".to_string(),
            promiscuous_mode: true,
            snapshot_length: 65535,
            buffer_size: 1_000_000,
            timeout_ms: 1000,
            filter: PacketFilter::new(),
        }
    }
}

/// Handler for processing captured packets
pub type PacketHandler = Box<dyn Fn(&NetworkPacket) + Send + Sync>;

/// Main packet capture engine
pub struct PacketCapture {
    config: CaptureConfig,
    stats: Arc<Mutex<CaptureStats>>,
    handlers: Vec<PacketHandler>,
    running: Arc<Mutex<bool>>,
    local_ips: Arc<Vec<IpAddr>>,
}

impl PacketCapture {
    /// Creates a new packet capture engine with the specified configuration
    pub fn new(config: CaptureConfig) -> Result<Self, String> {
        // Validate the interface exists
        let interfaces = Self::list_interfaces()
            .map_err(|e| format!("Failed to list interfaces: {}", e))?;
            
        if !interfaces.iter().any(|i| i.name == config.interface_name) {
            return Err(format!("Interface {} not found", config.interface_name));
        }
        
        // Get local IPs for this interface
        let local_ips = interfaces.iter()
            .find(|i| i.name == config.interface_name)
            .map(|i| i.ip_addresses.clone())
            .unwrap_or_default();
        
        Ok(PacketCapture {
            config,
            stats: Arc::new(Mutex::new(CaptureStats::new())),
            handlers: Vec::new(),
            running: Arc::new(Mutex::new(false)),
            local_ips: Arc::new(local_ips),
        })
    }
    
    /// Lists available network interfaces
    pub fn list_interfaces() -> Result<Vec<NetworkInterface>, String> {
        let devices = Device::list()
            .map_err(|e| format!("Failed to list network devices: {}", e))?;
            
        let mut interfaces = Vec::new();
        
        for device in devices {
            let interface = NetworkInterface {
                name: device.name,
                description: device.desc,
                mac_address: None, // pcap doesn't provide MAC address directly
                ip_addresses: device.addresses.iter()
                    .filter_map(|addr| {
                        match addr.addr {
                            pcap::Address::Ipv4(addr) => Some(IpAddr::V4(Ipv4Addr::from(addr.addr()))),
                            pcap::Address::Ipv6(addr) => Some(IpAddr::V6(Ipv6Addr::from(addr.addr()))),
                            _ => None,
                        }
                    })
                    .collect(),
                is_up: true, // pcap doesn't provide interface status directly
                is_loopback: device.flags & 1 != 0, // PCAP_IF_LOOPBACK flag
            };
            
            interfaces.push(interface);
        }
        
        Ok(interfaces)
    }
    
    /// Adds a packet handler function
    pub fn add_handler<F>(&mut self, handler: F)
    where
        F: Fn(&NetworkPacket) + Send + Sync + 'static,
    {
        self.handlers.push(Box::new(handler));
    }
    
    /// Starts packet capture in a background thread
    pub fn start_capture(&self) -> Result<thread::JoinHandle<()>, String> {
        // Check if already running
        {
            let mut running = self.running.lock().unwrap();
            if *running {
                return Err("Packet capture already running".to_string());
            }
            *running = true;
        }
        
        // Clone resources for the capture thread
        let config = self.config.clone();
        let stats = self.stats.clone();
        let handlers = self.handlers.clone();
        let running = self.running.clone();
        let local_ips = self.local_ips.clone();
        
        // Start capture in a new thread
        let handle = thread::spawn(move || {
            // Open the capture device
            let mut cap = match Capture::from_device(config.interface_name.as_str())
                .map_err(|e| format!("Failed to open device: {}", e))
                .and_then(|d| {
                    d.promisc(config.promiscuous_mode)
                     .snaplen(config.snapshot_length)
                     .buffer_size(config.buffer_size as i32)
                     .timeout(config.timeout_ms)
                     .open()
                     .map_err(|e| format!("Failed to open capture: {}", e))
                }) {
                    Ok(cap) => cap,
                    Err(e) => {
                        eprintln!("Error starting packet capture: {}", e);
                        let mut running = running.lock().unwrap();
                        *running = false;
                        return;
                    }
                };
                
            // Apply BPF filter if specified
            let bpf_expr = config.filter.to_bpf_expression();
            if !bpf_expr.is_empty() {
                if let Err(e) = cap.filter(&bpf_expr) {
                    eprintln!("Error applying BPF filter '{}': {}", bpf_expr, e);
                    let mut running = running.lock().unwrap();
                    *running = false;
                    return;
                }
            }
            
            println!("Packet capture started on {} with filter: {}", 
                config.interface_name, 
                if bpf_expr.is_empty() { "none" } else { &bpf_expr });
                
            // Main capture loop
            while *running.lock().unwrap() {
                // Try to capture a packet
                match cap.next_packet() {
                    Ok(packet) => {
                        // Update statistics
                        {
                            let mut stats_guard = stats.lock().unwrap();
                            stats_guard.packets_captured += 1;
                            stats_guard.bytes_captured += packet.header.len as u64;
                            stats_guard.update_running_time();
                        }
                        
                        // Parse the packet
                        if let Some(network_packet) = Self::parse_packet(
                            &config.interface_name, 
                            &packet.data, 
                            &local_ips
                        ) {
                            // Apply in-memory filter (in addition to BPF)
                            if config.filter.matches(&network_packet) {
                                // Call all packet handlers
                                for handler in &handlers {
                                    handler(&network_packet);
                                }
                            }
                        }
                    },
                    Err(pcap::Error::TimeoutExpired) => {
                        // Timeout is normal, just continue
                        continue;
                    },
                    Err(e) => {
                        eprintln!("Error capturing packet: {}", e);
                        
                        // Check if we should exit on error
                        if !*running.lock().unwrap() {
                            break;
                        }
                    }
                }
            }
            
            println!("Packet capture stopped");
            let mut running = running.lock().unwrap();
            *running = false;
        });
        
        Ok(handle)
    }
    
    /// Stops packet capture
    pub fn stop_capture(&self) {
        let mut running = self.running.lock().unwrap();
        *running = false;
    }
    
    /// Gets current capture statistics
    pub fn get_stats(&self) -> CaptureStats {
        self.stats.lock().unwrap().clone()
    }
    
    /// Parse a raw packet into a NetworkPacket struct
    fn parse_packet(
        interface_name: &str, 
        packet_data: &[u8],
        local_ips: &[IpAddr]
    ) -> Option<NetworkPacket> {
        // Try to parse as Ethernet
        if let Some(eth) = EthernetPacket::new(packet_data) {
            let mut packet = NetworkPacket::new(
                interface_name.to_string(),
                packet_data.to_vec()
            );
            
            // Set Ethernet fields
            let src_mac = eth.get_source();
            let dst_mac = eth.get_destination();
            packet.src_mac = Some([
                src_mac[0], src_mac[1], src_mac[2],
                src_mac[3], src_mac[4], src_mac[5],
            ]);
            packet.dst_mac = Some([
                dst_mac[0], dst_mac[1], dst_mac[2],
                dst_mac[3], dst_mac[4], dst_mac[5],
            ]);
            
            // Parse next layer based on EtherType
            match eth.get_ethertype() {
                EtherTypes::Ipv4 => {
                    // Parse IPv4
                    if let Some(ipv4) = Ipv4Packet::new(eth.payload()) {
                        packet.src_ip = Some(IpAddr::V4(ipv4.get_source()));
                        packet.dst_ip = Some(IpAddr::V4(ipv4.get_destination()));
                        
                        // Determine packet direction based on source/dest IPs
                        if local_ips.contains(&packet.src_ip.unwrap()) {
                            packet.direction = PacketDirection::Outbound;
                        } else if local_ips.contains(&packet.dst_ip.unwrap()) {
                            packet.direction = PacketDirection::Inbound;
                        }
                        
                        // Parse next layer based on protocol
                        Self::parse_transport_layer(&mut packet, ipv4.get_next_level_protocol(), ipv4.payload());
                    }
                },
                EtherTypes::Ipv6 => {
                    // Parse IPv6
                    if let Some(ipv6) = Ipv6Packet::new(eth.payload()) {
                        packet.src_ip = Some(IpAddr::V6(ipv6.get_source()));
                        packet.dst_ip = Some(IpAddr::V6(ipv6.get_destination()));
                        
                        // Determine packet direction based on source/dest IPs
                        if local_ips.contains(&packet.src_ip.unwrap()) {
                            packet.direction = PacketDirection::Outbound;
                        } else if local_ips.contains(&packet.dst_ip.unwrap()) {
                            packet.direction = PacketDirection::Inbound;
                        }
                        
                        // Parse next layer based on protocol
                        Self::parse_transport_layer(&mut packet, ipv6.get_next_header(), ipv6.payload());
                    }
                },
                EtherTypes::Arp => {
                    // ARP packet
                    packet.protocol = TransportProtocol::Other(0x0806);
                },
                _ => {
                    // Other EtherType
                    packet.protocol = TransportProtocol::Other(0);
                }
            }
            
            return Some(packet);
        }
        
        None
    }
    
    /// Parse transport layer protocols (TCP, UDP, etc.)
    fn parse_transport_layer(
        packet: &mut NetworkPacket, 
        protocol: IpNextHeaderProtocol, 
        payload: &[u8]
    ) {
        match protocol {
            IpNextHeaderProtocols::Tcp => {
                if let Some(tcp) = TcpPacket::new(payload) {
                    packet.protocol = TransportProtocol::TCP;
                    packet.src_port = Some(tcp.get_source());
                    packet.dst_port = Some(tcp.get_destination());
                    packet.tcp_flags = Some(
                        ((tcp.get_flags() as u8) & 0xFF)
                    );
                }
            },
            IpNextHeaderProtocols::Udp => {
                if let Some(udp) = UdpPacket::new(payload) {
                    packet.protocol = TransportProtocol::UDP;
                    packet.src_port = Some(udp.get_source());
                    packet.dst_port = Some(udp.get_destination());
                }
            },
            IpNextHeaderProtocols::Icmp => {
                if let Some(_icmp) = IcmpPacket::new(payload) {
                    packet.protocol = TransportProtocol::ICMP;
                }
            },
            IpNextHeaderProtocols::Igmp => {
                packet.protocol = TransportProtocol::IGMP;
            },
            _ => {
                packet.protocol = TransportProtocol::Other(protocol.0);
            }
        }
    }
}
```

## Using Our Packet Capture System

Now let's create a simple example that demonstrates how to use our packet capture system:

```rust
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;
use std::collections::HashMap;
use chrono::{DateTime, Local};

fn main() -> Result<(), String> {
    // List available interfaces
    println!("Available network interfaces:");
    let interfaces = PacketCapture::list_interfaces()?;
    for (i, interface) in interfaces.iter().enumerate() {
        let ips: Vec<String> = interface.ip_addresses.iter()
            .map(|ip| ip.to_string())
            .collect();
            
        println!("[{}] {} ({}) - IPs: {}", 
            i, 
            interface.name, 
            interface.description.as_deref().unwrap_or("No description"),
            if ips.is_empty() { "None".to_string() } else { ips.join(", ") }
        );
    }
    
    // Select an interface (for demo purposes, use the first non-loopback interface)
    let interface = interfaces.iter()
        .find(|i| !i.is_loopback && !i.ip_addresses.is_empty())
        .ok_or("No suitable interface found")?;
        
    println!("\nUsing interface: {}", interface.name);
    
    // Create configuration
    let config = CaptureConfig {
        interface_name: interface.name.clone(),
        promiscuous_mode: true,
        snapshot_length: 65535,
        buffer_size: 1_000_000,
        timeout_ms: 100,
        filter: PacketFilter {
            protocol: None,
            min_size: Some(40),  // Skip very small packets
            ..Default::default()
        },
    };
    
    // Create packet capture
    let mut capture = PacketCapture::new(config)?;
    
    // Create shared stats for handlers to update
    let protocol_counts = Arc::new(Mutex::new(HashMap::<String, u64>::new()));
    let protocol_counts_clone = protocol_counts.clone();
    
    // Add a simple packet handler
    capture.add_handler(move |packet| {
        let now: DateTime<Local> = Local::now();
        
        // Get protocol name
        let proto_name = match packet.protocol {
            TransportProtocol::TCP => "TCP",
            TransportProtocol::UDP => "UDP",
            TransportProtocol::ICMP => "ICMP",
            TransportProtocol::IGMP => "IGMP",
            TransportProtocol::SCTP => "SCTP",
            TransportProtocol::Other(p) => {
                if p == 0x0806 { "ARP" } else { "Other" }
            }
        };
        
        // Update protocol statistics
        {
            let mut counts = protocol_counts_clone.lock().unwrap();
            *counts.entry(proto_name.to_string()).or_insert(0) += 1;
        }
        
        // Print packet information
        println!("[{}] {} {}", 
            now.format("%H:%M:%S"),
            packet.summary(),
            if packet.is_tcp() {
                let flags = packet.tcp_flags.unwrap_or(0);
                format!("Flags: {}{}{}{}{}{}", 
                    if flags & 0x02 > 0 { "S" } else { "" },
                    if flags & 0x01 > 0 { "F" } else { "" },
                    if flags & 0x04 > 0 { "R" } else { "" },
                    if flags & 0x08 > 0 { "P" } else { "" },
                    if flags & 0x10 > 0 { "A" } else { "" },
                    if flags & 0x20 > 0 { "U" } else { "" },
                )
            } else {
                String::new()
            }
        );
    });
    
    // Start capture in background
    let handle = capture.start_capture()?;
    
    // Run for a short period as a demonstration
    println!("\nStarted packet capture. Running for 30 seconds...\n");
    
    // Show statistics periodically
    for _ in 0..6 {
        thread::sleep(Duration::from_secs(5));
        
        let stats = capture.get_stats();
        println!("\n--- Capture Stats ---");
        println!("Packets: {} ({:.1} packets/sec)", 
            stats.packets_captured,
            stats.packets_per_second()
        );
        println!("Data: {:.2} MB ({:.2} MB/sec)", 
            stats.bytes_captured as f64 / 1_000_000.0,
            stats.bytes_per_second() / 1_000_000.0
        );
        
        // Show protocol statistics
        println!("Protocol breakdown:");
        let counts = protocol_counts.lock().unwrap();
        let total_packets: u64 = counts.values().sum();
        
        for (proto, count) in counts.iter() {
            let percentage = if total_packets > 0 {
                (*count as f64 / total_packets as f64) * 100.0
            } else {
                0.0
            };
            
            println!("  - {}: {} ({:.1}%)", proto, count, percentage);
        }
    }
    
    // Stop capture
    capture.stop_capture();
    handle.join().map_err(|_| "Failed to join capture thread".to_string())?;
    
    println!("\nPacket capture completed.");
    
    Ok(())
}
```

## Building a Packet Processing Pipeline

In real applications, we want to process packets through a sequence of operations. Let's implement a packet processing pipeline:

```rust
use std::collections::VecDeque;
use std::sync::mpsc::{channel, Sender, Receiver};

/// Represents a stage in the packet processing pipeline
pub trait PacketProcessor: Send {
    /// Process a packet and return the packet for further processing
    /// or None if the packet should be dropped
    fn process(&mut self, packet: NetworkPacket) -> Option<NetworkPacket>;
    
    /// Name of this processor for identification
    fn name(&self) -> &str;
}

/// A pipeline of packet processors that process packets in sequence
pub struct PacketPipeline {
    name: String,
    processors: Vec<Box<dyn PacketProcessor>>,
    input_tx: Option<Sender<NetworkPacket>>,
    output_rx: Option<Receiver<NetworkPacket>>,
    running: bool,
    stats: HashMap<String, u64>,
}

impl PacketPipeline {
    /// Create a new packet processing pipeline
    pub fn new(name: &str) -> Self {
        PacketPipeline {
            name: name.to_string(),
            processors: Vec::new(),
            input_tx: None,
            output_rx: None,
            running: false,
            stats: HashMap::new(),
        }
    }
    
    /// Add a processor to the pipeline
    pub fn add_processor<P: PacketProcessor + 'static>(&mut self, processor: P) {
        self.processors.push(Box::new(processor));
    }
    
    /// Start the pipeline processing
    pub fn start(&mut self) -> Result<(Sender<NetworkPacket>, Receiver<NetworkPacket>), String> {
        if self.running {
            return Err("Pipeline already running".to_string());
        }
        
        // Create channels for input and final output
        let (input_tx, input_rx) = channel();
        let (output_tx, output_rx) = channel();
        
        // Create channels between each stage
        let mut senders = Vec::new();
        let mut receivers = Vec::new();
        
        // Create n-1 channels for n processors
        for _ in 0..self.processors.len() - 1 {
            let (tx, rx) = channel();
            senders.push(tx);
            receivers.push(rx);
        }
        
        // Combine input, intermediate channels, and output
        let mut rxs = vec![input_rx];
        rxs.extend(receivers);
        
        let mut txs = senders;
        txs.push(output_tx);
        
        // For each processor, spawn a thread to handle its stage
        for (i, processor) in self.processors.iter_mut().enumerate() {
            let rx = rxs[i].clone();
            let tx = txs[i].clone();
            let processor_name = processor.name().to_string();
            
            thread::spawn(move || {
                let mut processor = processor;
                let processor_name = processor_name.clone();
                
                // Process packets from input channel
                while let Ok(packet) = rx.recv() {
                    // Process the packet
                    if let Some(processed_packet) = processor.process(packet) {
                        // Send to next stage or output
                        if tx.send(processed_packet).is_err() {
                            eprintln!("Pipeline stage '{}' failed to send packet", processor_name);
                            break;
                        }
                    }
                }
                
                println!("Pipeline stage '{}' shutting down", processor_name);
            });
        }
        
        self.input_tx = Some(input_tx.clone());
        self.output_rx = Some(output_rx.clone());
        self.running = true;
        
        Ok((input_tx, output_rx))
    }
    
    /// Stop the pipeline
    pub fn stop(&mut self) {
        // Drop the channel sender which will cause receivers to get errors and exit
        self.input_tx = None;
        self.output_rx = None;
        self.running = false;
        
        // Wait for clean shutdown
        thread::sleep(Duration::from_millis(100));
    }
    
    /// Update statistics for this pipeline
    pub fn update_stats(&mut self, key: &str, value: u64) {
        *self.stats.entry(key.to_string()).or_insert(0) += value;
    }
    
    /// Get pipeline statistics
    pub fn get_stats(&self) -> &HashMap<String, u64> {
        &self.stats
    }
}

/// Example packet processors

// An IP deduplication processor that removes duplicate packets
pub struct DuplicateFilter {
    recent_packets: VecDeque<u64>, // Store packet hashes
    capacity: usize,
}

impl DuplicateFilter {
    pub fn new(capacity: usize) -> Self {
        DuplicateFilter {
            recent_packets: VecDeque::with_capacity(capacity),
            capacity,
        }
    }
    
    fn hash_packet(&self, packet: &NetworkPacket) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        
        // Hash the key fields to identify duplicates
        if let Some(src_ip) = packet.src_ip {
            src_ip.hash(&mut hasher);
        }
        
        if let Some(dst_ip) = packet.dst_ip {
            dst_ip.hash(&mut hasher);
        }
        
        if let Some(src_port) = packet.src_port {
            src_port.hash(&mut hasher);
        }
        
        if let Some(dst_port) = packet.dst_port {
            dst_port.hash(&mut hasher);
        }
        
        (packet.protocol as u8).hash(&mut hasher);
        
        // Include a small part of the payload if available
        if packet.data.len() >= 20 {
            for i in 0..20 {
                packet.data[i].hash(&mut hasher);
            }
        }
        
        hasher.finish()
    }
}

impl PacketProcessor for DuplicateFilter {
    fn process(&mut self, packet: NetworkPacket) -> Option<NetworkPacket> {
        let hash = self.hash_packet(&packet);
        
        // Check if this is a duplicate
        if self.recent_packets.contains(&hash) {
            // Duplicate packet, drop it
            return None;
        }
        
        // Add to recent packets
        self.recent_packets.push_back(hash);
        
        // Remove oldest if we're at capacity
        if self.recent_packets.len() > self.capacity {
            self.recent_packets.pop_front();
        }
        
        // Pass the packet through
        Some(packet)
    }
    
    fn name(&self) -> &str {
        "DuplicateFilter"
    }
}

// A TCP flow tracker
pub struct TcpFlowTracker {
    active_flows: HashMap<(IpAddr, u16, IpAddr, u16), TcpFlowState>,
    flow_timeout: Duration,
}

#[derive(Debug, Clone)]
pub struct TcpFlowState {
    pub flow_id: String,
    pub first_seen: SystemTime,
    pub last_seen: SystemTime,
    pub packet_count: u64,
    pub byte_count: u64,
    pub client_packets: u64,
    pub server_packets: u64,
    pub syn_seen: bool,
    pub fin_seen: bool,
    pub rst_seen: bool,
    pub current_state: TcpState,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TcpState {
    SynSent,
    SynReceived,
    Established,
    FinWait1,
    FinWait2,
    CloseWait,
    Closing,
    LastAck,
    TimeWait,
    Closed,
    Unknown,
}

impl TcpFlowTracker {
    pub fn new(flow_timeout: Duration) -> Self {
        TcpFlowTracker {
            active_flows: HashMap::new(),
            flow_timeout,
        }
    }
    
    fn clean_expired_flows(&mut self) {
        let now = SystemTime::now();
        self.active_flows.retain(|_, flow| {
            now.duration_since(flow.last_seen).unwrap_or_default() < self.flow_timeout
        });
    }
}

impl PacketProcessor for TcpFlowTracker {
    fn process(&mut self, packet: NetworkPacket) -> Option<NetworkPacket> {
        // Only process TCP packets
        if !packet.is_tcp() || packet.src_ip.is_none() || packet.dst_ip.is_none() || 
           packet.src_port.is_none() || packet.dst_port.is_none() {
            return Some(packet);
        }
        
        let src_ip = packet.src_ip.unwrap();
        let dst_ip = packet.dst_ip.unwrap();
        let src_port = packet.src_port.unwrap();
        let dst_port = packet.dst_port.unwrap();
        let tcp_flags = packet.tcp_flags.unwrap_or(0);
        
        // Clean expired flows periodically
        self.clean_expired_flows();
        
        // Create or update flow
        let flow_key = (src_ip, src_port, dst_ip, dst_port);
        let reverse_key = (dst_ip, dst_port, src_ip, src_port);
        
        let now = SystemTime::now();
        
        if self.active_flows.contains_key(&flow_key) {
            // Update existing flow
            let flow = self.active_flows.get_mut(&flow_key).unwrap();
            flow.last_seen = now;
            flow.packet_count += 1;
            flow.byte_count += packet.length as u64;
            flow.client_packets += 1;
            
            // Update TCP state
            if tcp_flags & 0x02 > 0 {
                // SYN flag
                flow.syn_seen = true;
                flow.current_state = TcpState::SynSent;
            } else if tcp_flags & 0x01 > 0 {
                // FIN flag
                flow.fin_seen = true;
                flow.current_state = match flow.current_state {
                    TcpState::Established => TcpState::FinWait1,
                    TcpState::CloseWait => TcpState::LastAck,
                    _ => flow.current_state,
                };
            } else if tcp_flags & 0x04 > 0 {
                // RST flag
                flow.rst_seen = true;
                flow.current_state = TcpState::Closed;
            } else if tcp_flags & 0x10 > 0 && flow.current_state == TcpState::SynSent {
                // ACK flag after SYN
                flow.current_state = TcpState::Established;
            }
        } else if self.active_flows.contains_key(&reverse_key) {
            // This is the other direction of an existing flow
            let flow = self.active_flows.get_mut(&reverse_key).unwrap();
            flow.last_seen = now;
            flow.packet_count += 1;
            flow.byte_count += packet.length as u64;
            flow.server_packets += 1;
            
            // Update TCP state for reverse direction
            if tcp_flags & 0x02 > 0 && tcp_flags & 0x10 > 0 {
                // SYN+ACK flags
                flow.current_state = TcpState::SynReceived;
            } else if tcp_flags & 0x01 > 0 {
                // FIN flag
                flow.fin_seen = true;
                flow.current_state = match flow.current_state {
                    TcpState::Established => TcpState::CloseWait,
                    TcpState::FinWait1 => TcpState::Closing,
                    TcpState::FinWait2 => TcpState::TimeWait,
                    _ => flow.current_state,
                };
            } else if tcp_flags & 0x04 > 0 {
                // RST flag
                flow.rst_seen = true;
                flow.current_state = TcpState::Closed;
            }
        } else {
            // New flow
            let flow_id = format!("{}:{}-{}:{}", 
                src_ip, src_port, dst_ip, dst_port);
                
            let initial_state = if tcp_flags & 0x02 > 0 {
                TcpState::SynSent // SYN flag
            } else {
                TcpState::Unknown // Mid-stream pickup
            };
            
            let flow = TcpFlowState {
                flow_id,
                first_seen: now,
                last_seen: now,
                packet_count: 1,
                byte_count: packet.length as u64,
                client_packets: 1,
                server_packets: 0,
                syn_seen: tcp_flags & 0x02 > 0,
                fin_seen: tcp_flags & 0x01 > 0,
                rst_seen: tcp_flags & 0x04 > 0,
                current_state: initial_state,
            };
            
            self.active_flows.insert(flow_key, flow);
        }
        
        // Pass the packet through
        Some(packet)
    }
    
    fn name(&self) -> &str {
        "TcpFlowTracker"
    }
}
```

## Integrating Packet Capture with a Processing Pipeline

Now we can combine our packet capture system with the processing pipeline:

```rust
fn main() -> Result<(), String> {
    // ... (interface selection code from previous example)
    
    // Create and start packet capture
    let mut capture = PacketCapture::new(config)?;
    
    // Create processing pipeline
    let mut pipeline = PacketPipeline::new("Main Pipeline");
    pipeline.add_processor(DuplicateFilter::new(1000));
    pipeline.add_processor(TcpFlowTracker::new(Duration::from_secs(120)));
    
    // Start the pipeline
    let (pipeline_tx, pipeline_rx) = pipeline.start()?;
    
    // Add a handler to feed packets into the pipeline
    let pipeline_tx_clone = pipeline_tx.clone();
    capture.add_handler(move |packet| {
        let _ = pipeline_tx_clone.send(packet.clone());
    });
    
    // Start packet capture
    let capture_handle = capture.start_capture()?;
    
    // Process packets coming out of the pipeline
    thread::spawn(move || {
        let mut tcp_flow_count = 0;
        
        while let Ok(packet) = pipeline_rx.recv() {
            // Process fully-analyzed packets
            if packet.is_tcp() {
                tcp_flow_count += 1;
                
                // Print periodic stats
                if tcp_flow_count % 100 == 0 {
                    println!("Processed {} TCP flows", tcp_flow_count);
                }
            }
        }
    });
    
    // Run for a demonstration period
    println!("Running packet capture and processing for 60 seconds...");
    thread::sleep(Duration::from_secs(60));
    
    // Cleanup
    capture.stop_capture();
    pipeline.stop();
    capture_handle.join().map_err(|_| "Failed to join capture thread".to_string())?;
    
    println!("Packet capture and processing completed.");
    Ok(())
}
```

## Handling High-Volume Traffic

One of the key challenges in packet capture is dealing with high-volume traffic. Here are some techniques we've incorporated:

1. **Efficient parsing with zero-copy**: Using libraries like `pnet` that provide zero-copy parsing of packets.
2. **Pipeline processing**: Breaking packet processing into stages that can run concurrently.
3. **BPF filters**: Applying kernel-level filters to reduce the number of packets that reach our application.
4. **Buffering**: Using large buffers to handle traffic bursts.
5. **Thread pools**: Processing packets in parallel for better throughput.

## Conclusion

In this section, we've built a robust packet capture system in Rust that can:

1. Capture packets from network interfaces
2. Parse Ethernet, IP, TCP, UDP and other protocols
3. Filter packets based on various criteria
4. Process packets through a flexible pipeline
5. Track TCP connections and maintain flow state
6. Handle high-volume traffic efficiently

These components form the foundation for our network security tools. In the next section, we'll build on this foundation to create more advanced traffic analysis capabilities.

---

🔗 **Previous**: [01-understanding-network-security.md](./01-understanding-network-security.md)

🔗 **Next**: [03-traffic-analysis.md](./03-traffic-analysis.md)
