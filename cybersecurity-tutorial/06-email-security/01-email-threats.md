# Email Threats and Attack Vectors

Email is a primary vector for cyberattacks, including phishing, malware, and business email compromise. In this section, we'll cover common email threats and how to recognize them.

## Overview

We'll cover:

1. Common email threats (phishing, malware, spam, BEC)
2. Anatomy of a phishing email
3. Social engineering tactics
4. Indicators of compromise (IoCs)
5. Email threat trends and statistics

## Common Email Threats

- **Phishing**: Deceptive emails to steal credentials or deliver malware
- **Malware**: Malicious attachments or links
- **Spam**: Unsolicited bulk email
- **Business Email Compromise (BEC)**: Impersonation of executives or partners

## Anatomy of a Phishing Email

- Spoofed sender address
- Urgent or threatening language
- Suspicious links or attachments
- Requests for sensitive information

## Social Engineering Tactics

- Pretexting: Creating a fabricated scenario
- Baiting: Offering something enticing
- Tailgating: Using urgency or authority

## Indicators of Compromise (IoCs)

- Unusual sender domains
- Misspelled URLs
- Unexpected attachments
- Requests for wire transfers or credentials

## Email Threat Trends

- Phishing remains the top attack vector
- Ransomware often delivered via email
- Increasing use of AI-generated phishing

## Conclusion

Understanding email threats is the first step in defending against them. In the next section, we'll implement email security controls in Rust.

---

🔗 **Previous**: [00-overview.md](./00-overview.md)

🔗 **Next**: [02-email-security-controls.md](./02-email-security-controls.md)
