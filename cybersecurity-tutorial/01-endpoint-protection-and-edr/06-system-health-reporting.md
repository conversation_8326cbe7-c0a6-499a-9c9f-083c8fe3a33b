# System Health Reporting

In this section, we'll implement a system health reporting module for our CyberShield application. This module will collect and report various system metrics to help identify potential security issues and performance bottlenecks.

## Table of Contents
- [Introduction to System Health Monitoring](#introduction-to-system-health-monitoring)
- [Implementing the System Health Plugin](#implementing-the-system-health-plugin)
- [Collecting System Metrics](#collecting-system-metrics)
- [Generating Health Reports](#generating-health-reports)
- [Alerting on Abnormal Metrics](#alerting-on-abnormal-metrics)
- [Visualization of System Health Data](#visualization-of-system-health-data)
- [Next Steps](#next-steps)

## Introduction to System Health Monitoring

System health monitoring is a critical component of any endpoint protection solution. By continuously monitoring system resources and performance metrics, we can:

1. Detect potential security incidents through anomalous resource usage
2. Identify performance bottlenecks that might affect security controls
3. Track resource utilization trends over time
4. Provide data for incident response and forensics

Our system health reporting module will monitor several key metrics:
- CPU usage (overall and per-process)
- Memory usage (overall and per-process)
- Disk activity and available space
- Network bandwidth utilization
- Running services and their states
- System uptime and stability

## Implementing the System Health Plugin

First, let's create a new system health plugin that implements our plugin trait:

```rust
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use sysinfo::{System, SystemExt, ProcessExt, DiskExt, NetworkExt};
use anyhow::{Result, Context};
use crossbeam_channel::Sender;
use crate::plugin::{Plugin, PluginEvent};

pub struct SystemHealthPlugin {
    name: String,
    description: String,
    enabled: Arc<RwLock<bool>>,
    system: System,
    event_sender: Sender<PluginEvent>,
    last_report: Instant,
    report_interval: Duration,
}

impl SystemHealthPlugin {
    pub fn new(event_sender: Sender<PluginEvent>) -> Self {
        Self {
            name: "System Health Reporter".to_string(),
            description: "Monitors and reports system health metrics".to_string(),
            enabled: Arc::new(RwLock::new(true)),
            system: System::new_all(),
            event_sender,
            last_report: Instant::now(),
            report_interval: Duration::from_secs(300), // Report every 5 minutes by default
        }
    }
    
    pub fn set_report_interval(&mut self, seconds: u64) {
        self.report_interval = Duration::from_secs(seconds);
    }
}

impl Plugin for SystemHealthPlugin {
    fn name(&self) -> &str {
        &self.name
    }
    
    fn description(&self) -> &str {
        &self.description
    }
    
    fn is_enabled(&self) -> bool {
        *self.enabled.read().unwrap()
    }
    
    fn enable(&self) -> Result<()> {
        let mut enabled = self.enabled.write().unwrap();
        *enabled = true;
        Ok(())
    }
    
    fn disable(&self) -> Result<()> {
        let mut enabled = self.enabled.write().unwrap();
        *enabled = false;
        Ok(())
    }
    
    fn start(&mut self) -> Result<()> {
        println!("Starting System Health Reporter plugin");
        
        // Initialize system info
        self.system.refresh_all();
        self.last_report = Instant::now();
        
        Ok(())
    }
    
    fn stop(&mut self) -> Result<()> {
        println!("Stopping System Health Reporter plugin");
        Ok(())
    }
    
    fn handle_event(&mut self) -> Result<()> {
        // Only generate reports at the specified interval
        if self.last_report.elapsed() >= self.report_interval && self.is_enabled() {
            self.generate_health_report()?;
            self.last_report = Instant::now();
        }
        
        Ok(())
    }
}
```

## Collecting System Metrics

Now, let's implement the functionality to collect various system metrics:

```rust
impl SystemHealthPlugin {
    // ... existing implementation ...
    
    fn collect_cpu_metrics(&mut self) -> Result<CpuMetrics> {
        self.system.refresh_cpu();
        
        let global_cpu_usage = self.system.global_cpu_info().cpu_usage();
        let cpu_count = self.system.cpus().len();
        let per_cpu_usage: Vec<f32> = self.system.cpus().iter()
            .map(|cpu| cpu.cpu_usage())
            .collect();
        
        Ok(CpuMetrics {
            global_usage: global_cpu_usage,
            cpu_count,
            per_cpu_usage,
        })
    }
    
    fn collect_memory_metrics(&mut self) -> Result<MemoryMetrics> {
        self.system.refresh_memory();
        
        Ok(MemoryMetrics {
            total_memory: self.system.total_memory(),
            used_memory: self.system.used_memory(),
            total_swap: self.system.total_swap(),
            used_swap: self.system.used_swap(),
        })
    }
    
    fn collect_disk_metrics(&mut self) -> Result<Vec<DiskMetrics>> {
        self.system.refresh_disks();
        
        let disk_metrics: Vec<DiskMetrics> = self.system.disks().iter()
            .map(|disk| DiskMetrics {
                name: disk.name().to_string_lossy().into_owned(),
                mount_point: disk.mount_point().to_string_lossy().into_owned(),
                total_space: disk.total_space(),
                available_space: disk.available_space(),
                disk_type: format!("{:?}", disk.type_()),
            })
            .collect();
        
        Ok(disk_metrics)
    }
    
    fn collect_network_metrics(&mut self) -> Result<Vec<NetworkMetrics>> {
        self.system.refresh_networks();
        
        let network_metrics: Vec<NetworkMetrics> = self.system.networks().iter()
            .map(|(interface, data)| NetworkMetrics {
                interface: interface.clone(),
                received_bytes: data.received(),
                transmitted_bytes: data.transmitted(),
                packets_received: data.packets_received(),
                packets_transmitted: data.packets_transmitted(),
            })
            .collect();
        
        Ok(network_metrics)
    }
    
    fn collect_process_metrics(&mut self, top_count: usize) -> Result<Vec<ProcessMetrics>> {
        self.system.refresh_processes();
        
        // Get processes sorted by CPU usage
        let mut processes: Vec<ProcessMetrics> = self.system.processes().iter()
            .map(|(pid, process)| ProcessMetrics {
                pid: *pid,
                name: process.name().to_string(),
                cpu_usage: process.cpu_usage(),
                memory_usage: process.memory(),
                disk_usage: process.disk_usage().total_written_bytes,
                start_time: process.start_time(),
            })
            .collect();
        
        // Sort by CPU usage (descending)
        processes.sort_by(|a, b| b.cpu_usage.partial_cmp(&a.cpu_usage).unwrap());
        
        // Return top N processes
        Ok(processes.into_iter().take(top_count).collect())
    }
}

// Define the metric structs
#[derive(Debug, Clone)]
struct CpuMetrics {
    global_usage: f32,
    cpu_count: usize,
    per_cpu_usage: Vec<f32>,
}

#[derive(Debug, Clone)]
struct MemoryMetrics {
    total_memory: u64,
    used_memory: u64,
    total_swap: u64,
    used_swap: u64,
}

#[derive(Debug, Clone)]
struct DiskMetrics {
    name: String,
    mount_point: String,
    total_space: u64,
    available_space: u64,
    disk_type: String,
}

#[derive(Debug, Clone)]
struct NetworkMetrics {
    interface: String,
    received_bytes: u64,
    transmitted_bytes: u64,
    packets_received: u64,
    packets_transmitted: u64,
}

#[derive(Debug, Clone)]
struct ProcessMetrics {
    pid: sysinfo::Pid,
    name: String,
    cpu_usage: f32,
    memory_usage: u64,
    disk_usage: u64,
    start_time: u64,
}
```

## Generating Health Reports

Now, let's implement the functionality to generate comprehensive health reports:

```rust
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use std::fs::File;
use std::io::Write;

impl SystemHealthPlugin {
    // ... existing implementation ...
    
    fn generate_health_report(&mut self) -> Result<()> {
        println!("Generating system health report...");
        
        let report = SystemHealthReport {
            timestamp: Utc::now(),
            cpu: self.collect_cpu_metrics()?,
            memory: self.collect_memory_metrics()?,
            disks: self.collect_disk_metrics()?,
            network: self.collect_network_metrics()?,
            top_processes: self.collect_process_metrics(10)?,
            uptime_seconds: self.system.uptime(),
        };
        
        // Send event with the report summary
        self.event_sender.send(PluginEvent::SystemHealth {
            cpu_usage: report.cpu.global_usage,
            memory_usage_percent: (report.memory.used_memory as f32 / report.memory.total_memory as f32) * 100.0,
            disk_alert: report.disks.iter().any(|d| {
                let used_percent = 100.0 - ((d.available_space as f64 / d.total_space as f64) * 100.0);
                used_percent > 90.0 // Alert if any disk is over 90% full
            }),
        }).context("Failed to send system health event")?;
        
        // Save detailed report to file
        self.save_report_to_file(&report)?;
        
        // Check for abnormal metrics and potentially alert
        self.check_abnormal_metrics(&report)?;
        
        Ok(())
    }
    
    fn save_report_to_file(&self, report: &SystemHealthReport) -> Result<()> {
        let timestamp = report.timestamp.format("%Y%m%d_%H%M%S").to_string();
        let filename = format!("system_health_{}.json", timestamp);
        
        let json = serde_json::to_string_pretty(report)
            .context("Failed to serialize health report")?;
            
        let mut file = File::create(&filename)
            .context(format!("Failed to create report file {}", filename))?;
            
        file.write_all(json.as_bytes())
            .context("Failed to write report to file")?;
            
        println!("Health report saved to {}", filename);
        Ok(())
    }
    
    fn check_abnormal_metrics(&self, report: &SystemHealthReport) -> Result<()> {
        // Check CPU usage (if over 90% sustained)
        if report.cpu.global_usage > 90.0 {
            self.event_sender.send(PluginEvent::Alert {
                severity: "WARNING".to_string(),
                message: format!("High CPU usage detected: {}%", report.cpu.global_usage),
                source: self.name.clone(),
            })?;
        }
        
        // Check memory usage (if over 90%)
        let memory_percent = (report.memory.used_memory as f32 / report.memory.total_memory as f32) * 100.0;
        if memory_percent > 90.0 {
            self.event_sender.send(PluginEvent::Alert {
                severity: "WARNING".to_string(),
                message: format!("High memory usage detected: {:.1}%", memory_percent),
                source: self.name.clone(),
            })?;
        }
        
        // Check disk space (if any disk is over 90% full)
        for disk in &report.disks {
            let used_percent = 100.0 - ((disk.available_space as f64 / disk.total_space as f64) * 100.0);
            if used_percent > 90.0 {
                self.event_sender.send(PluginEvent::Alert {
                    severity: "WARNING".to_string(),
                    message: format!("Low disk space on {}: {:.1}% used", disk.mount_point, used_percent),
                    source: self.name.clone(),
                })?;
            }
        }
        
        Ok(())
    }
}

#[derive(Debug, Serialize, Deserialize)]
struct SystemHealthReport {
    timestamp: DateTime<Utc>,
    cpu: CpuMetrics,
    memory: MemoryMetrics,
    disks: Vec<DiskMetrics>,
    network: Vec<NetworkMetrics>,
    top_processes: Vec<ProcessMetrics>,
    uptime_seconds: u64,
}

// Make the metrics structs serializable
#[derive(Debug, Clone, Serialize, Deserialize)]
struct CpuMetrics {
    // ... same as before ...
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct MemoryMetrics {
    // ... same as before ...
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct DiskMetrics {
    // ... same as before ...
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct NetworkMetrics {
    // ... same as before ...
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct ProcessMetrics {
    pid: u32, // Changed from sysinfo::Pid for serialization
    name: String,
    cpu_usage: f32,
    memory_usage: u64,
    disk_usage: u64,
    start_time: u64,
}
```

## Alerting on Abnormal Metrics

Let's extend our plugin to include a threshold-based alerting system:

```rust
impl SystemHealthPlugin {
    // ... existing implementation ...
    
    pub fn set_thresholds(&mut self, thresholds: SystemHealthThresholds) {
        self.thresholds = thresholds;
    }
    
    fn check_abnormal_metrics(&self, report: &SystemHealthReport) -> Result<()> {
        // Check against configured thresholds
        
        // CPU check
        if report.cpu.global_usage > self.thresholds.cpu_usage_percent {
            self.event_sender.send(PluginEvent::Alert {
                severity: "WARNING".to_string(),
                message: format!("High CPU usage detected: {:.1}% (threshold: {:.1}%)", 
                    report.cpu.global_usage, self.thresholds.cpu_usage_percent),
                source: self.name.clone(),
            })?;
        }
        
        // Memory check
        let memory_percent = (report.memory.used_memory as f32 / report.memory.total_memory as f32) * 100.0;
        if memory_percent > self.thresholds.memory_usage_percent {
            self.event_sender.send(PluginEvent::Alert {
                severity: "WARNING".to_string(),
                message: format!("High memory usage detected: {:.1}% (threshold: {:.1}%)", 
                    memory_percent, self.thresholds.memory_usage_percent),
                source: self.name.clone(),
            })?;
        }
        
        // Disk space check
        for disk in &report.disks {
            let used_percent = 100.0 - ((disk.available_space as f64 / disk.total_space as f64) * 100.0);
            if used_percent > self.thresholds.disk_usage_percent as f64 {
                self.event_sender.send(PluginEvent::Alert {
                    severity: "WARNING".to_string(),
                    message: format!("Low disk space on {}: {:.1}% used (threshold: {:.1}%)", 
                        disk.mount_point, used_percent, self.thresholds.disk_usage_percent),
                    source: self.name.clone(),
                })?;
            }
        }
        
        // Process check - detect any process using excessive resources
        for process in &report.top_processes {
            if process.cpu_usage > self.thresholds.process_cpu_percent {
                self.event_sender.send(PluginEvent::Alert {
                    severity: "INFO".to_string(),
                    message: format!("Process '{}' (PID: {}) using high CPU: {:.1}%", 
                        process.name, process.pid, process.cpu_usage),
                    source: self.name.clone(),
                })?;
            }
            
            let process_memory_percent = (process.memory_usage as f32 / report.memory.total_memory as f32) * 100.0;
            if process_memory_percent > self.thresholds.process_memory_percent {
                self.event_sender.send(PluginEvent::Alert {
                    severity: "INFO".to_string(),
                    message: format!("Process '{}' (PID: {}) using high memory: {:.1}%", 
                        process.name, process.pid, process_memory_percent),
                    source: self.name.clone(),
                })?;
            }
        }
        
        Ok(())
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthThresholds {
    pub cpu_usage_percent: f32,
    pub memory_usage_percent: f32,
    pub disk_usage_percent: f32,
    pub process_cpu_percent: f32,
    pub process_memory_percent: f32,
}

impl Default for SystemHealthThresholds {
    fn default() -> Self {
        Self {
            cpu_usage_percent: 90.0,
            memory_usage_percent: 90.0,
            disk_usage_percent: 90.0,
            process_cpu_percent: 80.0,
            process_memory_percent: 70.0,
        }
    }
}
```

## Visualization of System Health Data

For a complete system health monitoring solution, we'll want to visualize the collected data. While the full implementation of a visualization dashboard is beyond the scope of this tutorial, we'll demonstrate how to export our data in a format that can be easily visualized using common tools:

```rust
impl SystemHealthPlugin {
    // ... existing implementation ...
    
    fn export_metrics_for_visualization(&self, report: &SystemHealthReport) -> Result<()> {
        // Export CPU data as CSV for time-series visualization
        let mut cpu_file = std::fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open("cpu_metrics.csv")
            .context("Failed to open CPU metrics file")?;
            
        let timestamp = report.timestamp.to_rfc3339();
        writeln!(cpu_file, "{},{}", timestamp, report.cpu.global_usage)
            .context("Failed to write CPU metrics")?;
            
        // Export memory data
        let mut memory_file = std::fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open("memory_metrics.csv")
            .context("Failed to open memory metrics file")?;
            
        let memory_percent = (report.memory.used_memory as f32 / report.memory.total_memory as f32) * 100.0;
        writeln!(memory_file, "{},{}", timestamp, memory_percent)
            .context("Failed to write memory metrics")?;
            
        // Export disk data
        let mut disk_file = std::fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open("disk_metrics.csv")
            .context("Failed to open disk metrics file")?;
            
        for disk in &report.disks {
            let used_percent = 100.0 - ((disk.available_space as f64 / disk.total_space as f64) * 100.0);
            writeln!(disk_file, "{},{},{}", timestamp, disk.mount_point, used_percent)
                .context("Failed to write disk metrics")?;
        }
            
        Ok(())
    }
}
```

With these metrics exported, you can use various visualization tools like Grafana, Prometheus, or even simple plotting libraries to create dashboards showing system health over time.

Here's an example of how to visualize this data using a simple HTML dashboard with Chart.js:

```html
<!DOCTYPE html>
<html>
<head>
    <title>CyberShield System Health Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chart-container { width: 100%; max-width: 800px; margin-bottom: 30px; }
        .metrics-card { 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 15px; 
            margin-bottom: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metrics-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
        .warning { color: #f44336; }
    </style>
</head>
<body>
    <h1>CyberShield System Health Dashboard</h1>
    
    <div class="metrics-card">
        <div class="metrics-title">CPU Usage</div>
        <div class="chart-container">
            <canvas id="cpuChart"></canvas>
        </div>
    </div>
    
    <div class="metrics-card">
        <div class="metrics-title">Memory Usage</div>
        <div class="chart-container">
            <canvas id="memoryChart"></canvas>
        </div>
    </div>
    
    <div class="metrics-card">
        <div class="metrics-title">Disk Usage</div>
        <div class="chart-container">
            <canvas id="diskChart"></canvas>
        </div>
    </div>
    
    <div class="metrics-card">
        <div class="metrics-title">Top Processes</div>
        <div id="processTable"></div>
    </div>
    
    <script>
        // Fetch and parse CSV data
        async function fetchCSVData(url) {
            const response = await fetch(url);
            const csvText = await response.text();
            const lines = csvText.trim().split('\n');
            
            return lines.map(line => {
                const [timestamp, value] = line.split(',');
                return { timestamp, value: parseFloat(value) };
            });
        }
        
        // Create charts
        async function createCharts() {
            // CPU Chart
            const cpuData = await fetchCSVData('cpu_metrics.csv');
            new Chart(document.getElementById('cpuChart'), {
                type: 'line',
                data: {
                    labels: cpuData.map(row => {
                        const date = new Date(row.timestamp);
                        return date.toLocaleTimeString();
                    }),
                    datasets: [{
                        label: 'CPU Usage %',
                        data: cpuData.map(row => row.value),
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }]
                },
                options: {
                    scales: { y: { min: 0, max: 100 } }
                }
            });
            
            // Memory Chart
            const memoryData = await fetchCSVData('memory_metrics.csv');
            new Chart(document.getElementById('memoryChart'), {
                type: 'line',
                data: {
                    labels: memoryData.map(row => {
                        const date = new Date(row.timestamp);
                        return date.toLocaleTimeString();
                    }),
                    datasets: [{
                        label: 'Memory Usage %',
                        data: memoryData.map(row => row.value),
                        borderColor: 'rgb(153, 102, 255)',
                        tension: 0.1
                    }]
                },
                options: {
                    scales: { y: { min: 0, max: 100 } }
                }
            });
            
            // Disk Chart - more complex due to multiple disks
            const diskData = await fetchCSVData('disk_metrics.csv');
            
            // Group by mount point
            const disksByMount = {};
            diskData.forEach(row => {
                const [timestamp, mount, value] = [row.timestamp, row.mount, row.value];
                if (!disksByMount[mount]) {
                    disksByMount[mount] = { 
                        timestamps: [], 
                        values: [] 
                    };
                }
                disksByMount[mount].timestamps.push(timestamp);
                disksByMount[mount].values.push(value);
            });
            
            // Create datasets
            const diskDatasets = Object.entries(disksByMount).map(([mount, data], index) => {
                // Generate a color based on index
                const hue = (index * 137) % 360;
                return {
                    label: `${mount} Usage %`,
                    data: data.values,
                    borderColor: `hsl(${hue}, 70%, 60%)`,
                    tension: 0.1
                };
            });
            
            // Create chart with all disks
            new Chart(document.getElementById('diskChart'), {
                type: 'line',
                data: {
                    labels: disksByMount[Object.keys(disksByMount)[0]]?.timestamps.map(ts => {
                        const date = new Date(ts);
                        return date.toLocaleTimeString();
                    }) || [],
                    datasets: diskDatasets
                },
                options: {
                    scales: { y: { min: 0, max: 100 } }
                }
            });
        }
        
        // Initialize
        createCharts();
        
        // Refresh every minute
        setInterval(createCharts, 60000);
    </script>
</body>
</html>
```

## Next Steps

Now that we've implemented a comprehensive system health reporting module, we can:

1. Integrate it with our threat detection system to correlate system health anomalies with potential security incidents
2. Extend the visualization capabilities to create a full dashboard
3. Implement historical data analysis for trend detection and prediction
4. Add machine learning capabilities to detect anomalous system behavior automatically

In the next section, we'll implement testing and benchmarking for our endpoint protection solution.

[Previous: Threat Detection](05-threat-detection.md) | [Next: Testing and Benchmarking](07-testing-and-benchmarking.md)
