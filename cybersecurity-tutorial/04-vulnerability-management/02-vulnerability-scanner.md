# Building a Vulnerability Scanner

A vulnerability scanner is a tool that automatically identifies security weaknesses in systems, applications, and networks. In this section, we'll build a basic vulnerability scanner in Rust.

## Overview

We'll cover:

1. Vulnerability scanning concepts
2. Designing a plugin-based scanner architecture
3. Implementing basic vulnerability checks in Rust
4. Scanning for common software and configuration issues
5. Reporting scan results

## Vulnerability Scanning Concepts

- **Active scanning**: Probes systems for weaknesses (e.g., open ports, outdated software)
- **Passive scanning**: Observes network traffic for signs of vulnerabilities
- **Authenticated vs. unauthenticated scans**

## Plugin-Based Scanner Architecture

A modular scanner allows for easy addition of new checks. Example Rust trait:

```rust
pub trait VulnerabilityCheck {
    fn name(&self) -> &str;
    fn run(&self, target: &str) -> ScanResult;
}

pub struct ScanResult {
    pub check_name: String,
    pub target: String,
    pub vulnerable: bool,
    pub details: String,
}
```

## Implementing Basic Vulnerability Checks

Example: Check for open ports

```rust
use std::net::TcpStream;

pub struct OpenPortCheck {
    pub port: u16,
}

impl VulnerabilityCheck for OpenPortCheck {
    fn name(&self) -> &str { "Open Port Check" }
    fn run(&self, target: &str) -> ScanResult {
        let addr = format!("{}:{}", target, self.port);
        let result = TcpStream::connect(addr);
        ScanResult {
            check_name: self.name().to_string(),
            target: target.to_string(),
            vulnerable: result.is_ok(),
            details: if result.is_ok() {
                format!("Port {} is open", self.port)
            } else {
                format!("Port {} is closed", self.port)
            },
        }
    }
}
```

## Scanning for Software and Configuration Issues

You can implement additional checks for:
- Outdated software versions
- Default credentials
- Weak SSL/TLS configurations
- Missing security headers

## Reporting Scan Results

Example function to print results:

```rust
pub fn print_scan_results(results: &[ScanResult]) {
    for result in results {
        println!("[{}] {}: {} - {}", 
            if result.vulnerable { "VULNERABLE" } else { "SAFE" },
            result.target, result.check_name, result.details);
    }
}
```

## Example Usage

```rust
fn main() {
    let checks: Vec<Box<dyn VulnerabilityCheck>> = vec![
        Box::new(OpenPortCheck { port: 22 }),
        Box::new(OpenPortCheck { port: 80 }),
    ];
    let target = "192.168.1.10";
    let mut results = Vec::new();
    for check in &checks {
        results.push(check.run(target));
    }
    print_scan_results(&results);
}
```

## Conclusion

In this section, we've built a basic, extensible vulnerability scanner in Rust. In the next section, we'll integrate public vulnerability databases for more advanced scanning.

---

🔗 **Previous**: [01-vulnerability-management-concepts.md](./01-vulnerability-management-concepts.md)

🔗 **Next**: [03-vulnerability-databases.md](./03-vulnerability-databases.md)
