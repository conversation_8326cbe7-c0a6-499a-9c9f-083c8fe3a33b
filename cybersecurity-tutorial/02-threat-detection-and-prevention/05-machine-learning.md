# Machine Learning Integration

Machine learning (ML) offers powerful capabilities for threat detection by identifying patterns and anomalies that would be difficult to catch with traditional rule-based approaches. In this section, we'll integrate machine learning techniques into our threat detection system.

## Overview

We'll cover:

1. Collecting and preprocessing security data
2. Training anomaly detection models
3. Real-time inference for threat detection
4. Model update strategies

## Prerequisites

For this section, you'll need to install these additional Rust crates:

```toml
[dependencies]
# Previous dependencies
# ...

# Machine learning dependencies
smartcore = "0.3.2"      # ML algorithms in pure Rust
ndarray = "0.15.6"       # N-dimensional arrays
serde_json = "1.0"       # JSON serialization
```

## Data Collection and Feature Engineering

Before applying machine learning, we need to collect relevant data and convert it into features our models can use.

```rust
use std::collections::HashMap;
use ndarray::{Array1, Array2};
use std::time::{Duration, Instant};
use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SecurityEvent {
    event_type: String,
    timestamp: u64,
    source: String,
    details: HashMap<String, String>,
    severity: u8,
}

pub struct FeatureExtractor {
    feature_names: Vec<String>,
}

impl FeatureExtractor {
    pub fn new() -> Self {
        // Define the features we'll extract
        let feature_names = vec![
            "process_cpu_usage".to_string(),
            "memory_usage_mb".to_string(),
            "network_connections_count".to_string(),
            "file_ops_per_minute".to_string(),
            "registry_changes_count".to_string(),
            "persistence_indicators".to_string(),
            "privilege_escalation_attempts".to_string(),
            "unusual_file_access_count".to_string(),
        ];
        
        Self { feature_names }
    }
    
    pub fn extract_features(&self, events: &[SecurityEvent], window_size: Duration) -> Array1<f64> {
        let mut features = Array1::zeros(self.feature_names.len());
        
        // Group events by type for easier processing
        let mut events_by_type: HashMap<String, Vec<&SecurityEvent>> = HashMap::new();
        for event in events {
            events_by_type.entry(event.event_type.clone())
                .or_insert_with(Vec::new)
                .push(event);
        }
        
        // Extract each feature
        if let Some(process_events) = events_by_type.get("process") {
            features[0] = Self::calculate_avg_cpu_usage(process_events);
            features[1] = Self::calculate_avg_memory_usage(process_events);
        }
        
        if let Some(network_events) = events_by_type.get("network") {
            features[2] = network_events.len() as f64;
        }
        
        if let Some(file_events) = events_by_type.get("filesystem") {
            features[3] = Self::calculate_file_ops_rate(file_events, window_size);
            features[7] = Self::count_unusual_file_accesses(file_events);
        }
        
        if let Some(registry_events) = events_by_type.get("registry") {
            features[4] = registry_events.len() as f64;
            features[5] = Self::detect_persistence_indicators(registry_events);
        }
        
        if let Some(auth_events) = events_by_type.get("authentication") {
            features[6] = Self::count_privilege_escalation(auth_events);
        }
        
        features
    }
    
    // Helper methods to calculate specific features
    fn calculate_avg_cpu_usage(events: &[&SecurityEvent]) -> f64 {
        // Extract CPU usage from events and calculate average
        let cpu_usages: Vec<f64> = events.iter()
            .filter_map(|e| e.details.get("cpu_usage").and_then(|s| s.parse::<f64>().ok()))
            .collect();
            
        if cpu_usages.is_empty() {
            return 0.0;
        }
        
        cpu_usages.iter().sum::<f64>() / cpu_usages.len() as f64
    }
    
    fn calculate_avg_memory_usage(events: &[&SecurityEvent]) -> f64 {
        // Similar to CPU usage but for memory
        let memory_usages: Vec<f64> = events.iter()
            .filter_map(|e| e.details.get("memory_mb").and_then(|s| s.parse::<f64>().ok()))
            .collect();
            
        if memory_usages.is_empty() {
            return 0.0;
        }
        
        memory_usages.iter().sum::<f64>() / memory_usages.len() as f64
    }
    
    fn calculate_file_ops_rate(events: &[&SecurityEvent], window: Duration) -> f64 {
        let count = events.len() as f64;
        let window_minutes = window.as_secs_f64() / 60.0;
        count / window_minutes
    }
    
    fn count_unusual_file_accesses(events: &[&SecurityEvent]) -> f64 {
        events.iter()
            .filter(|e| {
                // Check for access to sensitive directories
                if let Some(path) = e.details.get("path") {
                    path.contains("/etc/") || 
                    path.contains("/bin/") ||
                    path.contains("System32") ||
                    path.contains("WindowsApps")
                } else {
                    false
                }
            })
            .count() as f64
    }
    
    fn detect_persistence_indicators(events: &[&SecurityEvent]) -> f64 {
        // Count registry modifications that suggest persistence mechanisms
        events.iter()
            .filter(|e| {
                if let Some(key) = e.details.get("key") {
                    key.contains("Run") || 
                    key.contains("RunOnce") ||
                    key.contains("Startup") ||
                    key.contains("BootExecute")
                } else {
                    false
                }
            })
            .count() as f64
    }
    
    fn count_privilege_escalation(events: &[&SecurityEvent]) -> f64 {
        events.iter()
            .filter(|e| {
                e.details.get("action").map_or(false, |a| a == "escalate") ||
                e.details.get("privileges").map_or(false, |p| p.contains("admin") || p.contains("root"))
            })
            .count() as f64
    }
}
```

## Anomaly Detection with an Isolation Forest

We'll implement an anomaly detection algorithm using the SmartCore library to identify unusual patterns:

```rust
use smartcore::ensemble::isolation_forest::IsolationForest;
use smartcore::linalg::naive::dense_matrix::DenseMatrix;
use ndarray::{Array2, ArrayView1};
use std::fs::File;
use std::io::{Read, Write};

pub struct AnomalyDetector {
    model: Option<IsolationForest<f64, DenseMatrix<f64>>>,
    contamination: f64,
    feature_extractor: FeatureExtractor,
}

impl AnomalyDetector {
    pub fn new(contamination: f64) -> Self {
        Self {
            model: None,
            contamination,
            feature_extractor: FeatureExtractor::new(),
        }
    }
    
    pub fn train(&mut self, training_data: &Array2<f64>) -> Result<(), String> {
        if training_data.nrows() < 10 {
            return Err("Insufficient training data. Need at least 10 samples.".to_string());
        }
        
        // Convert ndarray to DenseMatrix for SmartCore
        let x = DenseMatrix::from_array(
            training_data.nrows(), 
            training_data.ncols(), 
            &training_data.iter().cloned().collect::<Vec<f64>>()
        );
        
        // Train the model
        self.model = Some(IsolationForest::fit(
            &x, 
            IsolationForest::default()
                .with_n_trees(100)
                .with_max_depth(100)
                .with_contamination(self.contamination)
        ).map_err(|e| format!("Training error: {:?}", e))?);
        
        Ok(())
    }
    
    pub fn predict(&self, features: &ArrayView1<f64>) -> Result<bool, String> {
        let model = match &self.model {
            Some(m) => m,
            None => return Err("Model not trained".to_string()),
        };
        
        // Convert feature vector to DenseMatrix
        let x = DenseMatrix::from_array(1, features.len(), &features.iter().cloned().collect::<Vec<f64>>());
        
        // Predict anomaly (-1 is anomaly, 1 is normal)
        let prediction = model.predict(&x).map_err(|e| format!("Prediction error: {:?}", e))?;
        
        // Return true if anomaly detected
        Ok(prediction[0] == -1)
    }
    
    pub fn analyze_events(&self, events: &[SecurityEvent], window_size: Duration) -> Result<bool, String> {
        let features = self.feature_extractor.extract_features(events, window_size);
        self.predict(&features.view())
    }
    
    pub fn save_model(&self, path: &str) -> Result<(), String> {
        let model = match &self.model {
            Some(m) => m,
            None => return Err("No model to save".to_string()),
        };
        
        // Serialize model (would need proper implementation)
        let serialized = serde_json::to_string(model)
            .map_err(|e| format!("Serialization error: {:?}", e))?;
        
        let mut file = File::create(path)
            .map_err(|e| format!("File creation error: {:?}", e))?;
        file.write_all(serialized.as_bytes())
            .map_err(|e| format!("File write error: {:?}", e))?;
        
        Ok(())
    }
    
    pub fn load_model(&mut self, path: &str) -> Result<(), String> {
        let mut file = File::open(path)
            .map_err(|e| format!("File open error: {:?}", e))?;
        
        let mut content = String::new();
        file.read_to_string(&mut content)
            .map_err(|e| format!("File read error: {:?}", e))?;
        
        // Deserialize model (would need proper implementation)
        self.model = Some(serde_json::from_str(&content)
            .map_err(|e| format!("Deserialization error: {:?}", e))?);
        
        Ok(())
    }
}
```

## Feature Selection and Training Process

For effective ML-based detection, we need a robust training pipeline:

```rust
pub struct ModelTrainer {
    training_data: Vec<Array1<f64>>,
    labels: Vec<bool>,  // true for anomalous, false for normal
    feature_extractor: FeatureExtractor,
}

impl ModelTrainer {
    pub fn new() -> Self {
        Self {
            training_data: Vec::new(),
            labels: Vec::new(),
            feature_extractor: FeatureExtractor::new(),
        }
    }
    
    pub fn add_training_data(&mut self, events: &[SecurityEvent], window_size: Duration, is_anomaly: bool) {
        let features = self.feature_extractor.extract_features(events, window_size);
        self.training_data.push(features);
        self.labels.push(is_anomaly);
    }
    
    pub fn train_model(&self) -> Result<AnomalyDetector, String> {
        if self.training_data.is_empty() {
            return Err("No training data available".to_string());
        }
        
        // Calculate contamination (proportion of anomalies)
        let anomaly_count = self.labels.iter().filter(|&&l| l).count();
        let contamination = anomaly_count as f64 / self.labels.len() as f64;
        
        // Convert training data to Array2
        let nrows = self.training_data.len();
        let ncols = self.training_data[0].len();
        let mut data = Array2::zeros((nrows, ncols));
        
        for (i, features) in self.training_data.iter().enumerate() {
            data.row_mut(i).assign(features);
        }
        
        // Create and train detector
        let mut detector = AnomalyDetector::new(contamination);
        detector.train(&data)?;
        
        Ok(detector)
    }
}
```

## Integration with Threat Detection System

Now, we'll integrate our ML-based detection into the main threat detection system:

```rust
use std::sync::{Arc, Mutex};
use std::collections::VecDeque;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use std::thread;

pub struct MLBasedThreatDetector {
    detector: Arc<Mutex<AnomalyDetector>>,
    event_window: Arc<Mutex<VecDeque<SecurityEvent>>>,
    window_duration: Duration,
    analysis_interval: Duration,
    alert_callback: Box<dyn Fn(String) + Send + 'static>,
}

impl MLBasedThreatDetector {
    pub fn new(
        detector: AnomalyDetector,
        window_duration: Duration,
        analysis_interval: Duration,
        alert_callback: impl Fn(String) + Send + 'static,
    ) -> Self {
        Self {
            detector: Arc::new(Mutex::new(detector)),
            event_window: Arc::new(Mutex::new(VecDeque::new())),
            window_duration,
            analysis_interval,
            alert_callback: Box::new(alert_callback),
        }
    }
    
    pub fn add_event(&self, event: SecurityEvent) {
        let mut window = self.event_window.lock().unwrap();
        
        // Add new event
        window.push_back(event);
        
        // Remove old events outside the time window
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
            
        let window_start = current_time - self.window_duration.as_secs();
        
        // Remove events older than our window
        while let Some(front) = window.front() {
            if front.timestamp < window_start {
                window.pop_front();
            } else {
                break;
            }
        }
    }
    
    pub fn start_analysis_thread(&self) -> thread::JoinHandle<()> {
        let detector = self.detector.clone();
        let event_window = self.event_window.clone();
        let window_duration = self.window_duration;
        let analysis_interval = self.analysis_interval;
        let alert_callback = self.alert_callback.clone();
        
        thread::spawn(move || {
            loop {
                thread::sleep(analysis_interval);
                
                // Get current set of events
                let events = {
                    let window = event_window.lock().unwrap();
                    window.iter().cloned().collect::<Vec<_>>()
                };
                
                if events.is_empty() {
                    continue;
                }
                
                // Analyze events
                let detector = detector.lock().unwrap();
                match detector.analyze_events(&events, window_duration) {
                    Ok(true) => {
                        // Anomaly detected
                        let alert = format!(
                            "ML-based anomaly detected at {}! Analyzed {} events.",
                            SystemTime::now()
                                .duration_since(UNIX_EPOCH)
                                .unwrap()
                                .as_secs(),
                            events.len()
                        );
                        (alert_callback)(alert);
                    },
                    Ok(false) => {
                        // Normal behavior, no action needed
                    },
                    Err(e) => {
                        eprintln!("Error analyzing events: {}", e);
                    }
                }
            }
        })
    }
}
```

## Creating a Training Dataset

To make our anomaly detection effective, we need representative data:

```rust
fn generate_training_dataset() -> Result<(Vec<SecurityEvent>, Vec<SecurityEvent>), String> {
    // In a real implementation, this would load from real data sources
    // Here we generate synthetic data for demonstration
    
    let mut normal_events = Vec::new();
    let mut anomalous_events = Vec::new();
    
    // Generate normal events
    for i in 0..1000 {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() - (i as u64 * 60);  // Events from the past
            
        // Process events with normal resource usage
        let mut details = HashMap::new();
        details.insert("cpu_usage".to_string(), (5.0 + (rand::random::<f64>() * 10.0)).to_string());
        details.insert("memory_mb".to_string(), (100.0 + (rand::random::<f64>() * 200.0)).to_string());
        
        normal_events.push(SecurityEvent {
            event_type: "process".to_string(),
            timestamp,
            source: "system_monitor".to_string(),
            details,
            severity: 1,
        });
        
        // Add some normal file access events
        let mut file_details = HashMap::new();
        file_details.insert("path".to_string(), format!("/home/<USER>/documents/file_{}.txt", i % 100));
        file_details.insert("operation".to_string(), "read".to_string());
        
        normal_events.push(SecurityEvent {
            event_type: "filesystem".to_string(),
            timestamp,
            source: "file_monitor".to_string(),
            details: file_details,
            severity: 1,
        });
        
        // Add some normal network connections
        if i % 5 == 0 {  // Fewer network events
            let mut network_details = HashMap::new();
            network_details.insert("destination".to_string(), format!("192.168.1.{}", i % 255));
            network_details.insert("port".to_string(), format!("{}", 80 + (i % 20)));
            
            normal_events.push(SecurityEvent {
                event_type: "network".to_string(),
                timestamp,
                source: "network_monitor".to_string(),
                details: network_details,
                severity: 1,
            });
        }
    }
    
    // Generate anomalous events
    for i in 0..100 {  // Fewer anomalous events
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() - (i as u64 * 60);
            
        // High CPU/memory usage
        let mut details = HashMap::new();
        details.insert("cpu_usage".to_string(), (70.0 + (rand::random::<f64>() * 30.0)).to_string());
        details.insert("memory_mb".to_string(), (800.0 + (rand::random::<f64>() * 200.0)).to_string());
        
        anomalous_events.push(SecurityEvent {
            event_type: "process".to_string(),
            timestamp,
            source: "system_monitor".to_string(),
            details,
            severity: 3,
        });
        
        // Suspicious file access
        let mut file_details = HashMap::new();
        file_details.insert("path".to_string(), format!("/etc/passwd"));
        file_details.insert("operation".to_string(), "write".to_string());
        
        anomalous_events.push(SecurityEvent {
            event_type: "filesystem".to_string(),
            timestamp,
            source: "file_monitor".to_string(),
            details: file_details,
            severity: 4,
        });
        
        // Unusual network connections
        let mut network_details = HashMap::new();
        network_details.insert("destination".to_string(), format!("45.33.{}.{}", i % 255, (i + 100) % 255));
        network_details.insert("port".to_string(), format!("{}", 4444 + (i % 10)));
        
        anomalous_events.push(SecurityEvent {
            event_type: "network".to_string(),
            timestamp,
            source: "network_monitor".to_string(),
            details: network_details,
            severity: 4,
        });
        
        // Registry persistence
        if i % 10 == 0 {
            let mut reg_details = HashMap::new();
            reg_details.insert("key".to_string(), "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run".to_string());
            reg_details.insert("value".to_string(), "SuspiciousApp.exe".to_string());
            
            anomalous_events.push(SecurityEvent {
                event_type: "registry".to_string(),
                timestamp,
                source: "registry_monitor".to_string(),
                details: reg_details,
                severity: 5,
            });
        }
    }
    
    Ok((normal_events, anomalous_events))
}
```

## Model Update Strategy

ML models need to be periodically updated to remain effective:

```rust
pub struct ModelUpdateManager {
    detector: Arc<Mutex<AnomalyDetector>>,
    training_data_path: String,
    model_path: String,
    update_interval: Duration,
}

impl ModelUpdateManager {
    pub fn new(
        detector: Arc<Mutex<AnomalyDetector>>,
        training_data_path: String,
        model_path: String,
        update_interval: Duration,
    ) -> Self {
        Self {
            detector,
            training_data_path,
            model_path,
            update_interval,
        }
    }
    
    pub fn start_update_thread(&self) -> thread::JoinHandle<()> {
        let detector = self.detector.clone();
        let training_data_path = self.training_data_path.clone();
        let model_path = self.model_path.clone();
        let update_interval = self.update_interval;
        
        thread::spawn(move || {
            loop {
                thread::sleep(update_interval);
                
                println!("Updating ML model with new training data...");
                
                // Load training data
                // In a real implementation, this would load from real data sources
                match generate_training_dataset() {
                    Ok((normal_events, anomalous_events)) => {
                        // Create trainer
                        let mut trainer = ModelTrainer::new();
                        
                        // Add training data
                        for chunk in normal_events.chunks(50) {
                            trainer.add_training_data(
                                chunk, 
                                Duration::from_secs(3600), // 1 hour window
                                false
                            );
                        }
                        
                        for chunk in anomalous_events.chunks(10) {
                            trainer.add_training_data(
                                chunk,
                                Duration::from_secs(3600), // 1 hour window
                                true
                            );
                        }
                        
                        // Train new model
                        match trainer.train_model() {
                            Ok(new_detector) => {
                                // Save model
                                if let Err(e) = new_detector.save_model(&model_path) {
                                    eprintln!("Failed to save model: {}", e);
                                    continue;
                                }
                                
                                // Update the current detector
                                let mut detector_guard = detector.lock().unwrap();
                                *detector_guard = new_detector;
                                
                                println!("ML model updated successfully!");
                            },
                            Err(e) => {
                                eprintln!("Failed to train model: {}", e);
                            }
                        }
                    },
                    Err(e) => {
                        eprintln!("Failed to generate training data: {}", e);
                    }
                }
            }
        })
    }
}
```

## Bringing It All Together

Let's create a comprehensive example of using the ML-based threat detector:

```rust
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Initializing ML-based Threat Detection System");
    
    // Create a new anomaly detector
    let mut detector = AnomalyDetector::new(0.1); // 10% contamination
    
    // Generate initial training data
    let (normal_events, anomalous_events) = generate_training_dataset()?;
    
    // Create and train model
    let mut trainer = ModelTrainer::new();
    
    // Add normal data
    for chunk in normal_events.chunks(50) {
        trainer.add_training_data(
            chunk,
            Duration::from_secs(3600), // 1 hour window
            false
        );
    }
    
    // Add anomalous data (labeled as anomalies)
    for chunk in anomalous_events.chunks(10) {
        trainer.add_training_data(
            chunk,
            Duration::from_secs(3600), // 1 hour window
            true
        );
    }
    
    // Train model
    println!("Training initial model...");
    detector = trainer.train_model()?;
    println!("Model trained successfully!");
    
    // Create threat detector
    let ml_threat_detector = MLBasedThreatDetector::new(
        detector,
        Duration::from_secs(3600),      // 1-hour window
        Duration::from_secs(60),        // Analyze every minute
        |alert| println!("ALERT: {}", alert),
    );
    
    // Start analysis thread
    let _handle = ml_threat_detector.start_analysis_thread();
    
    // Simulate some events
    println!("Simulating normal system activity...");
    for event in normal_events.iter().take(100) {
        ml_threat_detector.add_event(event.clone());
        thread::sleep(Duration::from_millis(10));
    }
    
    println!("Simulating potential threat activity...");
    for event in anomalous_events.iter().take(20) {
        ml_threat_detector.add_event(event.clone());
        thread::sleep(Duration::from_millis(50));
    }
    
    // In a real application, this would run indefinitely
    thread::sleep(Duration::from_secs(120));
    println!("Demonstration completed");
    
    Ok(())
}
```

## Key Concepts and Best Practices

1. **Feature Selection**:
   - Choose features that effectively capture unusual behavior
   - Normalize features to ensure they have comparable scales
   - Consider both static and dynamic features

2. **Model Selection**:
   - Unsupervised learning works well for security since labeled attack data is scarce
   - Isolation Forest is efficient and effective for anomaly detection
   - Consider ensemble approaches for more robust detection

3. **False Positives Management**:
   - Tune detection thresholds based on your organization's risk tolerance
   - Implement confidence scores to prioritize alerts
   - Combine ML with other detection methods for verification

4. **Performance Considerations**:
   - Batch processing for historical analysis
   - Efficient feature extraction for real-time detection
   - Regular model updates to adapt to changing patterns

5. **Explainability**:
   - Provide context with detected anomalies
   - Track which features contributed most to the detection
   - Document model training and update history

## Conclusion

Machine learning integration enhances our threat detection capabilities by identifying subtle patterns and anomalies that traditional methods might miss. While no single approach is perfect, combining ML-based detection with signature and heuristic methods creates a comprehensive defense system.

In the next section, we'll learn how to manage the alerts generated by our various detection methods and implement automated response systems.

[Previous Section: Behavioral Analysis](04-behavioral-analysis.md) | [Next Section: Alert Management](06-alert-management.md)
