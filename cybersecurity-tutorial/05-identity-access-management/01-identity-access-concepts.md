# Identity and Access Management Concepts

Identity and Access Management (IAM) is a framework of policies and technologies for ensuring that the right individuals have the appropriate access to technology resources. In this section, we'll cover the foundational concepts of IAM and its importance in cybersecurity.

## Overview

We'll cover:

1. IAM fundamentals and terminology
2. Authentication vs. authorization
3. Access control models (RBAC, ABAC, MAC, DAC)
4. Identity lifecycle management
5. Integration with security operations

## IAM Fundamentals and Terminology

- **Identity**: A unique representation of a user, device, or process
- **Authentication**: Verifying the identity of a user or system
- **Authorization**: Granting or denying access to resources
- **Account provisioning**: Creating and managing user accounts
- **Single Sign-On (SSO)**: One set of credentials for multiple systems

## Authentication vs. Authorization

- **Authentication** answers: Who are you?
- **Authorization** answers: What are you allowed to do?

## Access Control Models

- **RBAC (Role-Based Access Control)**: Access based on user roles
- **ABAC (Attribute-Based Access Control)**: Access based on attributes (user, resource, environment)
- **MAC (Mandatory Access Control)**: Central authority defines access
- **DAC (Discretionary Access Control)**: Resource owners define access

Example Rust enum for access models:

```rust
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub enum AccessControlModel {
    RBAC,
    ABAC,
    MAC,
    DAC,
}
```

## Identity Lifecycle Management

- **Provisioning**: Creating accounts
- **Modification**: Updating roles/permissions
- **De-provisioning**: Disabling or deleting accounts
- **Audit**: Tracking changes and access

## Integration with Security Operations

- Feed IAM logs into SIEM for monitoring
- Automate account reviews and recertification
- Enforce least privilege and separation of duties

## Conclusion

Understanding IAM concepts is essential for securing access to systems and data. In the next section, we'll implement basic IAM features in Rust.

---

🔗 **Previous**: [00-overview.md](./00-overview.md)

🔗 **Next**: [02-iam-implementation.md](./02-iam-implementation.md)
