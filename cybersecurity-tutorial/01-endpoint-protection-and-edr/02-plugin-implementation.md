# Plugin Implementation

In this section, we'll implement the structure of our Endpoint Protection and EDR plugin. We'll integrate it with our core application's plugin system and establish the foundation for the specific monitoring components we'll build in subsequent sections.

## Setting Up the Plugin Structure

First, let's create a new directory for our endpoint protection plugin:

```bash
mkdir -p src/plugins/endpoint_protection
```

### Updating Our Project Dependencies

Before implementing the plugin, let's update our `Cargo.toml` file to include the dependencies we'll need:

```toml
[package]
name = "cybershield"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A modular cybersecurity software with plugin support"

[dependencies]
# Core dependencies (from previous section)
log = "0.4"
env_logger = "0.10"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.7"
clap = { version = "4.3", features = ["derive"] }
anyhow = "1.0"

# New dependencies for endpoint protection
notify = "5.1"         # File system monitoring
sysinfo = "0.29"       # System and process information
sha2 = "0.10"          # Cryptographic hashing
crossbeam-channel = "0.5"  # Multi-producer, multi-consumer channels
dashmap = "5.4"        # Thread-safe HashMap
chrono = "0.4"         # Date and time utilities
rayon = "1.7"          # Data parallelism library
regex = "1.8"          # Regular expressions

[dev-dependencies]
tempfile = "3.8"
assert_cmd = "2.0"
```

## Creating the Plugin Interface Implementation

Now, let's implement the `Plugin` trait for our endpoint protection plugin:

**src/plugins/endpoint_protection/mod.rs**:

```rust
//! Endpoint Protection and EDR plugin for CyberShield

mod file_monitor;
mod process_monitor;
mod threat_detection;
mod system_health;
mod config;

use anyhow::{Context, Result};
use log::{debug, info, warn};
use std::path::PathBuf;
use std::sync::{Arc, RwLock};

use crate::plugins::interface::Plugin;
use self::config::EndpointProtectionConfig;
use self::file_monitor::FileMonitor;
use self::process_monitor::ProcessMonitor;
use self::system_health::SystemHealthMonitor;
use self::threat_detection::ThreatDetector;

/// Endpoint Protection plugin for CyberShield
#[derive(Debug)]
pub struct EndpointProtectionPlugin {
    name: String,
    version: String,
    description: String,
    config: Arc<RwLock<EndpointProtectionConfig>>,
    file_monitor: Option<Arc<FileMonitor>>,
    process_monitor: Option<Arc<ProcessMonitor>>,
    threat_detector: Option<Arc<ThreatDetector>>,
    system_health_monitor: Option<Arc<SystemHealthMonitor>>,
}

impl EndpointProtectionPlugin {
    /// Creates a new instance of the Endpoint Protection plugin
    pub fn new(config_path: Option<PathBuf>) -> Result<Self> {
        // Load configuration
        let config = match config_path {
            Some(path) => EndpointProtectionConfig::load_from_file(&path)
                .with_context(|| format!("Failed to load config from {}", path.display()))?,
            None => EndpointProtectionConfig::default(),
        };

        Ok(Self {
            name: "Endpoint Protection".to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            description: "Monitors and protects endpoints from threats".to_string(),
            config: Arc::new(RwLock::new(config)),
            file_monitor: None,
            process_monitor: None,
            threat_detector: None,
            system_health_monitor: None,
        })
    }

    /// Stops all monitoring components
    pub fn stop_all(&mut self) -> Result<()> {
        info!("Stopping all endpoint protection components");
        
        // Stop file monitoring
        if let Some(monitor) = &self.file_monitor {
            monitor.stop()?;
        }
        
        // Stop process monitoring
        if let Some(monitor) = &self.process_monitor {
            monitor.stop()?;
        }
        
        // Stop threat detection
        if let Some(detector) = &self.threat_detector {
            detector.stop()?;
        }
        
        // Stop system health monitoring
        if let Some(monitor) = &self.system_health_monitor {
            monitor.stop()?;
        }
        
        Ok(())
    }
}

impl Plugin for EndpointProtectionPlugin {
    fn name(&self) -> &str {
        &self.name
    }
    
    fn version(&self) -> &str {
        &self.version
    }
    
    fn description(&self) -> &str {
        &self.description
    }
    
    fn initialize(&self) -> Result<()> {
        info!("Initializing Endpoint Protection plugin");
        
        // Access configuration in a thread-safe way
        let config = match self.config.read() {
            Ok(config) => config,
            Err(e) => {
                warn!("Failed to read configuration: {}", e);
                return Err(anyhow::anyhow!("Failed to read configuration"));
            }
        };
        
        // Initialize file monitoring if enabled
        if config.file_monitoring.enabled {
            debug!("File monitoring is enabled");
            // We'll implement this in the next section
        }
        
        // Initialize process monitoring if enabled
        if config.process_monitoring.enabled {
            debug!("Process monitoring is enabled");
            // We'll implement this in a later section
        }
        
        // Initialize threat detection if enabled
        if config.threat_detection.enabled {
            debug!("Threat detection is enabled");
            // We'll implement this in a later section
        }
        
        // Initialize system health monitoring if enabled
        if config.system_health.enabled {
            debug!("System health monitoring is enabled");
            // We'll implement this in a later section
        }
        
        info!("Endpoint Protection plugin initialized successfully");
        Ok(())
    }
    
    fn shutdown(&self) -> Result<()> {
        info!("Shutting down Endpoint Protection plugin");
        
        // In a mutable context, we'd call stop_all() here
        // Since the trait method takes &self (immutable), we'll log this limitation
        warn!("Plugin trait shutdown method is immutable; cannot stop monitoring components");
        warn!("Use EndpointProtectionPlugin::stop_all() before dropping the plugin");
        
        Ok(())
    }
}
```

## Implementing the Configuration Module

Now, let's implement the configuration structure for our plugin:

**src/plugins/endpoint_protection/config.rs**:

```rust
//! Configuration for the Endpoint Protection plugin

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

/// Configuration for file system monitoring
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct FileMonitoringConfig {
    /// Whether file monitoring is enabled
    pub enabled: bool,
    /// Paths to monitor (empty means monitor the user's home directory)
    pub paths: Vec<String>,
    /// File extensions to monitor (empty means monitor all)
    pub extensions: Vec<String>,
    /// Maximum file size to hash (in bytes)
    pub max_file_size: u64,
    /// Whether to monitor file creates
    pub monitor_creates: bool,
    /// Whether to monitor file modifications
    pub monitor_modifies: bool,
    /// Whether to monitor file deletions
    pub monitor_deletes: bool,
    /// Whether to recursively monitor directories
    pub recursive: bool,
}

/// Configuration for process monitoring
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ProcessMonitoringConfig {
    /// Whether process monitoring is enabled
    pub enabled: bool,
    /// Interval between process checks (in milliseconds)
    pub check_interval_ms: u64,
    /// Names of suspicious processes to watch for
    pub suspicious_processes: Vec<String>,
    /// Whether to monitor new processes
    pub monitor_new_processes: bool,
    /// Whether to monitor process terminations
    pub monitor_terminated_processes: bool,
    /// Whether to monitor CPU usage
    pub monitor_cpu_usage: bool,
    /// CPU usage threshold (percentage) above which to alert
    pub cpu_threshold: f32,
    /// Whether to monitor memory usage
    pub monitor_memory_usage: bool,
    /// Memory usage threshold (percentage) above which to alert
    pub memory_threshold: f32,
}

/// Configuration for threat detection
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ThreatDetectionConfig {
    /// Whether threat detection is enabled
    pub enabled: bool,
    /// Suspicious file patterns (regex)
    pub suspicious_file_patterns: Vec<String>,
    /// Suspicious process patterns (regex)
    pub suspicious_process_patterns: Vec<String>,
    /// Known malicious file hashes
    pub malicious_file_hashes: Vec<String>,
}

/// Configuration for system health monitoring
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SystemHealthConfig {
    /// Whether system health monitoring is enabled
    pub enabled: bool,
    /// Interval between health checks (in milliseconds)
    pub check_interval_ms: u64,
    /// Whether to monitor CPU usage
    pub monitor_cpu: bool,
    /// Whether to monitor memory usage
    pub monitor_memory: bool,
    /// Whether to monitor disk usage
    pub monitor_disk: bool,
    /// Whether to monitor network usage
    pub monitor_network: bool,
}

/// Configuration for the Endpoint Protection plugin
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct EndpointProtectionConfig {
    /// File monitoring configuration
    pub file_monitoring: FileMonitoringConfig,
    /// Process monitoring configuration
    pub process_monitoring: ProcessMonitoringConfig,
    /// Threat detection configuration
    pub threat_detection: ThreatDetectionConfig,
    /// System health monitoring configuration
    pub system_health: SystemHealthConfig,
}

impl EndpointProtectionConfig {
    /// Creates a default configuration
    pub fn default() -> Self {
        Self {
            file_monitoring: FileMonitoringConfig {
                enabled: true,
                paths: vec![
                    dirs::home_dir()
                        .map(|p| p.to_string_lossy().to_string())
                        .unwrap_or_else(|| ".".to_string()),
                ],
                extensions: vec![
                    "exe".to_string(),
                    "dll".to_string(),
                    "bat".to_string(),
                    "sh".to_string(),
                    "ps1".to_string(),
                ],
                max_file_size: 10 * 1024 * 1024, // 10 MB
                monitor_creates: true,
                monitor_modifies: true,
                monitor_deletes: true,
                recursive: true,
            },
            process_monitoring: ProcessMonitoringConfig {
                enabled: true,
                check_interval_ms: 5000, // 5 seconds
                suspicious_processes: vec![
                    "cmd.exe".to_string(),
                    "powershell.exe".to_string(),
                    "wscript.exe".to_string(),
                    "cscript.exe".to_string(),
                ],
                monitor_new_processes: true,
                monitor_terminated_processes: true,
                monitor_cpu_usage: true,
                cpu_threshold: 90.0,
                monitor_memory_usage: true,
                memory_threshold: 90.0,
            },
            threat_detection: ThreatDetectionConfig {
                enabled: true,
                suspicious_file_patterns: vec![
                    r".*\.vbs$".to_string(),
                    r".*\.tmp$".to_string(),
                ],
                suspicious_process_patterns: vec![
                    r"^.*\\Temp\\.*\.exe$".to_string(),
                ],
                malicious_file_hashes: Vec::new(),
            },
            system_health: SystemHealthConfig {
                enabled: true,
                check_interval_ms: 60000, // 60 seconds
                monitor_cpu: true,
                monitor_memory: true,
                monitor_disk: true,
                monitor_network: true,
            },
        }
    }
    
    /// Loads configuration from a file
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path = path.as_ref();
        
        if !path.exists() {
            let config = Self::default();
            let toml = toml::to_string_pretty(&config)?;
            fs::write(path, toml)?;
            return Ok(config);
        }
        
        let content = fs::read_to_string(path)?;
        let config: Self = toml::from_str(&content)?;
        
        Ok(config)
    }
    
    /// Saves configuration to a file
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let toml = toml::to_string_pretty(self)?;
        fs::write(path, toml)?;
        Ok(())
    }
}
```

Now that we've set up the basic structure of our plugin, let's create placeholder files for the components we'll implement in the following sections:

## Creating Placeholder Files for Components

**src/plugins/endpoint_protection/file_monitor.rs**:

```rust
//! File system monitoring for the Endpoint Protection plugin

use anyhow::Result;
use std::sync::Arc;
use std::path::PathBuf;

/// Monitors the file system for suspicious activity
#[derive(Debug)]
pub struct FileMonitor {
    // We'll implement this in the next section
}

impl FileMonitor {
    /// Creates a new file monitor
    pub fn new() -> Result<Self> {
        // We'll implement this in the next section
        unimplemented!("FileMonitor::new will be implemented in the next section")
    }
    
    /// Starts the file monitor
    pub fn start(&self) -> Result<()> {
        // We'll implement this in the next section
        unimplemented!("FileMonitor::start will be implemented in the next section")
    }
    
    /// Stops the file monitor
    pub fn stop(&self) -> Result<()> {
        // We'll implement this in the next section
        unimplemented!("FileMonitor::stop will be implemented in the next section")
    }
}
```

**src/plugins/endpoint_protection/process_monitor.rs**:

```rust
//! Process monitoring for the Endpoint Protection plugin

use anyhow::Result;

/// Monitors processes for suspicious activity
#[derive(Debug)]
pub struct ProcessMonitor {
    // We'll implement this in a later section
}

impl ProcessMonitor {
    /// Creates a new process monitor
    pub fn new() -> Result<Self> {
        // We'll implement this in a later section
        unimplemented!("ProcessMonitor::new will be implemented in a later section")
    }
    
    /// Starts the process monitor
    pub fn start(&self) -> Result<()> {
        // We'll implement this in a later section
        unimplemented!("ProcessMonitor::start will be implemented in a later section")
    }
    
    /// Stops the process monitor
    pub fn stop(&self) -> Result<()> {
        // We'll implement this in a later section
        unimplemented!("ProcessMonitor::stop will be implemented in a later section")
    }
}
```

**src/plugins/endpoint_protection/threat_detection.rs**:

```rust
//! Threat detection for the Endpoint Protection plugin

use anyhow::Result;

/// Detects threats based on file and process activities
#[derive(Debug)]
pub struct ThreatDetector {
    // We'll implement this in a later section
}

impl ThreatDetector {
    /// Creates a new threat detector
    pub fn new() -> Result<Self> {
        // We'll implement this in a later section
        unimplemented!("ThreatDetector::new will be implemented in a later section")
    }
    
    /// Starts the threat detector
    pub fn start(&self) -> Result<()> {
        // We'll implement this in a later section
        unimplemented!("ThreatDetector::start will be implemented in a later section")
    }
    
    /// Stops the threat detector
    pub fn stop(&self) -> Result<()> {
        // We'll implement this in a later section
        unimplemented!("ThreatDetector::stop will be implemented in a later section")
    }
}
```

**src/plugins/endpoint_protection/system_health.rs**:

```rust
//! System health monitoring for the Endpoint Protection plugin

use anyhow::Result;

/// Monitors system health metrics
#[derive(Debug)]
pub struct SystemHealthMonitor {
    // We'll implement this in a later section
}

impl SystemHealthMonitor {
    /// Creates a new system health monitor
    pub fn new() -> Result<Self> {
        // We'll implement this in a later section
        unimplemented!("SystemHealthMonitor::new will be implemented in a later section")
    }
    
    /// Starts the system health monitor
    pub fn start(&self) -> Result<()> {
        // We'll implement this in a later section
        unimplemented!("SystemHealthMonitor::start will be implemented in a later section")
    }
    
    /// Stops the system health monitor
    pub fn stop(&self) -> Result<()> {
        // We'll implement this in a later section
        unimplemented!("SystemHealthMonitor::stop will be implemented in a later section")
    }
}
```

## Update the Plugin Module

Finally, let's update our main `plugins/mod.rs` file to include the new endpoint protection plugin:

**src/plugins/mod.rs**:

```rust
pub mod interface;
pub mod endpoint_protection;
```

## Hands-On Lab: Implement a Simple Plugin

Follow the step-by-step instructions to create a basic plugin for endpoint monitoring. Try extending it with your own logic.

## Understanding Rust Concepts Used

In the code we've written so far, we've leveraged several important Rust concepts:

### 1. Modules and Visibility

We've organized our code into modules, using `mod` declarations to define the module structure and `pub` to control which items are accessible from outside their module.

### 2. Error Handling with anyhow

We've used the `anyhow` crate for ergonomic error handling:

- `Result<T>` for operations that can fail
- `Context` for adding context to errors
- `anyhow::anyhow!` for creating custom error messages

### 3. Thread-Safe Data Structures

We've used thread-safe data structures for sharing state:

- `Arc` (Atomic Reference Counting) for shared ownership of data
- `RwLock` for thread-safe read-write access to data

### 4. Traits and Implementations

We've implemented the `Plugin` trait for our `EndpointProtectionPlugin`, demonstrating Rust's trait-based polymorphism.

### 5. Structs and Methods

We've defined several structs (like `EndpointProtectionConfig` and `EndpointProtectionPlugin`) with associated methods.

## Design Decisions

### Plugin Architecture

We've designed our plugin with a modular structure, separating different concerns into distinct components:

1. **File monitoring**: For detecting suspicious file system activity
2. **Process monitoring**: For tracking and analyzing processes
3. **Threat detection**: For identifying potential threats
4. **System health**: For monitoring overall system health

This separation allows us to:

- Develop and test each component independently
- Enable or disable specific components based on configuration
- Add new components in the future without major refactoring

### Configuration System

We've implemented a configuration system that:

1. Loads from a TOML file if available
2. Creates default configuration if not available
3. Allows saving configuration changes back to a file
4. Uses strongly typed structures for type safety

This approach provides both flexibility and type safety.

### Thread Safety Considerations

We've designed our plugin to be thread-safe by:

1. Using `Arc` for shared ownership across threads
2. Using `RwLock` for concurrent read/write access to configuration
3. Planning for thread-safe communication between components

## Alternatives Considered

### Alternative 1: Monolithic Design

We could have implemented all functionality in a single module without separating concerns. This would be simpler initially but would make the code harder to maintain and extend.

### Alternative 2: Separate Plugins

We could have implemented each component (file monitoring, process monitoring, etc.) as a separate plugin. This would provide maximum modularity but would require more complex coordination between plugins.

### Alternative 3: Async Design

We could have used async/await for event handling instead of threads. This would potentially be more efficient but would add complexity and limit compatibility with some system APIs.

## Visual Aid

![Plugin Architecture Diagram](../images/plugin-architecture.png)

## Next Steps

Now that we have set up the structure of our Endpoint Protection plugin, in the next section we'll implement the first functional component: the File System Monitor.

## Navigation

- Previous: [Understanding Endpoint Protection](./01-understanding-endpoint-protection.md)
- Next: [File System Monitoring](./03-file-system-monitoring.md)
